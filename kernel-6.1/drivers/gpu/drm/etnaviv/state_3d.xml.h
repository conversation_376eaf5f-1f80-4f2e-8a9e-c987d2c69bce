/* SPDX-License-Identifier: GPL-2.0 */
#ifndef STATE_3D_XML
#define STATE_3D_XML

/* This is a cut-down version of the state_3d.xml.h file */

#define VIVS_TS_FLUSH_CACHE					0x00001650
#define VIVS_TS_FLUSH_CACHE_FLUSH				0x00000001

#define VIVS_NTE_DESCRIPTOR_FLUSH				0x00014c44
#define VIVS_NTE_DESCRIPTOR_FLUSH_UNK28__MASK			0xf0000000
#define VIVS_NTE_DESCRIPTOR_FLUSH_UNK28__SHIFT			28
#define VIVS_NTE_DESCRIPTOR_FLUSH_UNK28(x)			(((x) << VIVS_NTE_DESCRIPTOR_FLUSH_UNK28__SHIFT) & VIVS_NTE_DESCRIPTOR_FLUSH_UNK28__MASK)

#endif /* STATE_3D_XML */
