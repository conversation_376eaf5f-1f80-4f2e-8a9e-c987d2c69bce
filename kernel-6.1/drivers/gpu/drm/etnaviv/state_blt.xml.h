#ifndef STATE_BLT_XML
#define STATE_BLT_XML

/* Autogenerated file, DO NOT EDIT manually!

This file was generated by the rules-ng-ng headergen tool in this git repository:
http://0x04.net/cgit/index.cgi/rules-ng-ng
git clone git://0x04.net/rules-ng-ng

The rules-ng-ng source files this header was generated from are:
- state.xml     (  26087 bytes, from 2017-12-18 16:51:59)
- common.xml    (  35468 bytes, from 2018-01-22 13:48:54)
- common_3d.xml (  14615 bytes, from 2017-12-18 16:51:59)
- state_hi.xml  (  30232 bytes, from 2018-02-15 15:48:01)
- copyright.xml (   1597 bytes, from 2016-12-08 16:37:56)
- state_2d.xml  (  51552 bytes, from 2016-12-08 16:37:56)
- state_3d.xml  (  79992 bytes, from 2017-12-18 16:51:59)
- state_blt.xml (  13405 bytes, from 2017-12-18 16:51:59)
- state_vg.xml  (   5975 bytes, from 2016-12-08 16:37:56)

Copyright (C) 2012-2017 by the following authors:
- <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
- Christian Gmeiner <<EMAIL>>
- <PERSON> Stach <<EMAIL>>
- Russell King <<EMAIL>>

Permission is hereby granted, free of charge, to any person obtaining a
copy of this software and associated documentation files (the "Software"),
to deal in the Software without restriction, including without limitation
the rights to use, copy, modify, merge, publish, distribute, sub license,
and/or sell copies of the Software, and to permit persons to whom the
Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice (including the
next paragraph) shall be included in all copies or substantial portions
of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL
THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
DEALINGS IN THE SOFTWARE.
*/

/* This is a cut-down version of the state_blt.xml.h file */

#define VIVS_BLT_SET_COMMAND					0x000140ac

#define VIVS_BLT_ENABLE						0x000140b8
#define VIVS_BLT_ENABLE_ENABLE					0x00000001

#endif /* STATE_BLT_XML */
