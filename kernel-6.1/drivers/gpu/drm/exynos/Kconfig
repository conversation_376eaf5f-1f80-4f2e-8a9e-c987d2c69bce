# SPDX-License-Identifier: GPL-2.0-only
config DRM_EXYNOS
	tristate "DRM Support for Samsung SoC Exynos Series"
	depends on OF && DRM && COMMON_CLK
	depends on ARCH_S3C64XX || ARCH_S5PV210 || ARCH_EXYNOS || ARCH_MULTIPLATFORM || COMPILE_TEST
	depends on MMU
	select DRM_DISPLAY_HELPER if DRM_EXYNOS_DP
	select DRM_KMS_HELP<PERSON>
	select VIDEOMODE_HELPERS
	select SND_SOC_HDMI_CODEC if SND_SOC
	help
	  Choose this option if you have a Samsung SoC Exynos chipset.
	  If M is selected the module will be called exynosdrm.

if DRM_EXYNOS

comment "CRTCs"

config DRM_EXYNOS_FIMD
	bool "FIMD"
	depends on !FB_S3C
	select MFD_SYSCON
	help
	  Choose this option if you want to use Exynos FIMD for DRM.

config DRM_EXYNOS5433_DECON
	bool "DECON on Exynos5433"
	help
	  Choose this option if you want to use Exynos5433 DECON for DRM.

config DRM_EXYNOS7_DECON
	bool "DECON on Exynos7"
	depends on !FB_S3C
	help
	  Choose this option if you want to use Exynos DECON for DRM.

config DRM_EXYNOS_MIXER
	bool "Mixer"
	help
	  Choose this option if you want to use Exynos Mixer for DRM.

config DRM_EXYNOS_VIDI
	bool "Virtual Display"
	help
	  Choose this option if you want to use Exynos VIDI for DRM.

comment "Encoders and Bridges"

config DRM_EXYNOS_DPI
	bool "Parallel output"
	depends on DRM_EXYNOS_FIMD
	select DRM_PANEL
	default n
	help
	  This enables support for Exynos parallel output.

config DRM_EXYNOS_DSI
	bool "MIPI-DSI host"
	depends on DRM_EXYNOS_FIMD || DRM_EXYNOS5433_DECON || DRM_EXYNOS7_DECON
	select DRM_MIPI_DSI
	select DRM_PANEL
	default n
	help
	  This enables support for Exynos MIPI-DSI device.

config DRM_EXYNOS_DP
	bool "Exynos specific extensions for Analogix DP driver"
	depends on DRM_EXYNOS_FIMD || DRM_EXYNOS7_DECON
	select DRM_ANALOGIX_DP
	select DRM_DISPLAY_DP_HELPER
	default DRM_EXYNOS
	select DRM_PANEL
	help
	  This enables support for DP device.

config DRM_EXYNOS_HDMI
	bool "HDMI"
	depends on DRM_EXYNOS_MIXER || DRM_EXYNOS5433_DECON
	select CEC_CORE if CEC_NOTIFIER
	help
	  Choose this option if you want to use Exynos HDMI for DRM.

config DRM_EXYNOS_MIC
	bool "Mobile Image Compressor"
	depends on DRM_EXYNOS5433_DECON
	help
	  Choose this option if you want to use Exynos MIC for DRM.

comment "Sub-drivers"

config DRM_EXYNOS_G2D
	bool "G2D"
	depends on VIDEO_SAMSUNG_S5P_G2D=n || COMPILE_TEST
	help
	  Choose this option if you want to use Exynos G2D for DRM.

config DRM_EXYNOS_IPP
	bool

config DRM_EXYNOS_FIMC
	bool "FIMC"
	select DRM_EXYNOS_IPP
	help
	  Choose this option if you want to use Exynos FIMC for DRM.

config DRM_EXYNOS_ROTATOR
	bool "Rotator"
	select DRM_EXYNOS_IPP
	help
	  Choose this option if you want to use Exynos Rotator for DRM.

config DRM_EXYNOS_SCALER
	bool "Scaler"
	select DRM_EXYNOS_IPP
	help
	  Choose this option if you want to use Exynos Scaler for DRM.

config DRM_EXYNOS_GSC
	bool "GScaler"
	depends on VIDEO_SAMSUNG_EXYNOS_GSC=n || COMPILE_TEST
	select DRM_EXYNOS_IPP
	help
	  Choose this option if you want to use Exynos GSC for DRM.

endif
