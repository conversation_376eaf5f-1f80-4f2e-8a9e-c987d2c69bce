# SPDX-License-Identifier: GPL-2.0-only
config DRM_DW_HDMI
	tristate
	select DRM_DISPLAY_HDMI_HELPER
	select DRM_DISPLAY_HELPER
	select DRM_K<PERSON>_HELPER
	select REG<PERSON><PERSON>_<PERSON><PERSON>O
	select CEC_CORE if CE<PERSON>_NOTIFIER

config DRM_DW_HDMI_AHB_AUDIO
	tristate "Synopsys Designware AHB Audio interface"
	depends on DRM_DW_HDMI && SND
	select SND_PCM
	select SND_PCM_ELD
	select SND_PCM_IEC958
	help
	  Support the AHB Audio interface which is part of the Synopsys
	  Designware HDMI block.  This is used in conjunction with
	  the i.MX6 HDMI driver.

config DRM_DW_HDMI_I2S_AUDIO
	tristate "Synopsys Designware I2S Audio interface"
	depends on SND_SOC
	depends on DRM_DW_HDMI
	select SND_SOC_HDMI_CODEC
	help
	  Support the I2S Audio interface which is part of the Synopsys
	  Designware HDMI block.

config DRM_DW_HDMI_GP_AUDIO
	tristate "Synopsys Designware GP Audio interface"
	depends on DRM_DW_HDMI && <PERSON><PERSON>
	select <PERSON><PERSON>_<PERSON>M
	select S<PERSON>_<PERSON>M_ELD
	select SND_PCM_IEC958
	help
	  Support the GP Audio interface which is part of the Synopsys
	  Designware HDMI block.

config DRM_DW_HDMI_CEC
	tristate "Synopsis Designware CEC interface"
	depends on DRM_DW_HDMI
	select CEC_CORE
	select CEC_NOTIFIER
	help
	  Support the CE interface which is part of the Synopsys
	  Designware HDMI block.

config DRM_DW_MIPI_DSI
	tristate
	select DRM_KMS_HELPER
	select DRM_MIPI_DSI
	select DRM_PANEL_BRIDGE
