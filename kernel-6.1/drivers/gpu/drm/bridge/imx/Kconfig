if ARCH_MXC || COMPILE_TEST

config DRM_IMX8QM_LDB
	tristate "Freescale i.MX8QM LVDS display bridge"
	depends on OF
	depends on COMMON_CLK
	select DRM_KMS_HELPER
	help
	  Choose this to enable the internal LVDS Display Bridge(LDB) found in
	  Freescale i.MX8qm processor.  Official name of LDB is pixel mapper.

config DRM_IMX8QXP_LDB
	tristate "Freescale i.MX8QXP LVDS display bridge"
	depends on OF
	depends on COMMON_CLK
	select DRM_KMS_HELPER
	help
	  Choose this to enable the internal LVDS Display Bridge(LDB) found in
	  Freescale i.MX8qxp processor.  Official name of LDB is pixel mapper.

config DRM_IMX8QXP_PIXEL_COMBINER
	tristate "Freescale i.MX8QM/QXP pixel combiner"
	depends on OF
	depends on COMMON_CLK
	select DRM_KMS_HELPER
	help
	  Choose this to enable pixel combiner found in
	  Freescale i.MX8qm/qxp processors.

config DRM_IMX8QXP_PIXEL_LINK
	tristate "Freescale i.MX8QM/QXP display pixel link"
	depends on OF
	depends on IMX_SCU
	select DRM_KMS_HELPER
	help
	  Choose this to enable display pixel link found in
	  Freescale i.MX8qm/qxp processors.

config DRM_IMX8QXP_PIXEL_LINK_TO_DPI
	tristate "Freescale i.MX8QXP pixel link to display pixel interface"
	depends on OF
	select DRM_KMS_HELPER
	help
	  Choose this to enable pixel link to display pixel interface(PXL2DPI)
	  found in Freescale i.MX8qxp processor.

endif # ARCH_MXC || COMPILE_TEST
