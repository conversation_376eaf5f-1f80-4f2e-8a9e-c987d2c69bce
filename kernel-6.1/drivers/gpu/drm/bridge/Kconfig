# SPDX-License-Identifier: GPL-2.0-only
config DRM_BRIDGE
	def_bool y
	depends on DRM
	help
	  Bridge registration and lookup framework.

config DRM_PANEL_BRIDGE
	def_bool y
	depends on DRM_BRIDGE
	select DRM_PANEL
	help
	  DRM bridge wrapper of DRM panels

menu "Display Interface Bridges"
	depends on DRM && DRM_BRIDGE

config DRM_CDNS_DSI
	tristate "Cadence DPI/DSI bridge"
	select DRM_KMS_HELPER
	select DRM_MIPI_DSI
	select DRM_PANEL_BRIDGE
	select GENERIC_PHY_MIPI_DPHY
	depends on OF
	help
	  Support Cadence DPI to DSI bridge. This is an internal
	  bridge and is meant to be directly embedded in a SoC.

config DRM_CHIPONE_ICN6211
	tristate "Chipone ICN6211 MIPI-DSI/RGB Converter bridge"
	depends on OF
	select DRM_KMS_HELPER
	select DRM_MIPI_DSI
	select DRM_PANEL_BRIDGE
	select REGMAP_I2C
	help
	  ICN6211 is MIPI-DSI/RGB Converter bridge from chipone.

	  It has a flexible configuration of MIPI DSI signal input
	  and produce RGB565, RGB666, RGB888 output format.

	  If in doubt, say "N".

config DRM_CHRONTEL_CH7033
	tristate "Chrontel CH7033 Video Encoder"
	depends on OF
	select DRM_KMS_HELPER
	help
	  Enable support for the Chrontel CH7033 VGA/DVI/HDMI Encoder, as
	  found in the Dell Wyse 3020 thin client.

	  If in doubt, say "N".

config DRM_CROS_EC_ANX7688
	tristate "ChromeOS EC ANX7688 bridge"
	depends on OF
	depends on I2C_CROS_EC_TUNNEL || COMPILE_TEST
	select DRM_KMS_HELPER
	select REGMAP_I2C
	help
	  ChromeOS EC ANX7688 is an ultra-low power
	  4K Ultra-HD (4096x2160p60) mobile HD transmitter
	  designed for ChromeOS devices. It converts HDMI
	  2.0 to DisplayPort 1.3 Ultra-HD. It is connected
	  to the ChromeOS Embedded Controller.

config DRM_DISPLAY_CONNECTOR
	tristate "Display connector support"
	depends on OF
	help
	  Driver for display connectors with support for DDC and hot-plug
	  detection. Most display controllers handle display connectors
	  internally and don't need this driver, but the DRM subsystem is
	  moving towards separating connector handling from display controllers
	  on ARM-based platforms. Saying Y here when this driver is not needed
	  will not cause any issue.

config DRM_FSL_LDB
	tristate "Freescale i.MX8MP LDB bridge"
	depends on OF
	depends on ARCH_MXC || COMPILE_TEST
	select DRM_KMS_HELPER
	select DRM_PANEL_BRIDGE
	help
	  Support for i.MX8MP DPI-to-LVDS on-SoC encoder.

config DRM_ITE_IT6161
	tristate "ITE IT6161 DSI/HDMI bridge"
	select SND_SOC_HDMI_CODEC if SND_SOC
	depends on OF
	select DRM_PANEL_BRIDGE
	select DRM_KMS_HELPER
	select DRM_MIPI_DSI
	select REGMAP_I2C
	help
	  Driver for ITE IT6161 DSI to HDMI bridge
	  Please say Y if you have such hardware

config DRM_ITE_IT6505
        tristate "ITE IT6505 DisplayPort bridge"
        depends on OF
	select DRM_DISPLAY_DP_HELPER
	select DRM_DISPLAY_HDCP_HELPER
	select DRM_DISPLAY_HELPER
        select DRM_DP_AUX_BUS
        select DRM_KMS_HELPER
        select DRM_DP_HELPER
        select EXTCON
        select CRYPTO
        select CRYPTO_HASH
        help
          ITE IT6505 DisplayPort bridge chip driver.

config DRM_LONTIUM_LT8912B
	tristate "Lontium LT8912B DSI/HDMI bridge"
	depends on OF
	select DRM_PANEL_BRIDGE
	select DRM_KMS_HELPER
	select DRM_MIPI_DSI
	select REGMAP_I2C
	select VIDEOMODE_HELPERS
	help
	  Driver for Lontium LT8912B DSI to HDMI bridge
	  chip driver.
	  Please say Y if you have such hardware.

	  Say M here if you want to support this hardware as a module.
	  The module will be named "lontium-lt8912b".

config DRM_LONTIUM_LT9211
	tristate "Lontium LT9211 DSI/LVDS/DPI bridge"
	depends on OF
	select DRM_PANEL_BRIDGE
	select DRM_KMS_HELPER
	select DRM_MIPI_DSI
	select REGMAP_I2C
	help
	  Driver for Lontium LT9211 Single/Dual-Link DSI/LVDS or Single DPI
	  input to Single-link/Dual-Link DSI/LVDS or Single DPI output bridge
	  chip.
	  Please say Y if you have such hardware.

config DRM_LONTIUM_LT9611
	tristate "Lontium LT9611 DSI/HDMI bridge"
	select SND_SOC_HDMI_CODEC if SND_SOC
	depends on OF
	select DRM_PANEL_BRIDGE
	select DRM_KMS_HELPER
	select DRM_MIPI_DSI
	select REGMAP_I2C
	help
	  Driver for Lontium LT9611 DSI to HDMI bridge
	  chip driver that converts dual DSI and I2S to
	  HDMI signals
	  Please say Y if you have such hardware.

config DRM_LONTIUM_LT9611UXC
	tristate "Lontium LT9611UXC DSI/HDMI bridge"
	select SND_SOC_HDMI_CODEC if SND_SOC
	depends on OF
	select DRM_PANEL_BRIDGE
	select DRM_KMS_HELPER
	select DRM_MIPI_DSI
	select REGMAP_I2C
	help
	  Driver for Lontium LT9611UXC DSI to HDMI bridge
	  chip driver that converts dual DSI and I2S to
	  HDMI signals
	  Please say Y if you have such hardware.

config DRM_ITE_IT66121
	tristate "ITE IT66121 HDMI bridge"
	depends on OF
	select DRM_KMS_HELPER
	select REGMAP_I2C
	help
	  Support for ITE IT66121 HDMI bridge.

config DRM_LVDS_CODEC
	tristate "Transparent LVDS encoders and decoders support"
	depends on OF
	select DRM_KMS_HELPER
	select DRM_PANEL_BRIDGE
	help
	  Support for transparent LVDS encoders and decoders that don't
	  require any configuration.

config DRM_MAXIM_MAX96745
	tristate "Maxim max96745 GMSL2 Serializer"
	depends on OF
	select MFD_MAX96745
	select PINCTRL_MAX96745
	select DRM_KMS_HELPER
	select DRM_PANEL
	help
	  Driver for Maxim MAX96745 GMSL2 Serializer with eDP1.4a/DP1.4 Input.

config DRM_MAXIM_MAX96755F
	tristate "Maxim max96755 GMSL2 Serializer"
	depends on OF
	select MFD_MAX96755F
	select PINCTRL_MAX96755F
	select DRM_KMS_HELPER
	select DRM_PANEL
	help
	  Driver for Maxim MAX96755F GMSL2 Serializer with MIPI-DSI Input.

config DRM_MEGACHIPS_STDPXXXX_GE_B850V3_FW
	tristate "MegaChips stdp4028-ge-b850v3-fw and stdp2690-ge-b850v3-fw"
	depends on OF
	select DRM_KMS_HELPER
	select DRM_PANEL
	help
	  This is a driver for the display bridges of
	  GE B850v3 that convert dual channel LVDS
	  to DP++. This is used with the i.MX6 imx-ldb
	  driver. You are likely to say N here.

config DRM_NWL_MIPI_DSI
	tristate "Northwest Logic MIPI DSI Host controller"
	depends on DRM
	depends on COMMON_CLK
	depends on OF && HAS_IOMEM
	select DRM_KMS_HELPER
	select DRM_MIPI_DSI
	select DRM_PANEL_BRIDGE
	select GENERIC_PHY_MIPI_DPHY
	select MFD_SYSCON
	select MULTIPLEXER
	select REGMAP_MMIO
	help
	  This enables the Northwest Logic MIPI DSI Host controller as
	  for example found on NXP's i.MX8 Processors.

config DRM_NXP_PTN3460
	tristate "NXP PTN3460 DP/LVDS bridge"
	depends on OF
	select DRM_KMS_HELPER
	select DRM_PANEL
	help
	  NXP PTN3460 eDP-LVDS bridge chip driver.

config DRM_PARADE_PS8622
	tristate "Parade eDP/LVDS bridge"
	depends on OF
	select DRM_PANEL
	select DRM_KMS_HELPER
	select BACKLIGHT_CLASS_DEVICE
	help
	  Parade eDP-LVDS bridge chip driver.

config DRM_PARADE_PS8640
	tristate "Parade PS8640 MIPI DSI to eDP Converter"
	depends on OF
	select DRM_DISPLAY_DP_HELPER
	select DRM_DISPLAY_HELPER
	select DRM_DP_AUX_BUS
	select DRM_KMS_HELPER
	select DRM_MIPI_DSI
	select DRM_PANEL
	help
	  Choose this option if you have PS8640 for display
	  The PS8640 is a high-performance and low-power
	  MIPI DSI to eDP converter

config DRM_RK630_TVE
	tristate "ROCKCHIP RK630 TVE bridge"
	depends on OF
	depends on MFD_RK630
	select DRM_KMS_HELPER
	help
	  ROCKCHIP TVE bridge chip RK630 driver.

config DRM_RK1000_TVE
	tristate "Rockchip RK1000 TVE bridge"
	depends on OF
	select DRM_KMS_HELPER
	select MFD_RK1000
	help
	  Rockchip TVE bridge chip driver.

config DRM_ROHM_BU18XL82
	tristate "Rohm BU18TL82/BU18RL82 Clockless Link-BD Serializer/Deserializer bridge"
	depends on OF
	select DRM_PANEL_BRIDGE
	select DRM_KMS_HELPER
	select DRM_MIPI_DSI
	select REGMAP_I2C
	help
	    Rohm BU18TL82/BU18RL82 Clockless Link-BD Serializer/Deserializer bridge chip driver.

config DRM_SIL_SII8620
	tristate "Silicon Image SII8620 HDMI/MHL bridge"
	depends on OF
	select DRM_KMS_HELPER
	select EXTCON
	depends on RC_CORE || !RC_CORE
	help
	  Silicon Image SII8620 HDMI/MHL bridge chip driver.

config DRM_SII902X
	tristate "Silicon Image sii902x RGB/HDMI bridge"
	depends on OF
	select DRM_KMS_HELPER
	select REGMAP_I2C
	select I2C_MUX
	select SND_SOC_HDMI_CODEC if (SND_SOC && !ROCKCHIP_MINI_KERNEL)
	select VIDEOMODE_HELPERS
	select HDMI if ROCKCHIP_MINI_KERNEL
	help
	  Silicon Image sii902x bridge chip driver.

config DRM_SII9234
	tristate "Silicon Image SII9234 HDMI/MHL bridge"
	depends on OF
	help
	  Say Y here if you want support for the MHL interface.
	  It is an I2C driver, that detects connection of MHL bridge
	  and starts encapsulation of HDMI signal.

config DRM_SIMPLE_BRIDGE
	tristate "Simple DRM bridge support"
	depends on OF
	select DRM_KMS_HELPER
	help
	  Support for non-programmable DRM bridges, such as ADI ADV7123, TI
	  THS8134 and THS8135 or passive resistor ladder DACs.

config DRM_THINE_THC63LVD1024
	tristate "Thine THC63LVD1024 LVDS decoder bridge"
	depends on OF
	help
	  Thine THC63LVD1024 LVDS/parallel converter driver.

config DRM_TOSHIBA_TC358762
	tristate "TC358762 DSI/DPI bridge"
	depends on OF
	select DRM_MIPI_DSI
	select DRM_KMS_HELPER
	select DRM_PANEL_BRIDGE
	help
	  Toshiba TC358762 DSI/DPI bridge driver.

config DRM_TOSHIBA_TC358764
	tristate "TC358764 DSI/LVDS bridge"
	depends on OF
	select DRM_MIPI_DSI
	select DRM_KMS_HELPER
	select DRM_PANEL
	help
	  Toshiba TC358764 DSI/LVDS bridge driver.

config DRM_TOSHIBA_TC358767
	tristate "Toshiba TC358767 eDP bridge"
	depends on OF
	select DRM_DISPLAY_DP_HELPER
	select DRM_DISPLAY_HELPER
	select DRM_KMS_HELPER
	select REGMAP_I2C
	select DRM_MIPI_DSI
	select DRM_PANEL
	help
	  Toshiba TC358767 eDP bridge chip driver.

config DRM_TOSHIBA_TC358768
	tristate "Toshiba TC358768 MIPI DSI bridge"
	depends on OF
	select DRM_KMS_HELPER
	select REGMAP_I2C
	select DRM_PANEL
	select DRM_MIPI_DSI
	select VIDEOMODE_HELPERS
	help
	  Toshiba TC358768AXBG/TC358778XBG DSI bridge chip driver.

config DRM_TOSHIBA_TC358775
	tristate "Toshiba TC358775 DSI/LVDS bridge"
	depends on OF
	select DRM_DISPLAY_DP_HELPER
	select DRM_DISPLAY_HELPER
	select DRM_KMS_HELPER
	select REGMAP_I2C
	select DRM_PANEL
	select DRM_MIPI_DSI
	help
	  Toshiba TC358775 DSI/LVDS bridge chip driver.

config DRM_TI_DLPC3433
	tristate "TI DLPC3433 Display controller"
	depends on DRM && DRM_PANEL
	depends on OF
	select DRM_MIPI_DSI
	help
	  TI DLPC3433 is a MIPI DSI based display controller bridge
	  for processing high resolution DMD based projectors.

	  It has a flexible configuration of MIPI DSI and DPI signal
	  input that produces a DMD output in RGB565, RGB666, RGB888
	  formats.

	  It supports upto 720p resolution with 60 and 120 Hz refresh
	  rates.

config DRM_TI_TFP410
	tristate "TI TFP410 DVI/HDMI bridge"
	depends on OF
	select DRM_KMS_HELPER
	help
	  Texas Instruments TFP410 DVI/HDMI Transmitter driver

config DRM_TI_SN65DSI83
	tristate "TI SN65DSI83 and SN65DSI84 DSI to LVDS bridge"
	depends on OF
	select DRM_KMS_HELPER
	select REGMAP_I2C
	select DRM_PANEL
	select DRM_MIPI_DSI
	help
	  Texas Instruments SN65DSI83 and SN65DSI84 DSI to LVDS Bridge driver

config DRM_TI_SN65DSI86
	tristate "TI SN65DSI86 DSI to eDP bridge"
	depends on OF
	select DRM_DISPLAY_DP_HELPER
	select DRM_DISPLAY_HELPER
	select DRM_KMS_HELPER
	select REGMAP_I2C
	select DRM_PANEL
	select DRM_MIPI_DSI
	select AUXILIARY_BUS
	select DRM_DP_AUX_BUS
	help
	  Texas Instruments SN65DSI86 DSI to eDP Bridge driver

config DRM_TI_TPD12S015
	tristate "TI TPD12S015 HDMI level shifter and ESD protection"
	depends on OF
	select DRM_KMS_HELPER
	help
	  Texas Instruments TPD12S015 HDMI level shifter and ESD protection
	  driver.

source "drivers/gpu/drm/bridge/analogix/Kconfig"

source "drivers/gpu/drm/bridge/adv7511/Kconfig"

source "drivers/gpu/drm/bridge/cadence/Kconfig"

source "drivers/gpu/drm/bridge/imx/Kconfig"

source "drivers/gpu/drm/bridge/synopsys/Kconfig"

endmenu
