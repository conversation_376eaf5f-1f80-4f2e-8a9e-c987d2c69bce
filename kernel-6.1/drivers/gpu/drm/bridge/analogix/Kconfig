# SPDX-License-Identifier: GPL-2.0-only
config DRM_ANALOGIX_ANX6345
	tristate "Analogix ANX6345 bridge"
	depends on OF
	select DRM_ANALOGIX_DP
	select DRM_DISPLAY_DP_HELPER
	select DRM_DISPLAY_HELP<PERSON>
	select DRM_K<PERSON>_HELPER
	select REGMAP_I2C
	help
	  ANX6345 is an ultra-low power Full-HD DisplayPort/eDP
	  transmitter designed for portable devices. The
	  ANX6345 transforms the LVTTL RGB output of an
	  application processor to eDP or DisplayPort.

config DRM_ANALOGIX_ANX78XX
	tristate "Analogix ANX78XX bridge"
	select DRM_ANALOGIX_DP
	select DRM_DISPLAY_DP_HELPER
	select DRM_DISPLAY_HELPER
	select DRM_KMS_HELPER
	select REGMAP_I2C
	help
	  ANX78XX is an ultra-low power Full-HD SlimPort transmitter
	  designed for portable devices. The ANX78XX transforms
	  the HDMI output of an application processor to MyDP
	  or DisplayPort.

config DRM_ANALOGIX_DP
	tristate
	depends on DRM

config DRM_ANALOGIX_ANX7625
	tristate "Analogix Anx7625 MIPI to DP interface support"
	depends on DRM
	depends on OF
	select DRM_DISPLAY_DP_HELPER
	select DRM_DISPLAY_HDCP_HELPER
	select DRM_DISPLAY_HELPER
	select DRM_DP_AUX_BUS
	select DRM_MIPI_DSI
	help
	  ANX7625 is an ultra-low power 4K mobile HD transmitter
	  designed for portable devices. It converts MIPI/DPI to
	  DisplayPort1.3 4K.
