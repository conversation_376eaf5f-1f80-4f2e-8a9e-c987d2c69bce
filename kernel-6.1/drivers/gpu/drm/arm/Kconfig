# SPDX-License-Identifier: GPL-2.0
menu "ARM devices"

config DRM_HDLCD
	tristate "ARM HDLCD"
	depends on DRM && OF && (ARM || ARM64 || COMPILE_TEST)
	depends on COMMON_CL<PERSON>
	select DRM_KMS_HELPER
	select DRM_GEM_DMA_HELPER
	help
	  Choose this option if you have an ARM High Definition Colour LCD
	  controller.

	  If M is selected the module will be called hdlcd.

config DRM_HDLCD_SHOW_UNDERRUN
	bool "Show underrun conditions"
	depends on DRM_HDLCD
	default n
	help
	  Enable this option to show in red colour the pixels that the
	  HDLCD device did not fetch from framebuffer due to underrun
	  conditions.

config DRM_MALI_DISPLAY
	tristate "ARM Mali Display Processor"
	depends on DRM && OF && (ARM || ARM64 || COMPILE_TEST)
	depends on COMMON_CLK
	select DRM_KMS_HELPER
	select DRM_GEM_DMA_HELPER
	select VIDEOMODE_HELPERS
	help
	  Choose this option if you want to compile the ARM Mali Display
	  Processor driver. It supports the DP500, DP550 and DP650 variants
	  of the hardware.

	  If compiled as a module it will be called mali-dp.

source "drivers/gpu/drm/arm/display/Kconfig"

endmenu
