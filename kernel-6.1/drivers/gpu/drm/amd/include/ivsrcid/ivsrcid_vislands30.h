/*
 * Volcanic Islands IV SRC Register documentation
 *
 * Copyright (C) 2015  Advanced Micro Devices, Inc.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a
 * copy of this software and associated documentation files (the "Software"),
 * to deal in the Software without restriction, including without limitation
 * the rights to use, copy, modify, merge, publish, distribute, sublicense,
 * and/or sell copies of the Software, and to permit persons to whom the
 * Software is furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included
 * in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL
 * THE COPYRIGHT HOLDER(S) BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN
 * AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
 * CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */

#ifndef _IVSRCID_VISLANDS30_H_
#define _IVSRCID_VISLANDS30_H_


// IV Source IDs

#define VISLANDS30_IV_SRCID_D1_V_UPDATE_INT		            7	    // 0x07	
#define VISLANDS30_IV_EXTID_D1_V_UPDATE_INT                  0

#define VISLANDS30_IV_SRCID_D1_GRPH_PFLIP		            8	    // 0x08	
#define VISLANDS30_IV_EXTID_D1_GRPH_PFLIP                    0

#define VISLANDS30_IV_SRCID_D2_V_UPDATE_INT		            9	    // 0x09	
#define VISLANDS30_IV_EXTID_D2_V_UPDATE_INT                  0

#define VISLANDS30_IV_SRCID_D2_GRPH_PFLIP  		            10	    // 0x0a	
#define VISLANDS30_IV_EXTID_D2_GRPH_PFLIP                    0

#define VISLANDS30_IV_SRCID_D3_V_UPDATE_INT		            11	    // 0x0b	
#define VISLANDS30_IV_EXTID_D3_V_UPDATE_INT                  0

#define VISLANDS30_IV_SRCID_D3_GRPH_PFLIP		            12	    // 0x0c	
#define VISLANDS30_IV_EXTID_D3_GRPH_PFLIP                    0

#define VISLANDS30_IV_SRCID_D4_V_UPDATE_INT		            13	    // 0x0d  	
#define VISLANDS30_IV_EXTID_D4_V_UPDATE_INT                  0

#define VISLANDS30_IV_SRCID_D4_GRPH_PFLIP		            14	    // 0x0e  	
#define VISLANDS30_IV_EXTID_D4_GRPH_PFLIP                    0

#define VISLANDS30_IV_SRCID_D5_V_UPDATE_INT		            15	    // 0x0f	
#define VISLANDS30_IV_EXTID_D5_V_UPDATE_INT                  0

#define VISLANDS30_IV_SRCID_D5_GRPH_PFLIP		            16	    // 0x10  	
#define VISLANDS30_IV_EXTID_D5_GRPH_PFLIP                    0

#define VISLANDS30_IV_SRCID_D6_V_UPDATE_INT		            17	    // 0x11      	
#define VISLANDS30_IV_EXTID_D6_V_UPDATE_INT                  0

#define VISLANDS30_IV_SRCID_D6_GRPH_PFLIP		            18	    // 0x12  	
#define VISLANDS30_IV_EXTID_D6_GRPH_PFLIP                    0

#define VISLANDS30_IV_SRCID_D1_VERTICAL_INTERRUPT0           19      // 0x13
#define VISLANDS30_IV_EXTID_D1_VERTICAL_INTERRUPT0           7

#define VISLANDS30_IV_SRCID_D1_VERTICAL_INTERRUPT1           19      // 0x13
#define VISLANDS30_IV_EXTID_D1_VERTICAL_INTERRUPT1           8

#define VISLANDS30_IV_SRCID_D1_VERTICAL_INTERRUPT2           19      // 0x13
#define VISLANDS30_IV_EXTID_D1_VERTICAL_INTERRUPT2           9

#define VISLANDS30_IV_SRCID_D1_EXT_TIMING_SYNC_LOSS          19      // 0x13
#define VISLANDS30_IV_EXTID_D1_EXT_TIMING_SYNC_LOSS          10

#define VISLANDS30_IV_SRCID_D1_EXT_TIMING_SYNC               19      // 0x13
#define VISLANDS30_IV_EXTID_D1_EXT_TIMING_SYNC               11

#define VISLANDS30_IV_SRCID_D1_EXT_TIMING_SIGNAL             19      // 0x13
#define VISLANDS30_IV_EXTID_D1_EXT_TIMING_SIGNAL             12

#define VISLANDS30_IV_SRCID_D2_VERTICAL_INTERRUPT0           20      // 0x14
#define VISLANDS30_IV_EXTID_D2_VERTICAL_INTERRUPT0           7

#define VISLANDS30_IV_SRCID_D2_VERTICAL_INTERRUPT1           20      // 0x14
#define VISLANDS30_IV_EXTID_D2_VERTICAL_INTERRUPT1           8

#define VISLANDS30_IV_SRCID_D2_VERTICAL_INTERRUPT2           20      // 0x14
#define VISLANDS30_IV_EXTID_D2_VERTICAL_INTERRUPT2           9

#define VISLANDS30_IV_SRCID_D2_EXT_TIMING_SYNC_LOSS          20      // 0x14
#define VISLANDS30_IV_EXTID_D2_EXT_TIMING_SYNC_LOSS          10

#define VISLANDS30_IV_SRCID_D2_EXT_TIMING_SYNC               20      // 0x14
#define VISLANDS30_IV_EXTID_D2_EXT_TIMING_SYNC               11

#define VISLANDS30_IV_SRCID_D2_EXT_TIMING_SIGNAL             20      // 0x14
#define VISLANDS30_IV_EXTID_D2_EXT_TIMING_SIGNAL             12

#define VISLANDS30_IV_SRCID_D3_VERTICAL_INTERRUPT0           21      // 0x15
#define VISLANDS30_IV_EXTID_D3_VERTICAL_INTERRUPT0           7

#define VISLANDS30_IV_SRCID_D3_VERTICAL_INTERRUPT1           21      // 0x15
#define VISLANDS30_IV_EXTID_D3_VERTICAL_INTERRUPT1           8

#define VISLANDS30_IV_SRCID_D3_VERTICAL_INTERRUPT2           21      // 0x15
#define VISLANDS30_IV_EXTID_D3_VERTICAL_INTERRUPT2           9

#define VISLANDS30_IV_SRCID_D3_EXT_TIMING_SYNC_LOSS          21      // 0x15
#define VISLANDS30_IV_EXTID_D3_EXT_TIMING_SYNC_LOSS          10

#define VISLANDS30_IV_SRCID_D3_EXT_TIMING_SYNC               21      // 0x15
#define VISLANDS30_IV_EXTID_D3_EXT_TIMING_SYNC               11

#define VISLANDS30_IV_SRCID_D3_EXT_TIMING_SIGNAL             21      // 0x15
#define VISLANDS30_IV_EXTID_D3_EXT_TIMING_SIGNAL             12

#define VISLANDS30_IV_SRCID_D4_VERTICAL_INTERRUPT0           22      // 0x16
#define VISLANDS30_IV_EXTID_D4_VERTICAL_INTERRUPT0           7

#define VISLANDS30_IV_SRCID_D4_VERTICAL_INTERRUPT1           22      // 0x16
#define VISLANDS30_IV_EXTID_D4_VERTICAL_INTERRUPT1           8

#define VISLANDS30_IV_SRCID_D4_VERTICAL_INTERRUPT2           22      // 0x16
#define VISLANDS30_IV_EXTID_D4_VERTICAL_INTERRUPT2           9

#define VISLANDS30_IV_SRCID_D4_EXT_TIMING_SYNC_LOSS          22      // 0x16
#define VISLANDS30_IV_EXTID_D4_EXT_TIMING_SYNC_LOSS          10

#define VISLANDS30_IV_SRCID_D4_EXT_TIMING_SYNC               22      // 0x16
#define VISLANDS30_IV_EXTID_D4_EXT_TIMING_SYNC               11

#define VISLANDS30_IV_SRCID_D4_EXT_TIMING_SIGNAL             22      // 0x16
#define VISLANDS30_IV_EXTID_D4_EXT_TIMING_SIGNAL             12

#define VISLANDS30_IV_SRCID_D5_VERTICAL_INTERRUPT0           23      // 0x17
#define VISLANDS30_IV_EXTID_D5_VERTICAL_INTERRUPT0           7

#define VISLANDS30_IV_SRCID_D5_VERTICAL_INTERRUPT1           23      // 0x17
#define VISLANDS30_IV_EXTID_D5_VERTICAL_INTERRUPT1           8

#define VISLANDS30_IV_SRCID_D5_VERTICAL_INTERRUPT2           23      // 0x17
#define VISLANDS30_IV_EXTID_D5_VERTICAL_INTERRUPT2           9

#define VISLANDS30_IV_SRCID_D5_EXT_TIMING_SYNC_LOSS          23      // 0x17
#define VISLANDS30_IV_EXTID_D5_EXT_TIMING_SYNC_LOSS          10

#define VISLANDS30_IV_SRCID_D5_EXT_TIMING_SYNC               23      // 0x17
#define VISLANDS30_IV_EXTID_D5_EXT_TIMING_SYNC               11

#define VISLANDS30_IV_SRCID_D5_EXT_TIMING_SIGNAL             23      // 0x17
#define VISLANDS30_IV_EXTID_D5_EXT_TIMING_SIGNAL             12

#define VISLANDS30_IV_SRCID_D6_VERTICAL_INTERRUPT0           24      // 0x18
#define VISLANDS30_IV_EXTID_D6_VERTICAL_INTERRUPT0           7

#define VISLANDS30_IV_SRCID_D6_VERTICAL_INTERRUPT1           24      // 0x18
#define VISLANDS30_IV_EXTID_D6_VERTICAL_INTERRUPT1           8

#define VISLANDS30_IV_SRCID_D6_VERTICAL_INTERRUPT2           24      // 0x18
#define VISLANDS30_IV_EXTID_D6_VERTICAL_INTERRUPT2           9

#define VISLANDS30_IV_SRCID_HOTPLUG_DETECT_A		            42	    // 0x2a	
#define VISLANDS30_IV_EXTID_HOTPLUG_DETECT_A                 0

#define VISLANDS30_IV_SRCID_HOTPLUG_DETECT_B   		        42	    // 0x2a		
#define VISLANDS30_IV_EXTID_HOTPLUG_DETECT_B                 1

#define VISLANDS30_IV_SRCID_HOTPLUG_DETECT_C   		        42	    // 0x2a		
#define VISLANDS30_IV_EXTID_HOTPLUG_DETECT_C                 2

#define VISLANDS30_IV_SRCID_HOTPLUG_DETECT_D	    	        42	    // 0x2a		
#define VISLANDS30_IV_EXTID_HOTPLUG_DETECT_D                 3

#define VISLANDS30_IV_SRCID_HOTPLUG_DETECT_E		            42	    // 0x2a		
#define VISLANDS30_IV_EXTID_HOTPLUG_DETECT_E                 4

#define VISLANDS30_IV_SRCID_HOTPLUG_DETECT_F		            42	    // 0x2a		
#define VISLANDS30_IV_EXTID_HOTPLUG_DETECT_F                 5

#define VISLANDS30_IV_SRCID_HPD_RX_A		                    42	    // 0x2a		
#define VISLANDS30_IV_EXTID_HPD_RX_A                         6

#define VISLANDS30_IV_SRCID_HPD_RX_B		                    42	    // 0x2a		
#define VISLANDS30_IV_EXTID_HPD_RX_B                         7

#define VISLANDS30_IV_SRCID_HPD_RX_C		                    42	    // 0x2a		
#define VISLANDS30_IV_EXTID_HPD_RX_C                         8

#define VISLANDS30_IV_SRCID_HPD_RX_D		                    42	    // 0x2a		
#define VISLANDS30_IV_EXTID_HPD_RX_D                         9

#define VISLANDS30_IV_SRCID_HPD_RX_E		                    42	    // 0x2a		
#define VISLANDS30_IV_EXTID_HPD_RX_E                         10

#define VISLANDS30_IV_SRCID_HPD_RX_F		                    42	    // 0x2a		
#define VISLANDS30_IV_EXTID_HPD_RX_F                         11

#define VISLANDS30_IV_SRCID_GPIO_19                            0x00000053  /* 83 */

#define VISLANDS30_IV_SRCID_SRBM_READ_TIMEOUT_ERR              0x00000060  /* 96 */
#define VISLANDS30_IV_SRCID_SRBM_CTX_SWITCH                    0x00000061  /* 97 */

#define VISLANDS30_IV_SRBM_REG_ACCESS_ERROR                    0x00000062  /* 98 */


#define VISLANDS30_IV_SRCID_UVD_ENC_GEN_PURP                   0x00000077  /* 119 */
#define VISLANDS30_IV_SRCID_UVD_SYSTEM_MESSAGE                 0x0000007c  /* 124 */

#define VISLANDS30_IV_SRCID_BIF_PF_VF_MSGBUF_VALID             0x00000087  /* 135 */

#define VISLANDS30_IV_SRCID_BIF_VF_PF_MSGBUF_ACK               0x0000008a  /* 138 */

#define VISLANDS30_IV_SRCID_SYS_PAGE_INV_FAULT                 0x0000008c  /* 140 */
#define VISLANDS30_IV_SRCID_SYS_MEM_PROT_FAULT                 0x0000008d  /* 141 */

#define VISLANDS30_IV_SRCID_SEM_PAGE_INV_FAULT                 0x00000090  /* 144 */
#define VISLANDS30_IV_SRCID_SEM_MEM_PROT_FAULT                 0x00000091  /* 145 */

#define VISLANDS30_IV_SRCID_GFX_PAGE_INV_FAULT                 0x00000092  /* 146 */
#define VISLANDS30_IV_SRCID_GFX_MEM_PROT_FAULT                 0x00000093  /* 147 */

#define VISLANDS30_IV_SRCID_ACP                                0x000000a2  /* 162 */

#define VISLANDS30_IV_SRCID_VCE_TRAP                           0x000000a7  /* 167 */
#define VISLANDS30_IV_EXTID_VCE_TRAP_GENERAL_PURPOSE           0
#define VISLANDS30_IV_EXTID_VCE_TRAP_LOW_LATENCY               1
#define VISLANDS30_IV_EXTID_VCE_TRAP_REAL_TIME                 2

#define VISLANDS30_IV_SRCID_CP_INT_RB                          0x000000b0  /* 176 */
#define VISLANDS30_IV_SRCID_CP_INT_IB1                         0x000000b1  /* 177 */
#define VISLANDS30_IV_SRCID_CP_INT_IB2                         0x000000b2  /* 178 */
#define VISLANDS30_IV_SRCID_CP_PM4_RES_BITS_ERR                0x000000b4  /* 180 */
#define VISLANDS30_IV_SRCID_CP_END_OF_PIPE                     0x000000b5  /* 181 */
#define VISLANDS30_IV_SRCID_CP_BAD_OPCODE                      0x000000b7  /* 183 */
#define VISLANDS30_IV_SRCID_CP_PRIV_REG_FAULT                  0x000000b8  /* 184 */
#define VISLANDS30_IV_SRCID_CP_PRIV_INSTR_FAULT                0x000000b9  /* 185 */
#define VISLANDS30_IV_SRCID_CP_WAIT_MEM_SEM_FAULT              0x000000ba  /* 186 */
#define VISLANDS30_IV_SRCID_CP_GUI_IDLE                        0x000000bb  /* 187 */
#define VISLANDS30_IV_SRCID_CP_GUI_BUSY                        0x000000bc  /* 188 */

#define VISLANDS30_IV_SRCID_CP_COMPUTE_QUERY_STATUS            0x000000bf  /* 191 */
#define VISLANDS30_IV_SRCID_CP_ECC_ERROR                       0x000000c5  /* 197 */

#define CARRIZO_IV_SRCID_CP_COMPUTE_QUERY_STATUS               0x000000c7  /* 199 */

#define VISLANDS30_IV_SRCID_CP_WAIT_REG_MEM_POLL_TIMEOUT       0x000000c0  /* 192 */
#define VISLANDS30_IV_SRCID_CP_SEM_SIG_INCOMPL                 0x000000c1  /* 193 */
#define VISLANDS30_IV_SRCID_CP_PREEMPT_ACK                     0x000000c2  /* 194 */
#define VISLANDS30_IV_SRCID_CP_GENERAL_PROT_FAULT              0x000000c3  /* 195 */
#define VISLANDS30_IV_SRCID_CP_GDS_ALLOC_ERROR                 0x000000c4  /* 196 */
#define VISLANDS30_IV_SRCID_CP_ECC_ERROR                       0x000000c5  /* 197 */

#define VISLANDS30_IV_SRCID_RLC_STRM_PERF_MONITOR              0x000000ca  /* 202 */

#define VISLANDS30_IV_SDMA_ATOMIC_SRC_ID                       0x000000da  /* 218 */

#define VISLANDS30_IV_SRCID_SDMA_ECC_ERROR                     0x000000dc  /* 220 */

#define VISLANDS30_IV_SRCID_SDMA_TRAP          	               0x000000e0  /* 224 */
#define VISLANDS30_IV_SRCID_SDMA_SEM_INCOMPLETE                0x000000e1  /* 225 */
#define VISLANDS30_IV_SRCID_SDMA_SEM_WAIT                      0x000000e2  /* 226 */


#define VISLANDS30_IV_SRCID_SMU_DISP_TIMER2_TRIGGER            0x000000e5  /* 229 */

#define VISLANDS30_IV_SRCID_CG_TSS_THERMAL_LOW_TO_HIGH         0x000000e6  /* 230 */
#define VISLANDS30_IV_SRCID_CG_TSS_THERMAL_HIGH_TO_LOW         0x000000e7  /* 231 */

#define VISLANDS30_IV_SRCID_GRBM_READ_TIMEOUT_ERR              0x000000e8  /* 232 */
#define VISLANDS30_IV_SRCID_GRBM_REG_GUI_IDLE                  0x000000e9  /* 233 */

#define VISLANDS30_IV_SRCID_SQ_INTERRUPT_MSG                   0x000000ef  /* 239 */

#define VISLANDS30_IV_SRCID_SDMA_PREEMPT                       0x000000f0  /* 240 */
#define VISLANDS30_IV_SRCID_SDMA_VM_HOLE                       0x000000f2  /* 242 */
#define VISLANDS30_IV_SRCID_SDMA_CTXEMPTY                      0x000000f3  /* 243 */
#define VISLANDS30_IV_SRCID_SDMA_DOORBELL_INVALID              0x000000f4  /* 244 */
#define VISLANDS30_IV_SRCID_SDMA_FROZEN                        0x000000f5  /* 245 */
#define VISLANDS30_IV_SRCID_SDMA_POLL_TIMEOUT                  0x000000f6  /* 246 */
#define VISLANDS30_IV_SRCID_SDMA_SRBM_WRITE                    0x000000f7  /* 247 */

#define VISLANDS30_IV_SRCID_CG_THERMAL_TRIG                    0x000000f8  /* 248 */

#define VISLANDS30_IV_SRCID_SMU_DISP_TIMER_TRIGGER             0x000000fd  /* 253 */

/* These are not "real" source ids defined by HW */
#define VISLANDS30_IV_SRCID_VM_CONTEXT_ALL                     0x00000100  /* 256 */
#define VISLANDS30_IV_EXTID_VM_CONTEXT0_ALL                    0
#define VISLANDS30_IV_EXTID_VM_CONTEXT1_ALL                    1


/* IV Extended IDs */
#define VISLANDS30_IV_EXTID_NONE                               0x00000000
#define VISLANDS30_IV_EXTID_INVALID                            0xffffffff

#endif // _IVSRCID_VISLANDS30_H_
