/*
 * Copyright 2017 Advanced Micro Devices, Inc.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a
 * copy of this software and associated documentation files (the "Software"),
 * to deal in the Software without restriction, including without limitation
 * the rights to use, copy, modify, merge, publish, distribute, sublicense,
 * and/or sell copies of the Software, and to permit persons to whom the
 * Software is furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL
 * THE COPYRIGHT HOLDER(S) OR AUTHOR(S) BE LIABLE FOR ANY CLAIM, DAMAGES OR
 * OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE,
 * ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 * OTHER DEALINGS IN THE SOFTWARE.
 *
 * Authors: <AUTHORS>
 *
 */

#ifndef __IRQSRCS_DCN_1_0_H__
#define __IRQSRCS_DCN_1_0_H__


#define DCN_1_0__SRCID__DC_I2C_SW_DONE	            1	// DC_I2C SW done	DC_I2C_SW_DONE_INTERRUPT	DISP_INTERRUPT_STATUS	Level	
#define DCN_1_0__CTXID__DC_I2C_SW_DONE	            0

#define DCN_1_0__SRCID__DC_I2C_DDC1_HW_DONE	        1	// DC_I2C DDC1 HW done	DOUT_IHC_I2C_DDC1_HW_DONE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE21	Level	
#define DCN_1_0__CTXID__DC_I2C_DDC1_HW_DONE	        1

#define DCN_1_0__SRCID__DC_I2C_DDC2_HW_DONE	        1	// DC_I2C DDC2 HW done	DOUT_IHC_I2C_DDC2_HW_DONE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE21	Level	
#define DCN_1_0__CTXID__DC_I2C_DDC2_HW_DONE	        2

#define DCN_1_0__SRCID__DC_I2C_DDC3_HW_DONE	        1	// DC_I2C DDC3 HW done	DOUT_IHC_I2C_DDC3_HW_DONE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE21	Level	
#define DCN_1_0__CTXID__DC_I2C_DDC3_HW_DONE	        3

#define DCN_1_0__SRCID__DC_I2C_DDC4_HW_DONE	        1	// DC_I2C_DDC4 HW done	DOUT_IHC_I2C_DDC4_HW_DONE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE21	Level	
#define DCN_1_0__CTXID__DC_I2C_DDC4_HW_DONE         4

#define DCN_1_0__SRCID__DC_I2C_DDC5_HW_DONE	        1	// DC_I2C_DDC5 HW done	DOUT_IHC_I2C_DDC5_HW_DONE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE21	Level	
#define DCN_1_0__CTXID__DC_I2C_DDC5_HW_DONE	        5

#define DCN_1_0__SRCID__DC_I2C_DDC6_HW_DONE	        1	// DC_I2C_DDC6 HW done	DOUT_IHC_I2C_DDC6_HW_DONE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE21	Level	
#define DCN_1_0__CTXID__DC_I2C_DDC6_HW_DONE	        6

#define DCN_1_0__SRCID__DC_I2C_DDCVGA_HW_DONE	    1	// DC_I2C_DDCVGA HW done	DOUT_IHC_I2C_DDCVGA_HW_DONE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE21	Level	
#define DCN_1_0__CTXID__DC_I2C_DDCVGA_HW_DONE	    7

#define DCN_1_0__SRCID__DC_I2C_DDC1_READ_REQUEST	1   // DC_I2C DDC1 read request	DC_I2C_DDC1_READ_REQUEST_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE21	Level / Pulse	
#define DCN_1_0__CTXID__DC_I2C_DDC1_READ_REQUEST	8

#define DCN_1_0__SRCID__DC_I2C_DDC2_READ_REQUEST	1	// DC_I2C DDC2 read request	DC_I2C_DDC2_READ_REQUEST_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE21	Level / Pulse	
#define DCN_1_0__CTXID__DC_I2C_DDC2_READ_REQUEST	9

#define DCN_1_0__SRCID__DC_I2C_DDC3_READ_REQUEST	1	// DC_I2C DDC3 read request	DC_I2C_DDC3_READ_REQUEST_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE21	Level / Pulse	
#define DCN_1_0__CTXID__DC_I2C_DDC3_READ_REQUEST	10

#define DCN_1_0__SRCID__DC_I2C_DDC4_READ_REQUEST	1	// DC_I2C_DDC4 read request	DC_I2C_DDC4_READ_REQUEST_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE21	Level / Pulse	
#define DCN_1_0__CTXID__DC_I2C_DDC4_READ_REQUEST	11

#define DCN_1_0__SRCID__DC_I2C_DDC5_READ_REQUEST	1	// DC_I2C_DDC5 read request	DC_I2C_DDC5_READ_REQUEST_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE21	Level / Pulse	
#define DCN_1_0__CTXID__DC_I2C_DDC5_READ_REQUEST	12

#define DCN_1_0__SRCID__DC_I2C_DDC6_READ_REQUEST	1	// DC_I2C_DDC6 read request	DC_I2C_DDC6_READ_REQUEST_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE21	Level / Pulse	
#define DCN_1_0__CTXID__DC_I2C_DDC6_READ_REQUEST	13

#define DCN_1_0__SRCID__DC_I2C_DDCVGA_READ_REQUEST	1	// DC_I2C_DDCVGA read request	DC_I2C_VGA_READ_REQUEST_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE21	Level / Pulse	
#define DCN_1_0__CTXID__DC_I2C_DDCVGA_READ_REQUEST	14

#define DCN_1_0__SRCID__GENERIC_I2C_DDC_READ_REQUEST	1	// GENERIC_I2C_DDC read request	GENERIC_I2C_DDC_READ_REUEST_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE21	Level / Pulse	
#define DCN_1_0__CTXID__GENERIC_I2C_DDC_READ_REQUEST	15

#define DCN_1_0__SRCID__DCCG_PERFCOUNTER_INT0_STATUS	2	// DCCG perfmon counter0 interrupt	DCCG_PERFMON_COUNTER0_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE7	Level / Pulse	
#define DCN_1_0__CTXID__DCCG_PERFCOUNTER_INT0_STATUS	7

#define DCN_1_0__SRCID__DCCG_PERFCOUNTER_INT1_STATUS	2	// DCCG perfmon counter1 interrupt	DCCG_PERFMON_COUNTER1_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE7	Level	
#define DCN_1_0__CTXID__DCCG_PERFCOUNTER_INT1_STATUS	8

#define DCN_1_0__SRCID__DMU_PERFCOUNTER_INT0_STATUS	3	// DMU perfmon counter0 interrupt	DMU_PERFMON_COUNTER0_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE7	Level / Pulse	
#define DCN_1_0__CTXID__DMU_PERFCOUNTER_INT0_STATUS	7

#define DCN_1_0__SRCID__DMU_PERFCOUNTER_INT1_STATUS	3	// DMU perfmon counter1 interrupt	DMU_PERFMON_COUNTER1_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE7	Level	
#define DCN_1_0__CTXID__DMU_PERFCOUNTER_INT1_STATUS	8

#define DCN_1_0__SRCID__DIO_PERFCOUNTER_INT0_STATUS	4	// DIO perfmon counter0 interrupt	DIO_PERFMON_COUNTER0_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE7	Level / Pulse	
#define DCN_1_0__CTXID__DIO_PERFCOUNTER_INT0_STATUS	7

#define DCN_1_0__SRCID__DIO_PERFCOUNTER_INT1_STATUS	4	// DIO perfmon counter1 interrupt	DIO_PERFMON_COUNTER1_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE7	Level	
#define DCN_1_0__CTXID__DIO_PERFCOUNTER_INT1_STATUS	8

#define DCN_1_0__SRCID__RBBMIF_TIMEOUT_INT	        5	// RBBMIF timeout interrupt	RBBMIF_IHC_TIMEOUT_INTERRUPT	DISP_INTERRUPT_STATUS	Level	
#define DCN_1_0__CTXID__RBBMIF_TIMEOUT_INT	        12

#define DCN_1_0__SRCID__DMCU_INTERNAL_INT	        5	// DMCU execution exception	DMCU_UC_INTERNAL_INT	DISP_INTERRUPT_STATUS	Level	
#define DCN_1_0__CTXID__DMCU_INTERNAL_INT	        13

#define DCN_1_0__SRCID__DMCU_SCP_INT	            5	// DMCU  Slave Communication Port Interrupt	DMCU_SCP_INT	DISP_INTERRUPT_STATUS	Level	
#define DCN_1_0__CTXID__DMCU_SCP_INT	            14

#define DCN_1_0__SRCID__DMCU_ABM0_HG_READY_INT	    6	// ABM histogram ready interrupt	ABM0_HG_READY_INT	DISP_INTERRUPT_STATUS_CONTINUE22	Level	
#define DCN_1_0__CTXID__DMCU_ABM0_HG_READY_INT	    0

#define DCN_1_0__SRCID__DMCU_ABM0_LS_READY_INT	    6	// ABM luma stat ready interrupt	ABM0_LS_READY_INT	DISP_INTERRUPT_STATUS_CONTINUE22	Level	
#define DCN_1_0__CTXID__DMCU_ABM0_LS_READY_INT	    1

#define DCN_1_0__SRCID__DMCU_ABM0_BL_UPDATE_INT	    6	// ABM Backlight update interrupt  	ABM0_BL_UPDATE_INT	DISP_INTERRUPT_STATUS_CONTINUE22	Level	
#define DCN_1_0__CTXID__DMCU_ABM0_BL_UPDATE_INT	    2

#define DCN_1_0__SRCID__DMCU_ABM1_HG_READY_INT	    6	// ABM histogram ready interrupt	ABM1_HG_READY_INT	DISP_INTERRUPT_STATUS	Level	
#define DCN_1_0__CTXID__DMCU_ABM1_HG_READY_INT	    3

#define DCN_1_0__SRCID__DMCU_ABM1_LS_READY_INT	    6	// ABM luma stat ready interrupt	ABM1_LS_READY_INT	DISP_INTERRUPT_STATUS	Level	
#define DCN_1_0__CTXID__DMCU_ABM1_LS_READY_INT	    4

#define DCN_1_0__SRCID__DMCU_ABM1_BL_UPDATE_INT	    6	// ABM Backlight update interrupt  	ABM1_BL_UPDATE_INT	DISP_INTERRUPT_STATUS	Level	
#define DCN_1_0__CTXID__DMCU_ABM1_BL_UPDATE_INT	    5

#define DCN_1_0__SRCID__WB0_PERFCOUNTER_INT0_STATUS	6	// WB0 perfmon counter0 interrupt	WB0_PERFMON_COUNTER0_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE7	Level / Pulse	
#define DCN_1_0__CTXID__WB0_PERFCOUNTER_INT0_STATUS	6

#define DCN_1_0__SRCID__WB0_PERFCOUNTER_INT1_STATUS	6	// WB0 perfmon counter1 interrupt	WB0_PERFMON_COUNTER1_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE7	Level	
#define DCN_1_0__CTXID__WB0_PERFCOUNTER_INT1_STATUS	7

#define DCN_1_0__SRCID__DPDBG_FIFO_OVERFLOW_INT	    7	// DP debug FIFO overflow interrupt	DPDBG_IHC_FIFO_OVERFLOW_INT	DISP_INTERRUPT_STATUS_CONTINUE21	Level / Pulse	
#define DCN_1_0__CTXID__DPDBG_FIFO_OVERFLOW_INT	    1

#define DCN_1_0__SRCID__DCIO_DPCS_TXA_ERROR_INT	    8	// DPCS TXA error interrupt	DCIO_DPCS_TXA_IHC_ERROR_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE18	Level / Pulse	
#define DCN_1_0__CTXID__DCIO_DPCS_TXA_ERROR_INT	    0

#define DCN_1_0__SRCID__DCIO_DPCS_TXB_ERROR_INT	    8	// DPCS TXB error interrupt	DCIO_DPCS_TXB_IHC_ERROR_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE18	Level / Pulse	
#define DCN_1_0__CTXID__DCIO_DPCS_TXB_ERROR_INT	    1

#define DCN_1_0__SRCID__DCIO_DPCS_TXC_ERROR_INT	    8	// DPCS TXC error interrupt	DCIO_DPCS_TXC_IHC_ERROR_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE18	Level / Pulse	
#define DCN_1_0__CTXID__DCIO_DPCS_TXC_ERROR_INT	    2

#define DCN_1_0__SRCID__DCIO_DPCS_TXD_ERROR_INT	    8	// DPCS TXD error interrupt	DCIO_DPCS_TXD_IHC_ERROR_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE18	Level / Pulse	
#define DCN_1_0__CTXID__DCIO_DPCS_TXD_ERROR_INT	    3

#define DCN_1_0__SRCID__DCIO_DPCS_TXE_ERROR_INT	    8	// DPCS TXE error interrupt	DCIO_DPCS_TXE_IHC_ERROR_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE18	Level / Pulse	
#define DCN_1_0__CTXID__DCIO_DPCS_TXE_ERROR_INT	    4

#define DCN_1_0__SRCID__DCIO_DPCS_TXF_ERROR_INT	    8	// DPCS TXF error interrupt	DCIO_DPCS_TXF_IHC_ERROR_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE18	Level / Pulse	
#define DCN_1_0__CTXID__DCIO_DPCS_TXF_ERROR_INT	    5

#define DCN_1_0__SRCID__DCIO_DPCS_TXG_ERROR_INT	    8	// DPCS TXG error interrupt	DCIO_DPCS_TXG_IHC_ERROR_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE18	Level / Pulse	
#define DCN_1_0__CTXID__DCIO_DPCS_TXG_ERROR_INT	    6

#define DCN_1_0__SRCID__DCIO_DPCS_RXA_ERROR_INT	    8	// DPCS RXA error interrupt	DCIO_DPCS_RXA_IHC_ERROR_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE18	Level / Pulse	
#define DCN_1_0__CTXID__DCIO_DPCS_RXA_ERROR_INT	    7

#define DCN_1_0__SRCID__DC_HPD1_INT	                9	// Hot Plug Detection 1	DC_HPD1_INTERRUPT	DISP_INTERRUPT_STATUS	Level	
#define DCN_1_0__CTXID__DC_HPD1_INT	                0

#define DCN_1_0__SRCID__DC_HPD2_INT	                9	// Hot Plug Detection 2	DC_HPD2_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE	Level	
#define DCN_1_0__CTXID__DC_HPD2_INT	                1

#define DCN_1_0__SRCID__DC_HPD3_INT	                9	// Hot Plug Detection 3	DC_HPD3_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE2	Level	
#define DCN_1_0__CTXID__DC_HPD3_INT	                2

#define DCN_1_0__SRCID__DC_HPD4_INT	                9	// Hot Plug Detection 4	DC_HPD4_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE3	Level	
#define DCN_1_0__CTXID__DC_HPD4_INT	                3

#define DCN_1_0__SRCID__DC_HPD5_INT	                9	// Hot Plug Detection 5	DC_HPD5_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE4	Level	
#define DCN_1_0__CTXID__DC_HPD5_INT	                4

#define DCN_1_0__SRCID__DC_HPD6_INT	                9	// Hot Plug Detection 6	DC_HPD6_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE5	Level	
#define DCN_1_0__CTXID__DC_HPD6_INT	                5 

#define DCN_1_0__SRCID__DC_HPD1_RX_INT	            9	// Hot Plug Detection RX interrupt 1	DC_HPD1_RX_INTERRUPT	DISP_INTERRUPT_STATUS	Level	
#define DCN_1_0__CTXID__DC_HPD1_RX_INT	            6

#define DCN_1_0__SRCID__DC_HPD2_RX_INT	            9	// Hot Plug Detection RX interrupt 2	DC_HPD2_RX_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE	Level	
#define DCN_1_0__CTXID__DC_HPD2_RX_INT	            7

#define DCN_1_0__SRCID__DC_HPD3_RX_INT	            9	// Hot Plug Detection RX interrupt 3	DC_HPD3_RX_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE2	Level	
#define DCN_1_0__CTXID__DC_HPD3_RX_INT	            8

#define DCN_1_0__SRCID__DC_HPD4_RX_INT	            9	// Hot Plug Detection RX interrupt 4	DC_HPD4_RX_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE3	Level	
#define DCN_1_0__CTXID__DC_HPD4_RX_INT	            9

#define DCN_1_0__SRCID__DC_HPD5_RX_INT	            9	// Hot Plug Detection RX interrupt 5	DC_HPD5_RX_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE4	Level	
#define DCN_1_0__CTXID__DC_HPD5_RX_INT	            10

#define DCN_1_0__SRCID__DC_HPD6_RX_INT	            9	// Hot Plug Detection RX interrupt 6	DC_HPD6_RX_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE5	Level	
#define DCN_1_0__CTXID__DC_HPD6_RX_INT	            11

#define DCN_1_0__SRCID__DC_DAC_A_AUTO_DET	        0xA	// DAC A auto - detection	DACA_AUTODETECT_GENERITE_INTERRUPT	DISP_INTERRUPT_STATUS	Level	
#define DCN_1_0__CTXID__DC_DAC_A_AUTO_DET	        0

#define DCN_1_0__SRCID__AZ_ENDPOINT0_AUDIO_FMT_CHANGED_INT	0xA	// AZ Endpoint0 format changed	AZ_IHC_ENDPOINT0_AUDIO_FORMAT_CHANGED_INT	DISP_INTERRUPT_STATUS_CONTINUE19	Level / Pulse	
#define DCN_1_0__CTXID__AZ_ENDPOINT0_AUDIO_FMT_CHANGED_INT	2

#define DCN_1_0__SRCID__AZ_ENDPOINT1_AUDIO_FMT_CHANGED_INT	0xA	// AZ Endpoint1 format changed	AZ_IHC_ENDPOINT1_AUDIO_FORMAT_CHANGED_INT	DISP_INTERRUPT_STATUS_CONTINUE19	Level / Pulse	
#define DCN_1_0__CTXID__AZ_ENDPOINT1_AUDIO_FMT_CHANGED_INT	3

#define DCN_1_0__SRCID__AZ_ENDPOINT2_AUDIO_FMT_CHANGED_INT	0xA	// AZ Endpoint2 format changed	AZ_IHC_ENDPOINT2_AUDIO_FORMAT_CHANGED_INT	DISP_INTERRUPT_STATUS_CONTINUE19	Level / Pulse	
#define DCN_1_0__CTXID__AZ_ENDPOINT2_AUDIO_FMT_CHANGED_INT	4

#define DCN_1_0__SRCID__AZ_ENDPOINT3_AUDIO_FMT_CHANGED_INT	0xA	// AZ Endpoint3 format changed	AZ_IHC_ENDPOINT3_AUDIO_FORMAT_CHANGED_INT	DISP_INTERRUPT_STATUS_CONTINUE19	Level / Pulse	
#define DCN_1_0__CTXID__AZ_ENDPOINT3_AUDIO_FMT_CHANGED_INT	5

#define DCN_1_0__SRCID__AZ_ENDPOINT4_AUDIO_FMT_CHANGED_INT	0xA	// AZ Endpoint4 format changed	AZ_IHC_ENDPOINT4_AUDIO_FORMAT_CHANGED_INT	DISP_INTERRUPT_STATUS_CONTINUE19	Level / Pulse	
#define DCN_1_0__CTXID__AZ_ENDPOINT4_AUDIO_FMT_CHANGED_INT	6

#define DCN_1_0__SRCID__AZ_ENDPOINT5_AUDIO_FMT_CHANGED_INT	0xA	// AZ Endpoint5 format changed	AZ_IHC_ENDPOINT5_AUDIO_FORMAT_CHANGED_INT	DISP_INTERRUPT_STATUS_CONTINUE19	Level / Pulse	
#define DCN_1_0__CTXID__AZ_ENDPOINT5_AUDIO_FMT_CHANGED_INT	7

#define DCN_1_0__SRCID__AZ_ENDPOINT6_AUDIO_FMT_CHANGED_INT	0xA	// AZ Endpoint6 format changed	AZ_IHC_ENDPOINT6_AUDIO_FORMAT_CHANGED_INT	DISP_INTERRUPT_STATUS_CONTINUE19	Level / Pulse	
#define DCN_1_0__CTXID__AZ_ENDPOINT6_AUDIO_FMT_CHANGED_INT	8

#define DCN_1_0__SRCID__AZ_ENDPOINT7_AUDIO_FMT_CHANGED_INT	0xA	// AZ Endpoint7 format changed	AZ_IHC_ENDPOINT7_AUDIO_FORMAT_CHANGED_INT	DISP_INTERRUPT_STATUS_CONTINUE19	Level / Pulse	
#define DCN_1_0__CTXID__AZ_ENDPOINT7_AUDIO_FMT_CHANGED_INT	9

#define DCN_1_0__SRCID__AZ_ENDPOINT0_AUDIO_ENABLED_INT	0xB	// AZ Endpoint0 enabled	AZ_IHC_ENDPOINT0_AUDIO_ENABLED_INT	DISP_INTERRUPT_STATUS_CONTINUE19	Level / Pulse	
#define DCN_1_0__CTXID__AZ_ENDPOINT0_AUDIO_ENABLED_INT	0

#define DCN_1_0__SRCID__AZ_ENDPOINT1_AUDIO_ENABLED_INT	0xB	// AZ Endpoint1 enabled	AZ_IHC_ENDPOINT1_AUDIO_ENABLED_INT	DISP_INTERRUPT_STATUS_CONTINUE19	Level / Pulse	
#define DCN_1_0__CTXID__AZ_ENDPOINT1_AUDIO_ENABLED_INT	1

#define DCN_1_0__SRCID__AZ_ENDPOINT2_AUDIO_ENABLED_INT	0xB	// AZ Endpoint2 enabled	AZ_IHC_ENDPOINT2_AUDIO_ENABLED_INT	DISP_INTERRUPT_STATUS_CONTINUE19	Level / Pulse	
#define DCN_1_0__CTXID__AZ_ENDPOINT2_AUDIO_ENABLED_INT	2

#define DCN_1_0__SRCID__AZ_ENDPOINT3_AUDIO_ENABLED_INT	0xB	// AZ Endpoint3 enabled	AZ_IHC_ENDPOINT3_AUDIO_ENABLED_INT	DISP_INTERRUPT_STATUS_CONTINUE19	Level / Pulse	
#define DCN_1_0__CTXID__AZ_ENDPOINT3_AUDIO_ENABLED_INT	3

#define DCN_1_0__SRCID__AZ_ENDPOINT4_AUDIO_ENABLED_INT	0xB	// AZ Endpoint4 enabled	AZ_IHC_ENDPOINT4_AUDIO_ENABLED_INT	DISP_INTERRUPT_STATUS_CONTINUE19	Level / Pulse	
#define DCN_1_0__CTXID__AZ_ENDPOINT4_AUDIO_ENABLED_INT	4

#define DCN_1_0__SRCID__AZ_ENDPOINT5_AUDIO_ENABLED_INT	0xB	// AZ Endpoint5 enabled	AZ_IHC_ENDPOINT5_AUDIO_ENABLED_INT	DISP_INTERRUPT_STATUS_CONTINUE19	Level / Pulse	
#define DCN_1_0__CTXID__AZ_ENDPOINT5_AUDIO_ENABLED_INT	5

#define DCN_1_0__SRCID__AZ_ENDPOINT6_AUDIO_ENABLED_INT	0xB	// AZ Endpoint6 enabled	AZ_IHC_ENDPOINT6_AUDIO_ENABLED_INT	DISP_INTERRUPT_STATUS_CONTINUE19	Level / Pulse	
#define DCN_1_0__CTXID__AZ_ENDPOINT6_AUDIO_ENABLED_INT	6

#define DCN_1_0__SRCID__AZ_ENDPOINT7_AUDIO_ENABLED_INT	0xB	// AZ Endpoint7 enabled	AZ_IHC_ENDPOINT7_AUDIO_ENABLED_INT	DISP_INTERRUPT_STATUS_CONTINUE19	Level / Pulse	
#define DCN_1_0__CTXID__AZ_ENDPOINT7_AUDIO_ENABLED_INT	7

#define DCN_1_0__SRCID__AZ_ENDPOINT0_AUDIO_DISABLED_INT	0xC	// AZ Endpoint0 disabled	AZ_IHC_ENDPOINT0_AUDIO_DISABLED_INT	DISP_INTERRUPT_STATUS_CONTINUE19	Level / Pulse	
#define DCN_1_0__CTXID__AZ_ENDPOINT0_AUDIO_DISABLED_INT	0

#define DCN_1_0__SRCID__AZ_ENDPOINT1_AUDIO_DISABLED_INT	0xC	// AZ Endpoint1 disabled	AZ_IHC_ENDPOINT1_AUDIO_DISABLED_INT	DISP_INTERRUPT_STATUS_CONTINUE19	Level / Pulse	
#define DCN_1_0__CTXID__AZ_ENDPOINT1_AUDIO_DISABLED_INT	1

#define DCN_1_0__SRCID__AZ_ENDPOINT2_AUDIO_DISABLED_INT	0xC	// AZ Endpoint2 disabled	AZ_IHC_ENDPOINT2_AUDIO_DISABLED_INT	DISP_INTERRUPT_STATUS_CONTINUE19	Level / Pulse	
#define DCN_1_0__CTXID__AZ_ENDPOINT2_AUDIO_DISABLED_INT	2

#define DCN_1_0__SRCID__AZ_ENDPOINT3_AUDIO_DISABLED_INT	0xC	// AZ Endpoint3 disabled	AZ_IHC_ENDPOINT3_AUDIO_DISABLED_INT	DISP_INTERRUPT_STATUS_CONTINUE19	Level / Pulse	
#define DCN_1_0__CTXID__AZ_ENDPOINT3_AUDIO_DISABLED_INT	3

#define DCN_1_0__SRCID__AZ_ENDPOINT4_AUDIO_DISABLED_INT	0xC	// AZ Endpoint4 disabled	AZ_IHC_ENDPOINT4_AUDIO_DISABLED_INT	DISP_INTERRUPT_STATUS_CONTINUE19	Level / Pulse	
#define DCN_1_0__CTXID__AZ_ENDPOINT4_AUDIO_DISABLED_INT	4

#define DCN_1_0__SRCID__AZ_ENDPOINT5_AUDIO_DISABLED_INT	0xC	// AZ Endpoint5 disabled	AZ_IHC_ENDPOINT5_AUDIO_DISABLED_INT	DISP_INTERRUPT_STATUS_CONTINUE19	Level / Pulse	
#define DCN_1_0__CTXID__AZ_ENDPOINT5_AUDIO_DISABLED_INT	5

#define DCN_1_0__SRCID__AZ_ENDPOINT6_AUDIO_DISABLED_INT	0xC	// AZ Endpoint6 disabled	AZ_IHC_ENDPOINT6_AUDIO_DISABLED_INT	DISP_INTERRUPT_STATUS_CONTINUE19	Level / Pulse	
#define DCN_1_0__CTXID__AZ_ENDPOINT6_AUDIO_DISABLED_INT	6

#define DCN_1_0__SRCID__AZ_ENDPOINT7_AUDIO_DISABLED_INT	0xC	// AZ Endpoint7 disabled	AZ_IHC_ENDPOINT7_AUDIO_DISABLED_INT	DISP_INTERRUPT_STATUS_CONTINUE19	Level / Pulse	
#define DCN_1_0__CTXID__AZ_ENDPOINT7_AUDIO_DISABLED_INT	7

#define DCN_1_0__SRCID__DC_AUX1_GTC_SYNC_LOCK_DONE	0xD	    // AUX1 GTC sync lock complete 	AUX1_GTC_SYNC_LOCK_DONE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE6	Level	
#define DCN_1_0__CTXID__DC_AUX1_GTC_SYNC_LOCK_DONE	0

#define DCN_1_0__SRCID__DC_AUX1_GTC_SYNC_ERROR	    0xD	    // AUX1 GTC sync error occurred	AUX1_GTC_SYNC_ERROR_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE6	Level	
#define DCN_1_0__CTXID__DC_AUX1_GTC_SYNC_ERROR	    1

#define DCN_1_0__SRCID__DC_AUX2_GTC_SYNC_LOCK_DONE	0xD	    // AUX2 GTC sync lock complete 	AUX2_GTC_SYNC_LOCK_DONE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE6	Level	
#define DCN_1_0__CTXID__DC_AUX2_GTC_SYNC_LOCK_DONE	2

#define DCN_1_0__SRCID__DC_AUX2_GTC_SYNC_ERROR	    0xD	    // AUX2 GTC sync error occurred	AUX2_GTC_SYNC_ERROR_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE6	Level	
#define DCN_1_0__CTXID__DC_AUX2_GTC_SYNC_ERROR	    3

#define DCN_1_0__SRCID__DC_AUX3_GTC_SYNC_LOCK_DONE	0xD	    // AUX3 GTC sync lock complete 	AUX3_GTC_SYNC_LOCK_DONE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE6	Level	
#define DCN_1_0__CTXID__DC_AUX3_GTC_SYNC_LOCK_DONE	4

#define DCN_1_0__SRCID__DC_AUX3_GTC_SYNC_ERROR	    0xD	    // AUX3 GTC sync error occurred	AUX3_GTC_SYNC_ERROR_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE6	Level	
#define DCN_1_0__CTXID__DC_AUX3_GTC_SYNC_ERROR	    5

#define DCN_1_0__SRCID__DC_DIGA_VID_STRM_DISABLE	    0xE	    // DIGA vid stream disable	DIGA_DP_VID_STREAM_DISABLE_INTERRUPT	DISP_INTERRUPT_STATUS	Level	
#define DCN_1_0__CTXID__DC_DIGA_VID_STRM_DISABLE	    0

#define DCN_1_0__SRCID__DC_DIGB_VID_STRM_DISABLE	    0xE	    // DIGB vid stream disable	DIGB_DP_VID_STREAM_DISABLE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE	Level	
#define DCN_1_0__CTXID__DC_DIGB_VID_STRM_DISABLE	    1

#define DCN_1_0__SRCID__DC_DIGC_VID_STRM_DISABLE	    0xE	    // DIGC vid stream disable	DIGC_DP_VID_STREAM_DISABLE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE2	Level	
#define DCN_1_0__CTXID__DC_DIGC_VID_STRM_DISABLE	    2

#define DCN_1_0__SRCID__DC_DIGD_VID_STRM_DISABLE	    0xE	    // DIGD vid stream disable	DIGD_DP_VID_STREAM_DISABLE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE3	Level	
#define DCN_1_0__CTXID__DC_DIGD_VID_STRM_DISABLE	    3

#define DCN_1_0__SRCID__DC_DIGE_VID_STRM_DISABLE	    0xE	    // DIGE vid stream disable	DIGE_DP_VID_STREAM_DISABLE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE4	Level	
#define DCN_1_0__CTXID__DC_DIGE_VID_STRM_DISABLE	    4

#define DCN_1_0__SRCID__DC_DIGF_VID_STRM_DISABLE	    0xE	    // DIGF vid stream disable	DIGF_DP_VID_STREAM_DISABLE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE5	Level	
#define DCN_1_0__CTXID__DC_DIGF_VID_STRM_DISABLE	    5

#define DCN_1_0__SRCID__DC_DIGG_VID_STRM_DISABLE	    0xE	    // DIGF vid stream disable	DIGG_DP_VID_STREAM_DISABLE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE19	Level	
#define DCN_1_0__CTXID__DC_DIGG_VID_STRM_DISABLE	    6

#define DCN_1_0__SRCID__DC_DIGH_VID_STRM_DISABLE	    0xE	    // DIGH_DP_VID_STREAM_DISABLE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE21	Level	
#define DCN_1_0__CTXID__DC_DIGH_VID_STRM_DISABLE	    7

#define DCN_1_0__SRCID__DC_DIGA_FAST_TRAINING_COMPLETE_INT	0xF	    // DIGA - Fast Training Complete	DIGA_DP_FAST_TRAINING_COMPLETE_INTERRUPT	DISP_INTERRUPT_STATUS	Level	
#define DCN_1_0__CTXID__DC_DIGA_FAST_TRAINING_COMPLETE_INT	0

#define DCN_1_0__SRCID__DC_DIGB_FAST_TRAINING_COMPLETE_INT	0xF	    // DIGB - Fast Training Complete	DIGB_DP_FAST_TRAINING_COMPLETE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE	Level	
#define DCN_1_0__CTXID__DC_DIGB_FAST_TRAINING_COMPLETE_INT	1

#define DCN_1_0__SRCID__DC_DIGC_FAST_TRAINING_COMPLETE_INT	0xF	    // DIGC - Fast Training Complete	DIGC_DP_FAST_TRAINING_COMPLETE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE2	Level	
#define DCN_1_0__CTXID__DC_DIGC_FAST_TRAINING_COMPLETE_INT	2

#define DCN_1_0__SRCID__DC_DIGD_FAST_TRAINING_COMPLETE_INT	0xF	    // DIGD - Fast Training Complete	DIGD_DP_FAST_TRAINING_COMPLETE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE3	Level	
#define DCN_1_0__CTXID__DC_DIGD_FAST_TRAINING_COMPLETE_INT	3

#define DCN_1_0__SRCID__DC_DIGE_FAST_TRAINING_COMPLETE_INT	0xF	    // DIGE - Fast Training Complete	DIGE_DP_FAST_TRAINING_COMPLETE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE4	Level	
#define DCN_1_0__CTXID__DC_DIGE_FAST_TRAINING_COMPLETE_INT	4

#define DCN_1_0__SRCID__DC_DIGF_FAST_TRAINING_COMPLETE_INT	0xF	    // DIGF - Fast Training Complete	DIGF_DP_FAST_TRAINING_COMPLETE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE5	Level	
#define DCN_1_0__CTXID__DC_DIGF_FAST_TRAINING_COMPLETE_INT	5

#define DCN_1_0__SRCID__DC_DIGG_FAST_TRAINING_COMPLETE_INT	0xF	    // DIGG_DP_FAST_TRAINING_COMPLETE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE19	Level	
#define DCN_1_0__CTXID__DC_DIGG_FAST_TRAINING_COMPLETE_INT	6

#define DCN_1_0__SRCID__DC_DIGH_FAST_TRAINING_COMPLETE_INT	0xF	    // DIGH_DP_FAST_TRAINING_COMPLETE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE21	Level	
#define DCN_1_0__CTXID__DC_DIGH_FAST_TRAINING_COMPLETE_INT	7

#define DCN_1_0__SRCID__DC_AUX1_SW_DONE	                0x10	// AUX1 sw done	AUX1_SW_DONE_INTERRUPT	DISP_INTERRUPT_STATUS	Level	
#define DCN_1_0__CTXID__DC_AUX1_SW_DONE	                0

#define DCN_1_0__SRCID__DC_AUX1_LS_DONE	                0x10	// AUX1 ls done	AUX1_LS_DONE_INTERRUPT	DISP_INTERRUPT_STATUS	Level	
#define DCN_1_0__CTXID__DC_AUX1_LS_DONE	                1

#define DCN_1_0__SRCID__DC_AUX2_SW_DONE	                0x10	// AUX2 sw done	AUX2_SW_DONE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE	Level	
#define DCN_1_0__CTXID__DC_AUX2_SW_DONE	                2

#define DCN_1_0__SRCID__DC_AUX2_LS_DONE	                0x10	// AUX2 ls done	AUX2_LS_DONE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE	Level	
#define DCN_1_0__CTXID__DC_AUX2_LS_DONE	                3

#define DCN_1_0__SRCID__DC_AUX3_SW_DONE	                0x10	// AUX3 sw done	AUX3_SW_DONE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE2	Level	
#define DCN_1_0__CTXID__DC_AUX3_SW_DONE	                4

#define DCN_1_0__SRCID__DC_AUX3_LS_DONE	                0x10	// AUX3 ls done	AUX3_LS_DONE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE2	Level	
#define DCN_1_0__CTXID__DC_AUX3_LS_DONE	                5

#define DCN_1_0__SRCID__DC_AUX4_SW_DONE	                0x10	// AUX4 sw done	AUX4_SW_DONE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE3	Level	
#define DCN_1_0__CTXID__DC_AUX4_SW_DONE	                6

#define DCN_1_0__SRCID__DC_AUX4_LS_DONE	                0x10	// AUX4 ls done	AUX4_LS_DONE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE3	Level	
#define DCN_1_0__CTXID__DC_AUX4_LS_DONE	                7

#define DCN_1_0__SRCID__DC_AUX5_SW_DONE	                0x10	// AUX5 sw done	AUX5_SW_DONE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE4	Level	
#define DCN_1_0__CTXID__DC_AUX5_SW_DONE	                8

#define DCN_1_0__SRCID__DC_AUX5_LS_DONE	                0x10	// AUX5 ls done	AUX5_LS_DONE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE4	Level	
#define DCN_1_0__CTXID__DC_AUX5_LS_DONE	                9

#define DCN_1_0__SRCID__DC_AUX6_SW_DONE	                0x10	// AUX6 sw done	AUX6_SW_DONE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE5	Level	
#define DCN_1_0__CTXID__DC_AUX6_SW_DONE	                10

#define DCN_1_0__SRCID__DC_AUX6_LS_DONE	                0x10	// AUX6 ls done	AUX6_LS_DONE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE5	Level	
#define DCN_1_0__CTXID__DC_AUX6_LS_DONE	                11

#define DCN_1_0__SRCID__VGA_CRT_INT	                    0x10	// VGA Vblank 	VGA_IHC_VGA_CRT_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE11	Level	
#define DCN_1_0__CTXID__VGA_CRT_INT	                    12

#define DCN_1_0__SRCID__DCCG_PERFCOUNTER2_INT0_STATUS	0x11	// DCCG perfmon2 counter0 interrupt	DCCG_PERFMON2_COUNTER0_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE10	Level / Pulse	
#define DCN_1_0__CTXID__DCCG_PERFCOUNTER2_INT0_STATUS	0

#define DCN_1_0__SRCID__DCCG_PERFCOUNTER2_INT1_STATUS	0x11	// DCCG perfmon2 counter1 interrupt	DCCG_PERFMON2_COUNTER1_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE10	Level	
#define DCN_1_0__CTXID__DCCG_PERFCOUNTER2_INT1_STATUS	1

#define DCN_1_0__SRCID__BUFMGR_CWB0_IHIF_interrupt	    0x12	// mcif_wb_client(buffer manager)	MCIF_CWB0_IHIF_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE6	Level	
#define DCN_1_0__CTXID__BUFMGR_CWB0_IHIF_interrupt	    0

#define DCN_1_0__SRCID__BUFMGR_CWB1_IHIF_interrupt	    0x12	// mcif_wb_client(buffer manager)	MCIF_CWB1_IHIF_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE6	Level	
#define DCN_1_0__CTXID__BUFMGR_CWB1_IHIF_interrupt	    1

#define DCN_1_0__SRCID__MCIF0_BUFMGR_SW_CONTROL_MCIF_BUFMGR_SW_INT	0x12	// MCIF WB client(buffer manager)	MCIF_DWB0_IHIF_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE6	Level	
#define DCN_1_0__CTXID__MCIF0_BUFMGR_SW_CONTROL_MCIF_BUFMGR_SW_INT	2

#define DCN_1_0__SRCID__MCIF1_BUFMGR_SW_CONTROL_MCIF_BUFMGR_SW_INT	0x12	// MCIF WB client(buffer manager)	MCIF_DWB1_IHIF_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE6	Level	
#define DCN_1_0__CTXID__MCIF1_BUFMGR_SW_CONTROL_MCIF_BUFMGR_SW_INT	3

#define DCN_1_0__SRCID__SISCL0_COEF_RAM_CONFLICT_STATUS	            0x12	// WB host conflict interrupt	WBSCL0_HOST_CONFLICT_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE3	Level	
#define DCN_1_0__CTXID__SISCL0_COEF_RAM_CONFLICT_STATUS	            4

#define DCN_1_0__SRCID__SISCL0_OVERFLOW_STATUS	        0x12	// WB data overflow interrupt	WBSCL0_DATA_OVERFLOW_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE3	Level	
#define DCN_1_0__CTXID__SISCL0_OVERFLOW_STATUS	        5

#define DCN_1_0__SRCID__SISCL1_COEF_RAM_CONFLICT_STATUS	0x12	// WB host conflict interrupt	WBSCL1_HOST_CONFLICT_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE9	Level	
#define DCN_1_0__CTXID__SISCL1_COEF_RAM_CONFLICT_STATUS	6

#define DCN_1_0__SRCID__SISCL1_OVERFLOW_STATUS	        0x12	// WB data overflow interrupt	WBSCL1_DATA_OVERFLOW_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE9	Level	
#define DCN_1_0__CTXID__SISCL1_OVERFLOW_STATUS	        7

#define DCN_1_0__SRCID__DC_AUX4_GTC_SYNC_LOCK_DONE	    0x13	// AUX4 GTC sync lock complete 	AUX4_GTC_SYNC_LOCK_DONE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE6	Level
#define DCN_1_0__CTXID__DC_AUX4_GTC_SYNC_LOCK_DONE	    0

#define DCN_1_0__SRCID__DC_AUX4_GTC_SYNC_ERROR	        0x13	// AUX4 GTC sync error occurred	AUX4_GTC_SYNC_ERROR_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE6	Level	
#define DCN_1_0__CTXID__DC_AUX4_GTC_SYNC_ERROR	        1

#define DCN_1_0__SRCID__DC_AUX5_GTC_SYNC_LOCK_DONE	    0x13	// AUX5 GTC sync lock complete 	AUX5_GTC_SYNC_LOCK_DONE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE6	Level	
#define DCN_1_0__CTXID__DC_AUX5_GTC_SYNC_LOCK_DONE	    2

#define DCN_1_0__SRCID__DC_AUX5_GTC_SYNC_ERROR	        0x13	// AUX5 GTC sync error occurred	AUX5_GTC_SYNC_ERROR_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE6	Level	
#define DCN_1_0__CTXID__DC_AUX5_GTC_SYNC_ERROR	        3

#define DCN_1_0__SRCID__DC_AUX6_GTC_SYNC_LOCK_DONE	    0x13	// AUX6 GTC sync lock complete 	AUX6_GTC_SYNC_LOCK_DONE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE6	Level	
#define DCN_1_0__CTXID__DC_AUX6_GTC_SYNC_LOCK_DONE	    4

#define DCN_1_0__SRCID__DC_AUX6_GTC_SYNC_ERROR	        0x13	// AUX6 GTC sync error occurred	AUX6_GTC_SYNC_ERROR_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE6	Level	
#define DCN_1_0__CTXID__DC_AUX6_GTC_SYNC_ERROR	        5

#define DCN_1_0__SRCID__DCPG_DCFE0_POWER_UP_INT	        0x14	// Display pipe0 power up interrupt 	DCPG_IHC_DOMAIN0_POWER_UP_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE13	Level	
#define DCN_1_0__CTXID__DCPG_DCFE0_POWER_UP_INT	        0

#define DCN_1_0__SRCID__DCPG_DCFE1_POWER_UP_INT	        0x14	// Display pipe1 power up interrupt 	DCPG_IHC_DOMAIN1_POWER_UP_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE13	Level	
#define DCN_1_0__CTXID__DCPG_DCFE1_POWER_UP_INT	        1

#define DCN_1_0__SRCID__DCPG_DCFE2_POWER_UP_INT	        0x14	// Display pipe2 power up interrupt 	DCPG_IHC_DOMAIN2_POWER_UP_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE13	Level	
#define DCN_1_0__CTXID__DCPG_DCFE2_POWER_UP_INT	        2

#define DCN_1_0__SRCID__DCPG_DCFE3_POWER_UP_INT	        0x14	// Display pipe3 power up interrupt 	DCPG_IHC_DOMAIN3_POWER_UP_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE13	Level	
#define DCN_1_0__CTXID__DCPG_DCFE3_POWER_UP_INT	        3

#define DCN_1_0__SRCID__DCPG_DCFE4_POWER_UP_INT	        0x14	// Display pipe4 power up interrupt 	DCPG_IHC_DOMAIN4_POWER_UP_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE13	Level	
#define DCN_1_0__CTXID__DCPG_DCFE4_POWER_UP_INT	        4

#define DCN_1_0__SRCID__DCPG_DCFE5_POWER_UP_INT	        0x14	// Display pipe5 power up interrupt 	DCPG_IHC_DOMAIN5_POWER_UP_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE13	Level	
#define DCN_1_0__CTXID__DCPG_DCFE5_POWER_UP_INT	        5

#define DCN_1_0__SRCID__DCPG_DCFE6_POWER_UP_INT	        0x14	// Display pipe6 power up interrupt 	DCPG_IHC_DOMAIN6_POWER_UP_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE13	Level	
#define DCN_1_0__CTXID__DCPG_DCFE6_POWER_UP_INT	        6

#define DCN_1_0__SRCID__DCPG_DCFE7_POWER_UP_INT	        0x14	// Display pipe7 power up interrupt 	DCPG_IHC_DOMAIN7_POWER_UP_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE13	Level	
#define DCN_1_0__CTXID__DCPG_DCFE7_POWER_UP_INT	        7

#define DCN_1_0__SRCID__DCPG_DCFE0_POWER_DOWN_INT	    0x14	// Display pipe0 power down interrupt 	DCPG_IHC_DOMAIN0_POWER_DOWN_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE18	Level	
#define DCN_1_0__CTXID__DCPG_DCFE0_POWER_DOWN_INT	    8

#define DCN_1_0__SRCID__DCPG_DCFE1_POWER_DOWN_INT	    0x14	// Display pipe1 power down interrupt 	DCPG_IHC_DOMAIN1_POWER_DOWN_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE18	Level	
#define DCN_1_0__CTXID__DCPG_DCFE1_POWER_DOWN_INT	    9

#define DCN_1_0__SRCID__DCPG_DCFE2_POWER_DOWN_INT	    0x14	// Display pipe2 power down interrupt 	DCPG_IHC_DOMAIN2_POWER_DOWN_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE18	Level	
#define DCN_1_0__CTXID__DCPG_DCFE2_POWER_DOWN_INT	    10

#define DCN_1_0__SRCID__DCPG_DCFE3_POWER_DOWN_INT   	0x14	// Display pipe3 power down interrupt 	DCPG_IHC_DOMAIN3_POWER_DOWN_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE18	Level	
#define DCN_1_0__CTXID__DCPG_DCFE3_POWER_DOWN_INT	    11

#define DCN_1_0__SRCID__DCPG_DCFE4_POWER_DOWN_INT	    0x14	// Display pipe4 power down interrupt 	DCPG_IHC_DOMAIN4_POWER_DOWN_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE18	Level	
#define DCN_1_0__CTXID__DCPG_DCFE4_POWER_DOWN_INT	    12

#define DCN_1_0__SRCID__DCPG_DCFE5_POWER_DOWN_INT	    0x14	// Display pipe5 power down interrupt 	DCPG_IHC_DOMAIN5_POWER_DOWN_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE18	Level	
#define DCN_1_0__CTXID__DCPG_DCFE5_POWER_DOWN_INT	    13

#define DCN_1_0__SRCID__DCPG_DCFE6_POWER_DOWN_INT	    0x14	// Display pipe6 power down interrupt 	DCPG_IHC_DOMAIN6_POWER_DOWN_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE18	Level	
#define DCN_1_0__CTXID__DCPG_DCFE6_POWER_DOWN_INT	    14

#define DCN_1_0__SRCID__DCPG_DCFE7_POWER_DOWN_INT	    0x14	// Display pipe7 power down interrupt 	DCPG_IHC_DOMAIN7_POWER_DOWN_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE18	Level	
#define DCN_1_0__CTXID__DCPG_DCFE7_POWER_DOWN_INT	    15

#define DCN_1_0__SRCID__DCCG_IHC_VSYNC_otg0_latch_int	0x15	// an interrupt that is triggered when the time(number of refclk cycles) of a programmable number of frames is counted.The counting starts / end at VSYNC rising edge or falling edge.DCCG_IHC_VSYNC_OTG0_LATCH_INT	DISP_INTERRUPT_STATUS_CONTINUE10	Level	
#define DCN_1_0__CTXID__DCCG_IHC_VSYNC_otg0_latch_int	0

#define DCN_1_0__SRCID__DCCG_IHC_VSYNC_otg1_latch_int	0x15	// an interrupt that is triggered when the time(number of refclk cycles) of a programmable number of frames is counted.The counting starts / end at VSYNC rising edge or falling edge.DCCG_IHC_VSYNC_OTG1_LATCH_INT	DISP_INTERRUPT_STATUS_CONTINUE10	Level	
#define DCN_1_0__CTXID__DCCG_IHC_VSYNC_otg1_latch_int	1

#define DCN_1_0__SRCID__DCCG_IHC_VSYNC_otg2_latch_int	0x15	// an interrupt that is triggered when the time(number of refclk cycles) of a programmable number of frames is counted.The counting starts / end at VSYNC rising edge or falling edge.DCCG_IHC_VSYNC_OTG2_LATCH_INT	DISP_INTERRUPT_STATUS_CONTINUE10	Level	
#define DCN_1_0__CTXID__DCCG_IHC_VSYNC_otg2_latch_int	2

#define DCN_1_0__SRCID__DCCG_IHC_VSYNC_otg3_latch_int	0x15	// an interrupt that is triggered when the time(number of refclk cycles) of a programmable number of frames is counted.The counting starts / end at VSYNC rising edge or falling edge.DCCG_IHC_VSYNC_OTG3_LATCH_INT	DISP_INTERRUPT_STATUS_CONTINUE10	Level	
#define DCN_1_0__CTXID__DCCG_IHC_VSYNC_otg3_latch_int	3

#define DCN_1_0__SRCID__DCCG_IHC_VSYNC_otg4_latch_int	0x15	// an interrupt that is triggered when the time(number of refclk cycles) of a programmable number of frames is counted.The counting starts / end at VSYNC rising edge or falling edge.DCCG_IHC_VSYNC_OTG4_LATCH_INT	DISP_INTERRUPT_STATUS_CONTINUE10	Level	
#define DCN_1_0__CTXID__DCCG_IHC_VSYNC_otg4_latch_int	4

#define DCN_1_0__SRCID__DCCG_IHC_VSYNC_otg5_latch_int	0x15	// an interrupt that is triggered when the time(number of refclk cycles) of a programmable number of frames is counted.The counting starts / end at VSYNC rising edge or falling edge.DCCG_IHC_VSYNC_OTG5_LATCH_INT	DISP_INTERRUPT_STATUS_CONTINUE10	Level	
#define DCN_1_0__CTXID__DCCG_IHC_VSYNC_otg5_latch_int	5

#define DCN_1_0__SRCID__OPTC0_DATA_UNDERFLOW_INT	    0x15	// D0 ODM data underflow interrupt 	OPTC1_DATA_UNDERFLOW_INTERRUPT	DISP_INTERRUPT_STATUS	Level	
#define DCN_1_0__CTXID__OPTC0_DATA_UNDERFLOW_INT	    6

#define DCN_1_0__SRCID__OPTC1_DATA_UNDERFLOW_INT	    0x15	// D0 ODM data underflow interrupt 	OPTC2_DATA_UNDERFLOW_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE	Level	
#define DCN_1_0__CTXID__OPTC1_DATA_UNDERFLOW_INT	    7

#define DCN_1_0__SRCID__OPTC2_DATA_UNDERFLOW_INT	    0x15	// D0 ODM data underflow interrupt 	OPTC3_DATA_UNDERFLOW_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE2	Level	
#define DCN_1_0__CTXID__OPTC2_DATA_UNDERFLOW_INT	    8

#define DCN_1_0__SRCID__OPTC3_DATA_UNDERFLOW_INT	    0x15	// D0 ODM data underflow interrupt 	OPTC4_DATA_UNDERFLOW_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE3	Level	
#define DCN_1_0__CTXID__OPTC3_DATA_UNDERFLOW_INT	    9

#define DCN_1_0__SRCID__OPTC4_DATA_UNDERFLOW_INT	    0x15	// D0 ODM data underflow interrupt 	OPTC5_DATA_UNDERFLOW_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE4	Level	
#define DCN_1_0__CTXID__OPTC4_DATA_UNDERFLOW_INT	    10

#define DCN_1_0__SRCID__OPTC5_DATA_UNDERFLOW_INT	    0x15	// D0 ODM data underflow interrupt 	OPTC6_DATA_UNDERFLOW_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE4	Level	
#define DCN_1_0__CTXID__OPTC5_DATA_UNDERFLOW_INT	    11

#define DCN_1_0__SRCID__MPCC0_STALL_INTERRUPT	        0x16	// Indicate no pixel was available to be sent when OPP asked for	MPCC0_STALL_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE11	Level	
#define DCN_1_0__CTXID__MPCC0_STALL_INTERRUPT	        0

#define DCN_1_0__SRCID__MPCC1_STALL_INTERRUPT	        0x16	// Indicate no pixel was available to be sent when OPP asked for	MPCC1_STALL_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE11	Level	
#define DCN_1_0__CTXID__MPCC1_STALL_INTERRUPT	        1

#define DCN_1_0__SRCID__MPCC2_STALL_INTERRUPT	        0x16	// Indicate no pixel was available to be sent when OPP asked for	MPCC2_STALL_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE11	Level	
#define DCN_1_0__CTXID__MPCC2_STALL_INTERRUPT	        2

#define DCN_1_0__SRCID__MPCC3_STALL_INTERRUPT	        0x16	// Indicate no pixel was available to be sent when OPP asked for	MPCC3_STALL_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE11	Level	
#define DCN_1_0__CTXID__MPCC3_STALL_INTERRUPT	        3

#define DCN_1_0__SRCID__MPCC4_STALL_INTERRUPT	        0x16	// Indicate no pixel was available to be sent when OPP asked for	MPCC4_STALL_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE11	Level	
#define DCN_1_0__CTXID__MPCC4_STALL_INTERRUPT	        4

#define DCN_1_0__SRCID__MPCC5_STALL_INTERRUPT	        0x16	// Indicate no pixel was available to be sent when OPP asked for	MPCC5_STALL_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE11	Level	
#define DCN_1_0__CTXID__MPCC5_STALL_INTERRUPT	        5

#define DCN_1_0__SRCID__MPCC6_STALL_INTERRUPT	        0x16	// Indicate no pixel was available to be sent when OPP asked for	MPCC6_STALL_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE11	Level	
#define DCN_1_0__CTXID__MPCC6_STALL_INTERRUPT	        6

#define DCN_1_0__SRCID__MPCC7_STALL_INTERRUPT	        0x16	// Indicate no pixel was available to be sent when OPP asked for	MPCC7_STALL_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE11	Level	
#define DCN_1_0__CTXID__MPCC7_STALL_INTERRUPT	        7

#define DCN_1_0__SRCID__OTG1_CPU_SS_INT	                0x17	// D1: OTG Static Screen interrupt	OTG1_IHC_CPU_SS_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE20	Level / Pulse	
#define DCN_1_0__CTXID__OTG1_CPU_SS_INT	                0

#define DCN_1_0__SRCID__OTG1_RANGE_TIMING_UPDATE	    0x17	// D1 : OTG range timing	OTG1_IHC_RANGE_TIMING_UPDATE	DISP_INTERRUPT_STATUS_CONTINUE10	Level / Pulse	
#define DCN_1_0__CTXID__OTG1_RANGE_TIMING_UPDATE	    1

#define DCN_1_0__SRCID__OTG2_CPU_SS_INT	0x17	// D2 : OTG Static Screen interrupt	OTG2_IHC_CPU_SS_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE20	Level / Pulse	
#define DCN_1_0__CTXID__OTG2_CPU_SS_INT	2

#define DCN_1_0__SRCID__OTG2_RANGE_TIMING_UPDATE	0x17	// D2 : OTG range timing	OTG2_IHC_RANGE_TIMING_UPDATE	DISP_INTERRUPT_STATUS_CONTINUE10	Level / Pulse	
#define DCN_1_0__CTXID__OTG2_RANGE_TIMING_UPDATE	3

#define DCN_1_0__SRCID__OTG3_CPU_SS_INT	0x17	// D3 : OTG Static Screen interrupt	OTG3_IHC_CPU_SS_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE20	Level / Pulse	
#define DCN_1_0__CTXID__OTG3_CPU_SS_INT	4

#define DCN_1_0__SRCID__OTG3_RANGE_TIMING_UPDATE	0x17	// D3 : OTG range timing	OTG3_IHC_RANGE_TIMING_UPDATE	DISP_INTERRUPT_STATUS_CONTINUE10	Level / Pulse	
#define DCN_1_0__CTXID__OTG3_RANGE_TIMING_UPDATE	5

#define DCN_1_0__SRCID__OTG4_CPU_SS_INT	0x17	// D4 : OTG Static Screen interrupt	OTG4_IHC_CPU_SS_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE20	Level / Pulse	
#define DCN_1_0__CTXID__OTG4_CPU_SS_INT	6

#define DCN_1_0__SRCID__OTG4_RANGE_TIMING_UPDATE	0x17	// D4 : OTG range timing	OTG4_IHC_RANGE_TIMING_UPDATE	DISP_INTERRUPT_STATUS_CONTINUE10	Level / Pulse	
#define DCN_1_0__CTXID__OTG4_RANGE_TIMING_UPDATE	7

#define DCN_1_0__SRCID__OTG5_CPU_SS_INT	0x17	// D5 : OTG Static Screen interrupt	OTG5_IHC_CPU_SS_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE20	Level / Pulse	
#define DCN_1_0__CTXID__OTG5_CPU_SS_INT	8

#define DCN_1_0__SRCID__OTG5_RANGE_TIMING_UPDATE	0x17	// D5 : OTG range timing	OTG5_IHC_RANGE_TIMING_UPDATE	DISP_INTERRUPT_STATUS_CONTINUE10	Level / Pulse	
#define DCN_1_0__CTXID__OTG5_RANGE_TIMING_UPDATE	9

#define DCN_1_0__SRCID__OTG6_CPU_SS_INT	0x17	// D6 : OTG Static Screen interrupt	OTG6_IHC_CPU_SS_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE20	Level / Pulse	
#define DCN_1_0__CTXID__OTG6_CPU_SS_INT	10

#define DCN_1_0__SRCID__OTG6_RANGE_TIMING_UPDATE	0x17	// D6 : OTG range timing	OTG6_IHC_RANGE_TIMING_UPDATE	DISP_INTERRUPT_STATUS_CONTINUE10	Level / Pulse	
#define DCN_1_0__CTXID__OTG6_RANGE_TIMING_UPDATE	11

#define DCN_1_0__SRCID__DC_D1_OTG_V_UPDATE	0x18	// D1 : OTG V_update	OTG1_IHC_V_UPDATE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE20	Level / Pulse
#define DCN_1_0__SRCID__DC_D2_OTG_V_UPDATE	0x19	// D2 : OTG V_update	OTG2_IHC_V_UPDATE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE20	Level / Pulse
#define DCN_1_0__SRCID__DC_D3_OTG_V_UPDATE	0x1A	// D3 : OTG V_update	OTG3_IHC_V_UPDATE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE20	Level / Pulse
#define DCN_1_0__SRCID__DC_D4_OTG_V_UPDATE	0x1B	// D4 : OTG V_update	OTG4_IHC_V_UPDATE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE20	Level / Pulse
#define DCN_1_0__SRCID__DC_D5_OTG_V_UPDATE	0x1C	// D5 : OTG V_update	OTG5_IHC_V_UPDATE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE20	Level / Pulse
#define DCN_1_0__SRCID__DC_D6_OTG_V_UPDATE	0x1D	// D6 : OTG V_update	OTG6_IHC_V_UPDATE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE20	Level / Pulse

#define DCN_1_0__SRCID__DC_D1_OTG_SNAPSHOT	0x1E	// D1 : OTG snapshot	OTG1_IHC_SNAPSHOT_INTERRUPT	DISP_INTERRUPT_STATUS	Level / Pulse	
#define DCN_1_0__CTXID__DC_D1_OTG_SNAPSHOT	0

#define DCN_1_0__SRCID__DC_D1_FORCE_CNT_W	0x1E	// D1 : Force - count--w	OTG1_IHC_FORCE_COUNT_NOW_INTERRUPT	DISP_INTERRUPT_STATUS	Level / Pulse	
#define DCN_1_0__CTXID__DC_D1_FORCE_CNT_W	1

#define DCN_1_0__SRCID__DC_D1_FORCE_VSYNC_NXT_LINE	0x1E	// D1 : Force - Vsync - next - line	OTG1_IHC_FORCE_VSYNC_NEXT_LINE_INTERRUPT	DISP_INTERRUPT_STATUS	Level / Pulse	
#define DCN_1_0__CTXID__DC_D1_FORCE_VSYNC_NXT_LINE	2

#define DCN_1_0__SRCID__DC_D1_OTG_EXTT_TRG_A	0x1E	// D1 : OTG external trigger A	OTG1_IHC_TRIGA_INTERRUPT	DISP_INTERRUPT_STATUS	Level / Pulse	
#define DCN_1_0__CTXID__DC_D1_OTG_EXTT_TRG_A	3

#define DCN_1_0__SRCID__DC_D1_OTG_EXTT_TRG_B	0x1E	// D1 : OTG external trigger B	OTG1_IHC_TRIGB_INTERRUPT	DISP_INTERRUPT_STATUS	Level / Pulse	
#define DCN_1_0__CTXID__DC_D1_OTG_EXTT_TRG_B	4

#define DCN_1_0__SRCID__DC_D1_OTG_GSL_VSYNC_GAP	0x1E	// D1 : gsl_vsync_gap_interrupt_frame_delay	OTG1_IHC_GSL_VSYNC_GAP_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE20	Level / Pulse	
#define DCN_1_0__CTXID__DC_D1_OTG_GSL_VSYNC_GAP	5

#define DCN_1_0__SRCID__OTG1_VERTICAL_INTERRUPT0_CONTROL	0x1E	// D1 : OTG vertical interrupt 0	OTG1_IHC_VERTICAL_INTERRUPT0	DISP_INTERRUPT_STATUS_CONTINUE	Level / Pulse	
#define DCN_1_0__CTXID__OTG1_VERTICAL_INTERRUPT0_CONTROL	6

#define DCN_1_0__SRCID__OTG1_VERTICAL_INTERRUPT1_CONTROL	0x1E	// D1 : OTG vertical interrupt 1	OTG1_IHC_VERTICAL_INTERRUPT1	DISP_INTERRUPT_STATUS_CONTINUE	Level / Pulse	
#define DCN_1_0__CTXID__OTG1_VERTICAL_INTERRUPT1_CONTROL	7

#define DCN_1_0__SRCID__OTG1_VERTICAL_INTERRUPT2_CONTROL	0x1E	// D1 : OTG vertical interrupt 2	OTG1_IHC_VERTICAL_INTERRUPT2	DISP_INTERRUPT_STATUS_CONTINUE	Level / Pulse	
#define DCN_1_0__CTXID__OTG1_VERTICAL_INTERRUPT2_CONTROL	8

#define DCN_1_0__SRCID__OTG1_EXT_TIMING_SYNC_LOSS_INTERRUPT_CONTROL	0x1E	// D1 : OTG ext sync loss interrupt	OTG1_IHC_EXT_TIMING_SYNC_LOSS_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE	Level / Pulse	
#define DCN_1_0__CTXID__OTG1_EXT_TIMING_SYNC_LOSS_INTERRUPT_CONTROL	9

#define DCN_1_0__SRCID__OTG1_EXT_TIMING_SYNC_INTERRUPT_CONTROL	0x1E	// D1 : OTG ext sync interrupt	OTG1_IHC_EXT_TIMING_SYNC_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE	Level / Pulse	
#define DCN_1_0__CTXID__OTG1_EXT_TIMING_SYNC_INTERRUPT_CONTROL	10

#define DCN_1_0__SRCID__OTG1_EXT_TIMING_SYNC_SIGNAL_INTERRUPT_CONTROL	0x1E	// D1 : OTG ext sync signal interrupt	OTG1_IHC_EXT_TIMING_SYNC_SIGNAL_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE	Level / Pulse	
#define DCN_1_0__CTXID__OTG1_EXT_TIMING_SYNC_SIGNAL_INTERRUPT_CONTROL	11

#define DCN_1_0__SRCID__OTG1_SET_VTOTAL_MIN_EVENT_INT	0x1E	// D1 : OTG DRR event occurred interrupt	OTG1_IHC_SET_V_TOTAL_MIN_EVENT_OCCURED_INT	DISP_INTERRUPT_STATUS	Level / Pulse	
#define DCN_1_0__CTXID__OTG1_SET_VTOTAL_MIN_EVENT_INT	12

#define DCN_1_0__SRCID__DC_D2_OTG_SNAPSHOT	0x1F	// D2 : OTG snapshot	OTG2_IHC_SNAPSHOT_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE	Level / Pulse	
#define DCN_1_0__CTXID__DC_D2_OTG_SNAPSHOT	0

#define DCN_1_0__SRCID__DC_D2_FORCE_CNT_W	0x1F	// D2 : Force - count--w	OTG2_IHC_FORCE_COUNT_NOW_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE	Level / Pulse	
#define DCN_1_0__CTXID__DC_D2_FORCE_CNT_W	1

#define DCN_1_0__SRCID__DC_D2_FORCE_VSYNC_NXT_LINE	0x1F	// D2 : Force - Vsync - next - line	OTG2_IHC_FORCE_VSYNC_NEXT_LINE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE	Level / Pulse	
#define DCN_1_0__CTXID__DC_D2_FORCE_VSYNC_NXT_LINE	2

#define DCN_1_0__SRCID__DC_D2_OTG_EXTT_TRG_A	0x1F	// D2 : OTG external trigger A	OTG2_IHC_TRIGA_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE	Level / Pulse	
#define DCN_1_0__CTXID__DC_D2_OTG_EXTT_TRG_A	3

#define DCN_1_0__SRCID__DC_D2_OTG_EXTT_TRG_B	0x1F	// D2 : OTG external trigger B	OTG2_IHC_TRIGB_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE	Level / Pulse	
#define DCN_1_0__CTXID__DC_D2_OTG_EXTT_TRG_B	4

#define DCN_1_0__SRCID__DC_D2_OTG_GSL_VSYNC_GAP	0x1F	// D2 : gsl_vsync_gap_interrupt_frame_delay	OTG2_IHC_GSL_VSYNC_GAP_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE20	Level / Pulse	
#define DCN_1_0__CTXID__DC_D2_OTG_GSL_VSYNC_GAP	5

#define DCN_1_0__SRCID__OTG2_VERTICAL_INTERRUPT0_CONTROL	0x1F	// D2 : OTG vertical interrupt 0	OTG2_IHC_VERTICAL_INTERRUPT0	DISP_INTERRUPT_STATUS_CONTINUE2	Level / Pulse	
#define DCN_1_0__CTXID__OTG2_VERTICAL_INTERRUPT0_CONTROL	6

#define DCN_1_0__SRCID__OTG2_VERTICAL_INTERRUPT1_CONTROL	0x1F	// D2 : OTG vertical interrupt 1	OTG2_IHC_VERTICAL_INTERRUPT1	DISP_INTERRUPT_STATUS_CONTINUE2	Level / Pulse	
#define DCN_1_0__CTXID__OTG2_VERTICAL_INTERRUPT1_CONTROL	7

#define DCN_1_0__SRCID__OTG2_VERTICAL_INTERRUPT2_CONTROL	0x1F	// D2 : OTG vertical interrupt 2	OTG2_IHC_VERTICAL_INTERRUPT2	DISP_INTERRUPT_STATUS_CONTINUE2	Level / Pulse	
#define DCN_1_0__CTXID__OTG2_VERTICAL_INTERRUPT2_CONTROL	8

#define DCN_1_0__SRCID__OTG2_EXT_TIMING_SYNC_LOSS_INTERRUPT_CONTROL	0x1F	// D2 : OTG ext sync loss interrupt	OTG2_IHC_EXT_TIMING_SYNC_LOSS_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE2	Level / Pulse	
#define DCN_1_0__CTXID__OTG2_EXT_TIMING_SYNC_LOSS_INTERRUPT_CONTROL	9

#define DCN_1_0__SRCID__OTG2_EXT_TIMING_SYNC_INTERRUPT_CONTROL	0x1F	// D2 : OTG ext sync interrupt	OTG2_IHC_EXT_TIMING_SYNC_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE2	Level / Pulse	
#define DCN_1_0__CTXID__OTG2_EXT_TIMING_SYNC_INTERRUPT_CONTROL	10

#define DCN_1_0__SRCID__OTG2_EXT_TIMING_SYNC_SIGNAL_INTERRUPT_CONTROL	0x1F	// D2 : OTG ext sync signal interrupt	OTG2_IHC_EXT_TIMING_SYNC_SIGNAL_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE2	Level / Pulse	
#define DCN_1_0__CTXID__OTG2_EXT_TIMING_SYNC_SIGNAL_INTERRUPT_CONTROL	11

#define DCN_1_0__SRCID__OTG2_SET_VTOTAL_MIN_EVENT_INT	0x1F	// D2 : OTG DRR event occurred interrupt	OTG2_IHC_SET_V_TOTAL_MIN_EVENT_OCCURED_INT	DISP_INTERRUPT_STATUS_CONTINUE	Level / Pulse	
#define DCN_1_0__CTXID__OTG2_SET_VTOTAL_MIN_EVENT_INT	12

#define DCN_1_0__SRCID__DC_D3_OTG_SNAPSHOT	0x20	// D3 : OTG snapshot	OTG3_IHC_SNAPSHOT_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE2	Level / Pulse	
#define DCN_1_0__CTXID__DC_D3_OTG_SNAPSHOT	0

#define DCN_1_0__SRCID__DC_D3_FORCE_CNT_W	0x20	// D3 : Force - count--w	OTG3_IHC_FORCE_COUNT_NOW_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE2	Level / Pulse	
#define DCN_1_0__CTXID__DC_D3_FORCE_CNT_W	1

#define DCN_1_0__SRCID__DC_D3_FORCE_VSYNC_NXT_LINE	0x20	// D3 : Force - Vsync - next - line	OTG3_IHC_FORCE_VSYNC_NEXT_LINE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE2	Level / Pulse	
#define DCN_1_0__CTXID__DC_D3_FORCE_VSYNC_NXT_LINE	2

#define DCN_1_0__SRCID__DC_D3_OTG_EXTT_TRG_A	0x20	// D3 : OTG external trigger A	OTG3_IHC_TRIGA_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE2	Level / Pulse	
#define DCN_1_0__CTXID__DC_D3_OTG_EXTT_TRG_A	3

#define DCN_1_0__SRCID__DC_D3_OTG_EXTT_TRG_B	0x20	// D3 : OTG external trigger B	OTG3_IHC_TRIGB_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE2	Level / Pulse	
#define DCN_1_0__CTXID__DC_D3_OTG_EXTT_TRG_B	4

#define DCN_1_0__SRCID__DC_D3_OTG_GSL_VSYNC_GAP	0x20	// D3 : gsl_vsync_gap_interrupt_frame_delay	OTG3_IHC_GSL_VSYNC_GAP_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE20	Level / Pulse	
#define DCN_1_0__CTXID__DC_D3_OTG_GSL_VSYNC_GAP	5

#define DCN_1_0__SRCID__OTG3_VERTICAL_INTERRUPT0_CONTROL	0x20	// D3 : OTG vertical interrupt 0	OTG3_IHC_VERTICAL_INTERRUPT0	DISP_INTERRUPT_STATUS_CONTINUE3	Level / Pulse	
#define DCN_1_0__CTXID__OTG3_VERTICAL_INTERRUPT0_CONTROL	6

#define DCN_1_0__SRCID__OTG3_VERTICAL_INTERRUPT1_CONTROL	0x20	// D3 : OTG vertical interrupt 1	OTG3_IHC_VERTICAL_INTERRUPT1	DISP_INTERRUPT_STATUS_CONTINUE3	Level / Pulse	
#define DCN_1_0__CTXID__OTG3_VERTICAL_INTERRUPT1_CONTROL	7

#define DCN_1_0__SRCID__OTG3_VERTICAL_INTERRUPT2_CONTROL	0x20	// D3 : OTG vertical interrupt 2	OTG3_IHC_VERTICAL_INTERRUPT2	DISP_INTERRUPT_STATUS_CONTINUE3	Level / Pulse	
#define DCN_1_0__CTXID__OTG3_VERTICAL_INTERRUPT2_CONTROL	8

#define DCN_1_0__SRCID__OTG3_EXT_TIMING_SYNC_LOSS_INTERRUPT_CONTROL	0x20	// D3 : OTG ext sync loss interrupt	OTG3_IHC_EXT_TIMING_SYNC_LOSS_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE3	Level / Pulse	
#define DCN_1_0__CTXID__OTG3_EXT_TIMING_SYNC_LOSS_INTERRUPT_CONTROL	9

#define DCN_1_0__SRCID__OTG3_EXT_TIMING_SYNC_INTERRUPT_CONTROL	0x20	// D3 : OTG ext sync interrupt	OTG3_IHC_EXT_TIMING_SYNC_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE3	Level / Pulse	
#define DCN_1_0__CTXID__OTG3_EXT_TIMING_SYNC_INTERRUPT_CONTROL	10

#define DCN_1_0__SRCID__OTG3_EXT_TIMING_SYNC_SIGNAL_INTERRUPT_CONTROL	0x20	// D3 : OTG ext sync signal interrupt	OTG3_IHC_EXT_TIMING_SYNC_SIGNAL_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE3	Level / Pulse	
#define DCN_1_0__CTXID__OTG3_EXT_TIMING_SYNC_SIGNAL_INTERRUPT_CONTROL	11

#define DCN_1_0__SRCID__OTG3_SET_VTOTAL_MIN_EVENT_INT	0x20	// D3 : OTG DRR event occurred interrupt	OTG3_IHC_SET_V_TOTAL_MIN_EVENT_OCCURED_INT	DISP_INTERRUPT_STATUS_CONTINUE2	Level / Pulse	
#define DCN_1_0__CTXID__OTG3_SET_VTOTAL_MIN_EVENT_INT	12

#define DCN_1_0__SRCID__DC_D4_OTG_SNAPSHOT	0x21	// D4 : OTG snapshot	OTG4_IHC_SNAPSHOT_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE3	Level / Pulse	
#define DCN_1_0__CTXID__DC_D4_OTG_SNAPSHOT	0

#define DCN_1_0__SRCID__DC_D4_FORCE_CNT_W	0x21	// D4 : Force - count--w	OTG4_IHC_FORCE_COUNT_NOW_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE3	Level / Pulse	
#define DCN_1_0__CTXID__DC_D4_FORCE_CNT_W	1

#define DCN_1_0__SRCID__DC_D4_FORCE_VSYNC_NXT_LINE	0x21	// D4 : Force - Vsync - next - line	OTG4_IHC_FORCE_VSYNC_NEXT_LINE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE3	Level / Pulse	
#define DCN_1_0__CTXID__DC_D4_FORCE_VSYNC_NXT_LINE	2

#define DCN_1_0__SRCID__DC_D4_OTG_EXTT_TRG_A	0x21	// D4 : OTG external trigger A	OTG4_IHC_TRIGA_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE3	Level / Pulse	
#define DCN_1_0__CTXID__DC_D4_OTG_EXTT_TRG_A	3

#define DCN_1_0__SRCID__DC_D4_OTG_EXTT_TRG_B	0x21	// D4 : OTG external trigger B	OTG4_IHC_TRIGB_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE3	Level / Pulse	
#define DCN_1_0__CTXID__DC_D4_OTG_EXTT_TRG_B	4

#define DCN_1_0__SRCID__DC_D4_OTG_GSL_VSYNC_GAP	0x21	// D4 : gsl_vsync_gap_interrupt_frame_delay	OTG4_IHC_GSL_VSYNC_GAP_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE20	Level / Pulse	
#define DCN_1_0__CTXID__DC_D4_OTG_GSL_VSYNC_GAP	5

#define DCN_1_0__SRCID__OTG4_VERTICAL_INTERRUPT0_CONTROL	0x21	// D4 : OTG vertical interrupt 0	OTG4_IHC_VERTICAL_INTERRUPT0	DISP_INTERRUPT_STATUS_CONTINUE4	Level / Pulse	
#define DCN_1_0__CTXID__OTG4_VERTICAL_INTERRUPT0_CONTROL	6

#define DCN_1_0__SRCID__OTG4_VERTICAL_INTERRUPT1_CONTROL	0x21	// D4 : OTG vertical interrupt 1	OTG4_IHC_VERTICAL_INTERRUPT1	DISP_INTERRUPT_STATUS_CONTINUE4	Level / Pulse	
#define DCN_1_0__CTXID__OTG4_VERTICAL_INTERRUPT1_CONTROL	7

#define DCN_1_0__SRCID__OTG4_VERTICAL_INTERRUPT2_CONTROL	0x21	// D4 : OTG vertical interrupt 2	OTG4_IHC_VERTICAL_INTERRUPT2	DISP_INTERRUPT_STATUS_CONTINUE4	Level / Pulse	
#define DCN_1_0__CTXID__OTG4_VERTICAL_INTERRUPT2_CONTROL	8

#define DCN_1_0__SRCID__OTG4_EXT_TIMING_SYNC_LOSS_INTERRUPT_CONTROL	0x21	// D4 : OTG ext sync loss interrupt	OTG4_IHC_EXT_TIMING_SYNC_LOSS_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE4	Level / Pulse	
#define DCN_1_0__CTXID__OTG4_EXT_TIMING_SYNC_LOSS_INTERRUPT_CONTROL	9

#define DCN_1_0__SRCID__OTG4_EXT_TIMING_SYNC_INTERRUPT_CONTROL	0x21	// D4 : OTG ext sync interrupt	OTG4_IHC_EXT_TIMING_SYNC_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE4	Level / Pulse	
#define DCN_1_0__CTXID__OTG4_EXT_TIMING_SYNC_INTERRUPT_CONTROL	10

#define DCN_1_0__SRCID__OTG4_EXT_TIMING_SYNC_SIGNAL_INTERRUPT_CONTROL	0x21	// D4 : OTG ext sync signal interrupt	OTG4_IHC_EXT_TIMING_SYNC_SIGNAL_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE4	Level / Pulse	
#define DCN_1_0__CTXID__OTG4_EXT_TIMING_SYNC_SIGNAL_INTERRUPT_CONTROL	11

#define DCN_1_0__SRCID__OTG4_SET_VTOTAL_MIN_EVENT_INT	0x21	// D4 : OTG DRR event occurred interrupt	OTG4_IHC_SET_V_TOTAL_MIN_EVENT_OCCURED_INT	DISP_INTERRUPT_STATUS_CONTINUE3	Level / Pulse	
#define DCN_1_0__CTXID__OTG4_SET_VTOTAL_MIN_EVENT_INT	12

#define DCN_1_0__SRCID__DC_D5_OTG_SNAPSHOT	0x22	// D5 : OTG snapshot	OTG5_IHC_SNAPSHOT_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE4	Level / Pulse	
#define DCN_1_0__CTXID__DC_D5_OTG_SNAPSHOT	0

#define DCN_1_0__SRCID__DC_D5_FORCE_CNT_W	0x22	// D5 : Force - count--w	OTG5_IHC_FORCE_COUNT_NOW_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE4	Level / Pulse	
#define DCN_1_0__CTXID__DC_D5_FORCE_CNT_W	1

#define DCN_1_0__SRCID__DC_D5_FORCE_VSYNC_NXT_LINE	0x22	// D5 : Force - Vsync - next - line	OTG5_IHC_FORCE_VSYNC_NEXT_LINE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE4	Level / Pulse	
#define DCN_1_0__CTXID__DC_D5_FORCE_VSYNC_NXT_LINE	2

#define DCN_1_0__SRCID__DC_D5_OTG_EXTT_TRG_A	0x22	// D5 : OTG external trigger A	OTG5_IHC_TRIGA_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE4	Level / Pulse	
#define DCN_1_0__CTXID__DC_D5_OTG_EXTT_TRG_A	3

#define DCN_1_0__SRCID__DC_D5_OTG_EXTT_TRG_B	0x22	// D5 : OTG external trigger B	OTG5_IHC_TRIGB_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE4	Level / Pulse	
#define DCN_1_0__CTXID__DC_D5_OTG_EXTT_TRG_B	4

#define DCN_1_0__SRCID__DC_D5_OTG_GSL_VSYNC_GAP	0x22	// D5 : gsl_vsync_gap_interrupt_frame_delay	OTG5_IHC_GSL_VSYNC_GAP_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE20	Level / Pulse	
#define DCN_1_0__CTXID__DC_D5_OTG_GSL_VSYNC_GAP	5

#define DCN_1_0__SRCID__OTG5_VERTICAL_INTERRUPT0_CONTROL	0x22	// D5 : OTG vertical interrupt 0	OTG5_IHC_VERTICAL_INTERRUPT0	DISP_INTERRUPT_STATUS_CONTINUE5	Level / Pulse	
#define DCN_1_0__CTXID__OTG5_VERTICAL_INTERRUPT0_CONTROL	6

#define DCN_1_0__SRCID__OTG5_VERTICAL_INTERRUPT1_CONTROL	0x22	// D5 : OTG vertical interrupt 1	OTG5_IHC_VERTICAL_INTERRUPT1	DISP_INTERRUPT_STATUS_CONTINUE5	Level / Pulse	
#define DCN_1_0__CTXID__OTG5_VERTICAL_INTERRUPT1_CONTROL	7

#define DCN_1_0__SRCID__OTG5_VERTICAL_INTERRUPT2_CONTROL	0x22	// D5 : OTG vertical interrupt 2	OTG5_IHC_VERTICAL_INTERRUPT2	DISP_INTERRUPT_STATUS_CONTINUE5	Level / Pulse	
#define DCN_1_0__CTXID__OTG5_VERTICAL_INTERRUPT2_CONTROL	8

#define DCN_1_0__SRCID__OTG5_EXT_TIMING_SYNC_LOSS_INTERRUPT_CONTROL	0x22	// D5 : OTG ext sync loss interrupt	OTG5_IHC_EXT_TIMING_SYNC_LOSS_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE4	Level / Pulse	
#define DCN_1_0__CTXID__OTG5_EXT_TIMING_SYNC_LOSS_INTERRUPT_CONTROL	9

#define DCN_1_0__SRCID__OTG5_EXT_TIMING_SYNC_INTERRUPT_CONTROL	0x22	// D5 : OTG ext sync interrupt	OTG5_IHC_EXT_TIMING_SYNC_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE4	Level / Pulse	
#define DCN_1_0__CTXID__OTG5_EXT_TIMING_SYNC_INTERRUPT_CONTROL	10

#define DCN_1_0__SRCID__OTG5_EXT_TIMING_SYNC_SIGNAL_INTERRUPT_CONTROL	0x22	// D5 : OTG ext sync signal interrupt	OTG5_IHC_EXT_TIMING_SYNC_SIGNAL_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE4	Level / Pulse	
#define DCN_1_0__CTXID__OTG5_EXT_TIMING_SYNC_SIGNAL_INTERRUPT_CONTROL	11

#define DCN_1_0__SRCID__OTG5_SET_VTOTAL_MIN_EVENT_INT	0x22	// D5 : OTG DRR event occurred interrupt	OTG5_IHC_SET_V_TOTAL_MIN_EVENT_OCCURED_INT	DISP_INTERRUPT_STATUS_CONTINUE4	Level / Pulse	
#define DCN_1_0__CTXID__OTG5_SET_VTOTAL_MIN_EVENT_INT	12

#define DCN_1_0__SRCID__DC_D1_VBLANK	0x23	// D1 : VBlank	HUBP0_IHC_VBLANK_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE13	Level / Pulse	
#define DCN_1_0__CTXID__DC_D1_VBLANK	0

#define DCN_1_0__SRCID__DC_D1_VLINE1	0x23	// D1 : Vline	HUBP0_IHC_VLINE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE13	Level / Pulse	
#define DCN_1_0__CTXID__DC_D1_VLINE1	1

#define DCN_1_0__SRCID__DC_D1_VLINE2	0x23	// D1 : Vline2	HUBP0_IHC_VLINE2_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE13	Level / Pulse	
#define DCN_1_0__CTXID__DC_D1_VLINE2	2

#define DCN_1_0__SRCID__DC_D2_VBLANK	0x23	// D2 : Vblank	HUBP1_IHC_VBLANK_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE14	Level / Pulse	
#define DCN_1_0__CTXID__DC_D2_VBLANK	3

#define DCN_1_0__SRCID__DC_D2_VLINE1	0x23	// D2 : Vline	HUBP1_IHC_VLINE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE14	Level / Pulse	
#define DCN_1_0__CTXID__DC_D2_VLINE1	4

#define DCN_1_0__SRCID__DC_D2_VLINE2	0x23	// D2 : Vline2	HUBP1_IHC_VLINE2_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE14	Level / Pulse	
#define DCN_1_0__CTXID__DC_D2_VLINE2	5

#define DCN_1_0__SRCID__HUBP0_IHC_VM_CONTEXT_ERROR	0x23	// "Reports three types of fault that may occur during memory address translation in HUBPREQ:	HUBP0_IHC_VM_CONTEXT_ERROR_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE13	Level	
#define DCN_1_0__CTXID__HUBP0_IHC_VM_CONTEXT_ERROR	6

#define DCN_1_0__SRCID__HUBP1_IHC_VM_CONTEXT_ERROR	0x23	// "Reports three types of fault that may occur during memory address translation in HUBPREQ:	HUBP1_IHC_VM_CONTEXT_ERROR_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE14	Level	
#define DCN_1_0__CTXID__HUBP1_IHC_VM_CONTEXT_ERROR	7

#define DCN_1_0__SRCID__HUBP2_IHC_VM_CONTEXT_ERROR	0x23	// "Reports three types of fault that may occur during memory address translation in HUBPREQ:	HUBP2_IHC_VM_CONTEXT_ERROR_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE15	Level	
#define DCN_1_0__CTXID__HUBP2_IHC_VM_CONTEXT_ERROR	8

#define DCN_1_0__SRCID__HUBP3_IHC_VM_CONTEXT_ERROR	0x23	// "Reports three types of fault that may occur during memory address translation in HUBPREQ:	HUBP3_IHC_VM_CONTEXT_ERROR_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE16	Level	
#define DCN_1_0__CTXID__HUBP3_IHC_VM_CONTEXT_ERROR	9

#define DCN_1_0__SRCID__HUBP4_IHC_VM_CONTEXT_ERROR	0x23	// "Reports three types of fault that may occur during memory address translation in HUBPREQ:	HUBP4_IHC_VM_CONTEXT_ERROR_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE16	Level	
#define DCN_1_0__CTXID__HUBP4_IHC_VM_CONTEXT_ERROR	10

#define DCN_1_0__SRCID__HUBP5_IHC_VM_CONTEXT_ERROR	0x23	// "Reports three types of fault that may occur during memory address translation in HUBPREQ:	HUBP5_IHC_VM_CONTEXT_ERROR_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE16	Level	
#define DCN_1_0__CTXID__HUBP5_IHC_VM_CONTEXT_ERROR	11

#define DCN_1_0__SRCID__HUBP6_IHC_VM_CONTEXT_ERROR	0x23	// "Reports three types of fault that may occur during memory address translation in HUBPREQ:	HUBP6_IHC_VM_CONTEXT_ERROR_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE16	Level	
#define DCN_1_0__CTXID__HUBP6_IHC_VM_CONTEXT_ERROR	12

#define DCN_1_0__SRCID__HUBP7_IHC_VM_CONTEXT_ERROR	0x23	// "Reports three types of fault that may occur during memory address translation in HUBPREQ:	HUBP7_IHC_VM_CONTEXT_ERROR_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE16	Level	
#define DCN_1_0__CTXID__HUBP7_IHC_VM_CONTEXT_ERROR	13

#define DCN_1_0__SRCID__DPP0_PERFCOUNTER_INT0_STATUS	0x24	// DPP0 perfmon counter0 interrupt	DPP0_PERFMON_COUNTER0_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE8	Level / Pulse	
#define DCN_1_0__CTXID__DPP0_PERFCOUNTER_INT0_STATUS	0

#define DCN_1_0__SRCID__DPP0_PERFCOUNTER_INT1_STATUS	0x24	// DPP0 perfmon counter1 interrupt	DPP0_PERFMON_COUNTER1_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE8	Level	
#define DCN_1_0__CTXID__DPP0_PERFCOUNTER_INT1_STATUS	1

#define DCN_1_0__SRCID__DC_D3_VBLANK	0x24	// D3 : VBlank	HUBP2_IHC_VBLANK_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE15	Level / Pulse	
#define DCN_1_0__CTXID__DC_D3_VBLANK	9

#define DCN_1_0__SRCID__DC_D3_VLINE1	0x24	// D3 : Vline	HUBP2_IHC_VLINE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE15	Level / Pulse	
#define DCN_1_0__CTXID__DC_D3_VLINE1	10

#define DCN_1_0__SRCID__DC_D3_VLINE2	0x24	// D3 : Vline2	HUBP2_IHC_VLINE2_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE15	Level / Pulse	
#define DCN_1_0__CTXID__DC_D3_VLINE2	11

#define DCN_1_0__SRCID__DC_D4_VBLANK	0x24	// D4 : Vblank	HUBP3_IHC_VBLANK_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE16	Level / Pulse	
#define DCN_1_0__CTXID__DC_D4_VBLANK	12

#define DCN_1_0__SRCID__DC_D4_VLINE1	0x24	// D4 : Vline	HUBP3_IHC_VLINE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE16	Level / Pulse	
#define DCN_1_0__CTXID__DC_D4_VLINE1	13

#define DCN_1_0__SRCID__DC_D4_VLINE2	0x24	// D4 : Vline2	HUBP3_IHC_VLINE2_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE16	Level / Pulse	
#define DCN_1_0__CTXID__DC_D4_VLINE2	14

#define DCN_1_0__SRCID__DPP1_PERFCOUNTER_INT0_STATUS	0x25	// DPP1 perfmon counter0 interrupt	DPP1_PERFMON_COUNTER0_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE8	Level / Pulse	
#define DCN_1_0__CTXID__DPP1_PERFCOUNTER_INT0_STATUS	0

#define DCN_1_0__SRCID__DPP1_PERFCOUNTER_INT1_STATUS	0x25	// DPP1 perfmon counter1 interrupt	DPP1_PERFMON_COUNTER1_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE8	Level	
#define DCN_1_0__CTXID__DPP1_PERFCOUNTER_INT1_STATUS	1

#define DCN_1_0__SRCID__DC_D5_VBLANK	0x25	// D5 : VBlank	HUBP4_IHC_VBLANK_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE16	Level / Pulse	
#define DCN_1_0__CTXID__DC_D5_VBLANK	9

#define DCN_1_0__SRCID__DC_D5_VLINE1	0x25	// D5 : Vline	HUBP4_IHC_VLINE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE16	Level / Pulse	
#define DCN_1_0__CTXID__DC_D5_VLINE1	10

#define DCN_1_0__SRCID__DC_D5_VLINE2	0x25	// D5 : Vline2	HUBP4_IHC_VLINE2_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE16	Level / Pulse	
#define DCN_1_0__CTXID__DC_D5_VLINE2	11

#define DCN_1_0__SRCID__DC_D6_VBLANK	0x25	// D6 : Vblank	HUBP5_IHC_VBLANK_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE16	Level / Pulse	
#define DCN_1_0__CTXID__DC_D6_VBLANK	12

#define DCN_1_0__SRCID__DC_D6_VLINE1	0x25	// D6 : Vline	HUBP5_IHC_VLINE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE16	Level / Pulse	
#define DCN_1_0__CTXID__DC_D6_VLINE1	13

#define DCN_1_0__SRCID__DC_D6_VLINE2	0x25	// D6 : Vline2	HUBP5_IHC_VLINE2_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE16	Level / Pulse	
#define DCN_1_0__CTXID__DC_D6_VLINE2	14

#define DCN_1_0__SRCID__DPP2_PERFCOUNTER_INT0_STATUS	0x26	// DPP2 perfmon counter0 interrupt	DPP2_PERFMON_COUNTER0_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE8	Level / Pulse	
#define DCN_1_0__CTXID__DPP2_PERFCOUNTER_INT0_STATUS	0

#define DCN_1_0__SRCID__DPP2_PERFCOUNTER_INT1_STATUS	0x26	// DPP2 perfmon counter1 interrupt	DPP2_PERFMON_COUNTER1_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE8	Level	
#define DCN_1_0__CTXID__DPP2_PERFCOUNTER_INT1_STATUS	1

#define DCN_1_0__SRCID__DC_D7_VBLANK	0x26	// D7 : VBlank	HUBP6_IHC_VBLANK_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE16	Level / Pulse	
#define DCN_1_0__CTXID__DC_D7_VBLANK	9

#define DCN_1_0__SRCID__DC_D7_VLINE1	0x26	// D7 : Vline	HUBP6_IHC_VLINE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE16	Level / Pulse	
#define DCN_1_0__CTXID__DC_D7_VLINE1	10

#define DCN_1_0__SRCID__DC_D7_VLINE2	0x26	// D7 : Vline2	HUBP6_IHC_VLINE2_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE16	Level / Pulse	
#define DCN_1_0__CTXID__DC_D7_VLINE2	11

#define DCN_1_0__SRCID__DC_D8_VBLANK	0x26	// D8 : Vblank	HUBP7_IHC_VBLANK_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE16	Level / Pulse	
#define DCN_1_0__CTXID__DC_D8_VBLANK	12

#define DCN_1_0__SRCID__DC_D8_VLINE1	0x26	// D8 : Vline	HUBP7_IHC_VLINE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE16	Level / Pulse	
#define DCN_1_0__CTXID__DC_D8_VLINE1	13

#define DCN_1_0__SRCID__DC_D8_VLINE2	0x26	// D8 : Vline2	HUBP7_IHC_VLINE2_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE16	Level / Pulse	
#define DCN_1_0__CTXID__DC_D8_VLINE2	14

#define DCN_1_0__SRCID__DPP3_PERFCOUNTER_INT0_STATUS	0x27	// DPP3 perfmon counter0 interrupt	DPP3_PERFMON_COUNTER0_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE9	Level / Pulse	
#define DCN_1_0__CTXID__DPP3_PERFCOUNTER_INT0_STATUS	0

#define DCN_1_0__SRCID__DPP3_PERFCOUNTER_INT1_STATUS	0x27	// DPP3 perfmon counter1 interrupt	DPP3_PERFMON_COUNTER1_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE9	Level	
#define DCN_1_0__CTXID__DPP3_PERFCOUNTER_INT1_STATUS	1

#define DCN_1_0__SRCID__DPP4_PERFCOUNTER_INT0_STATUS	0x28	// DPP4 perfmon counter0 interrupt	DPP4_PERFMON_COUNTER0_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE9	Level / Pulse	
#define DCN_1_0__CTXID__DPP4_PERFCOUNTER_INT0_STATUS	0

#define DCN_1_0__SRCID__DPP4_PERFCOUNTER_INT1_STATUS	0x28	// DPP4 perfmon counter1 interrupt	DPP4_PERFMON_COUNTER1_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE9	Level	
#define DCN_1_0__CTXID__DPP4_PERFCOUNTER_INT1_STATUS	1

#define DCN_1_0__SRCID__DPP5_PERFCOUNTER_INT0_STATUS	0x29	// DPP5 perfmon counter0 interrupt	DPP5_PERFMON_COUNTER0_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE9	Level / Pulse	
#define DCN_1_0__CTXID__DPP5_PERFCOUNTER_INT0_STATUS	0

#define DCN_1_0__SRCID__DPP5_PERFCOUNTER_INT1_STATUS	0x29	// DPP5 perfmon counter1 interrupt	DPP5_PERFMON_COUNTER1_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE9	Level	
#define DCN_1_0__CTXID__DPP5_PERFCOUNTER_INT1_STATUS	1

#define DCN_1_0__SRCID__DPP6_PERFCOUNTER_INT0_STATUS	0x2A	// DPP6 perfmon counter0 interrupt	DPP6_PERFMON_COUNTER0_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE12	Level / Pulse	
#define DCN_1_0__CTXID__DPP6_PERFCOUNTER_INT0_STATUS	0

#define DCN_1_0__SRCID__DPP6_PERFCOUNTER_INT1_STATUS	0x2A	// DPP6 perfmon counter1 interrupt	DPP6_PERFMON_COUNTER1_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE12	Level	
#define DCN_1_0__CTXID__DPP6_PERFCOUNTER_INT1_STATUS	1

#define DCN_1_0__SRCID__DPP7_PERFCOUNTER_INT0_STATUS	0x2B	// DPP7 perfmon counter0 interrupt	DPP7_PERFMON_COUNTER0_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE12	Level / Pulse	
#define DCN_1_0__CTXID__DPP7_PERFCOUNTER_INT0_STATUS	0

#define DCN_1_0__SRCID__DPP7_PERFCOUNTER_INT1_STATUS	0x2B	// DPP7 perfmon counter1 interrupt	DPP7_PERFMON_COUNTER1_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE12	Level	
#define DCN_1_0__CTXID__DPP7_PERFCOUNTER_INT1_STATUS	1

#define DCN_1_0__SRCID__HUBP0_PERFCOUNTER_INT0_STATUS	0x2C	// HUBP0 perfmon counter0 interrupt	HUBP0_PERFMON_COUNTER0_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE13	Level / Pulse	
#define DCN_1_0__CTXID__HUBP0_PERFCOUNTER_INT0_STATUS	0

#define DCN_1_0__SRCID__HUBP0_PERFCOUNTER_INT1_STATUS	0x2C	// HUBP0 perfmon counter1 interrupt	HUBP0_PERFMON_COUNTER1_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE13	Level	
#define DCN_1_0__CTXID__HUBP0_PERFCOUNTER_INT1_STATUS	1

#define DCN_1_0__SRCID__HUBP1_PERFCOUNTER_INT0_STATUS	0x2D	// HUBP1 perfmon counter0 interrupt	HUBP1_PERFMON_COUNTER0_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE14	Level / Pulse	
#define DCN_1_0__CTXID__HUBP1_PERFCOUNTER_INT0_STATUS	0

#define DCN_1_0__SRCID__HUBP1_PERFCOUNTER_INT1_STATUS	0x2D	// HUBP1 perfmon counter1 interrupt	HUBP1_PERFMON_COUNTER1_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE14	Level	
#define DCN_1_0__CTXID__HUBP1_PERFCOUNTER_INT1_STATUS	1

#define DCN_1_0__SRCID__HUBP2_PERFCOUNTER_INT0_STATUS	0x2E	// HUBP2 perfmon counter0 interrupt	HUBP2_PERFMON_COUNTER0_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE14	Level / Pulse	
#define DCN_1_0__CTXID__HUBP2_PERFCOUNTER_INT0_STATUS	0

#define DCN_1_0__SRCID__HUBP2_PERFCOUNTER_INT1_STATUS	0x2E	// HUBP2 perfmon counter1 interrupt	HUBP2_PERFMON_COUNTER1_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE14	Level	
#define DCN_1_0__CTXID__HUBP2_PERFCOUNTER_INT1_STATUS	1

#define DCN_1_0__SRCID__HUBP3_PERFCOUNTER_INT0_STATUS	0x2F	// HUBP3 perfmon counter0 interrupt	HUBP3_PERFMON_COUNTER0_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE14	Level / Pulse	
#define DCN_1_0__CTXID__HUBP3_PERFCOUNTER_INT0_STATUS	0

#define DCN_1_0__SRCID__HUBP3_PERFCOUNTER_INT1_STATUS	0x2F	// HUBP3 perfmon counter1 interrupt	HUBP3_PERFMON_COUNTER1_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE14	Level	
#define DCN_1_0__CTXID__HUBP3_PERFCOUNTER_INT1_STATUS	1

#define DCN_1_0__SRCID__HUBP4_PERFCOUNTER_INT0_STATUS	0x30	// HUBP4 perfmon counter0 interrupt	HUBP4_PERFMON_COUNTER0_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE15	Level / Pulse	
#define DCN_1_0__CTXID__HUBP4_PERFCOUNTER_INT0_STATUS	0

#define DCN_1_0__SRCID__HUBP4_PERFCOUNTER_INT1_STATUS	0x30	// HUBP4 perfmon counter1 interrupt	HUBP4_PERFMON_COUNTER1_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE15	Level	
#define DCN_1_0__CTXID__HUBP4_PERFCOUNTER_INT1_STATUS	1

#define DCN_1_0__SRCID__HUBP5_PERFCOUNTER_INT0_STATUS	0x31	// HUBP5 perfmon counter0 interrupt	HUBP5_PERFMON_COUNTER0_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE15	Level / Pulse	
#define DCN_1_0__CTXID__HUBP5_PERFCOUNTER_INT0_STATUS	0

#define DCN_1_0__SRCID__HUBP5_PERFCOUNTER_INT1_STATUS	0x31	// HUBP5 perfmon counter1 interrupt	HUBP5_PERFMON_COUNTER1_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE15	Level	
#define DCN_1_0__CTXID__HUBP5_PERFCOUNTER_INT1_STATUS	1

#define DCN_1_0__SRCID__HUBP6_PERFCOUNTER_INT0_STATUS	0x32	// HUBP6 perfmon counter0 interrupt	HUBP6_PERFMON_COUNTER0_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE15	Level / Pulse	
#define DCN_1_0__CTXID__HUBP6_PERFCOUNTER_INT0_STATUS	0

#define DCN_1_0__SRCID__HUBP6_PERFCOUNTER_INT1_STATUS	0x32	// HUBP6 perfmon counter1 interrupt	HUBP6_PERFMON_COUNTER1_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE15	Level	
#define DCN_1_0__CTXID__HUBP6_PERFCOUNTER_INT1_STATUS	1

#define DCN_1_0__SRCID__HUBP7_PERFCOUNTER_INT0_STATUS	0x33	// HUBP7 perfmon counter0 interrupt	HUBP7_PERFMON_COUNTER0_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE16	Level / Pulse	
#define DCN_1_0__CTXID__HUBP7_PERFCOUNTER_INT0_STATUS	0

#define DCN_1_0__SRCID__HUBP7_PERFCOUNTER_INT1_STATUS	0x33	// HUBP7 perfmon counter1 interrupt	HUBP7_PERFMON_COUNTER1_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE16	Level	
#define DCN_1_0__CTXID__HUBP7_PERFCOUNTER_INT1_STATUS	1

#define DCN_1_0__SRCID__WB1_PERFCOUNTER_INT0_STATUS	0x34	// WB1 perfmon counter0 interrupt	WB1_PERFMON_COUNTER0_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE11	Level / Pulse	
#define DCN_1_0__CTXID__WB1_PERFCOUNTER_INT0_STATUS	0

#define DCN_1_0__SRCID__WB1_PERFCOUNTER_INT1_STATUS	0x34	// WB1 perfmon counter1 interrupt	WB1_PERFMON_COUNTER1_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE11	Level	
#define DCN_1_0__CTXID__WB1_PERFCOUNTER_INT1_STATUS	1

#define DCN_1_0__SRCID__HUBBUB_PERFCOUNTER_INT0_STATUS	0x35	// HUBBUB perfmon counter0 interrupt	HUBBUB_PERFMON_COUNTER0_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE13	Level / Pulse	
#define DCN_1_0__CTXID__HUBBUB_PERFCOUNTER_INT0_STATUS	0

#define DCN_1_0__SRCID__HUBBUB_PERFCOUNTER_INT1_STATUS	0x35	// HUBBUB perfmon counter1 interrupt	HUBBUB_PERFMON_COUNTER1_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE13	Level	
#define DCN_1_0__CTXID__HUBBUB_PERFCOUNTER_INT1_STATUS	1

#define DCN_1_0__SRCID__MPC_PERFCOUNTER_INT0_STATUS	0x36	// MPC perfmon counter0 interrupt	MPC_PERFMON_COUNTER0_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE12	Level / Pulse	
#define DCN_1_0__CTXID__MPC_PERFCOUNTER_INT0_STATUS	0

#define DCN_1_0__SRCID__MPC_PERFCOUNTER_INT1_STATUS	0x36	// MPC perfmon counter1 interrupt	MPC_PERFMON_COUNTER1_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE12	Level	
#define DCN_1_0__CTXID__MPC_PERFCOUNTER_INT1_STATUS	1

#define DCN_1_0__SRCID__OPP_PERFCOUNTER_INT0_STATUS	0x37	// OPP perfmon counter0 interrupt	OPP_PERFMON_COUNTER0_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE17	Level / Pulse	
#define DCN_1_0__CTXID__OPP_PERFCOUNTER_INT0_STATUS	0

#define DCN_1_0__SRCID__OPP_PERFCOUNTER_INT1_STATUS	0x37	// OPP perfmon counter1 interrupt	OPP_PERFMON_COUNTER1_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE17	Level	
#define DCN_1_0__CTXID__OPP_PERFCOUNTER_INT1_STATUS	1

#define DCN_1_0__SRCID__DC_D6_OTG_SNAPSHOT	0x38	// D6: OTG snapshot	OTG6_IHC_SNAPSHOT_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE5	Level / Pulse	
#define DCN_1_0__CTXID__DC_D6_OTG_SNAPSHOT	0

#define DCN_1_0__SRCID__DC_D6_FORCE_CNT_W	0x38	// D6 : Force - count--w	OTG6_IHC_FORCE_COUNT_NOW_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE5	Level / Pulse	
#define DCN_1_0__CTXID__DC_D6_FORCE_CNT_W	1

#define DCN_1_0__SRCID__DC_D6_FORCE_VSYNC_NXT_LINE	0x38	// D6 : Force - Vsync - next - line	OTG6_IHC_FORCE_VSYNC_NEXT_LINE_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE5	Level / Pulse	
#define DCN_1_0__CTXID__DC_D6_FORCE_VSYNC_NXT_LINE	2

#define DCN_1_0__SRCID__DC_D6_OTG_EXTT_TRG_A	0x38	// D6 : OTG external trigger A	OTG6_IHC_TRIGA_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE5	Level / Pulse	
#define DCN_1_0__CTXID__DC_D6_OTG_EXTT_TRG_A	3

#define DCN_1_0__SRCID__DC_D6_OTG_EXTT_TRG_B	0x38	// D6 : OTG external trigger B	OTG6_IHC_TRIGB_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE5	Level / Pulse	
#define DCN_1_0__CTXID__DC_D6_OTG_EXTT_TRG_B	4

#define DCN_1_0__SRCID__DC_D6_OTG_GSL_VSYNC_GAP	0x38	// D6 : gsl_vsync_gap_interrupt_frame_delay	OTG6_IHC_GSL_VSYNC_GAP_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE20	Level / Pulse	
#define DCN_1_0__CTXID__DC_D6_OTG_GSL_VSYNC_GAP	5

#define DCN_1_0__SRCID__OTG6_VERTICAL_INTERRUPT0_CONTROL	0x38	// D6 : OTG vertical interrupt 0	OTG6_IHC_VERTICAL_INTERRUPT0	DISP_INTERRUPT_STATUS_CONTINUE5	Level / Pulse	
#define DCN_1_0__CTXID__OTG6_VERTICAL_INTERRUPT0_CONTROL	6

#define DCN_1_0__SRCID__OTG6_VERTICAL_INTERRUPT1_CONTROL	0x38	// D6 : OTG vertical interrupt 1	OTG6_IHC_VERTICAL_INTERRUPT1	DISP_INTERRUPT_STATUS_CONTINUE5	Level / Pulse	
#define DCN_1_0__CTXID__OTG6_VERTICAL_INTERRUPT1_CONTROL	7

#define DCN_1_0__SRCID__OTG6_VERTICAL_INTERRUPT2_CONTROL	0x38	// D6 : OTG vertical interrupt 2	OTG6_IHC_VERTICAL_INTERRUPT2	DISP_INTERRUPT_STATUS_CONTINUE5	Level / Pulse	
#define DCN_1_0__CTXID__OTG6_VERTICAL_INTERRUPT2_CONTROL	8

#define DCN_1_0__SRCID__OTG6_EXT_TIMING_SYNC_LOSS_INTERRUPT_CONTROL	0x38	// D6 : OTG ext sync loss interrupt	OTG6_IHC_EXT_TIMING_SYNC_LOSS_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE5	Level / Pulse	
#define DCN_1_0__CTXID__OTG6_EXT_TIMING_SYNC_LOSS_INTERRUPT_CONTROL	9

#define DCN_1_0__SRCID__OTG6_EXT_TIMING_SYNC_INTERRUPT_CONTROL	0x38	// D6 : OTG ext sync interrupt	OTG6_IHC_EXT_TIMING_SYNC_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE5	Level / Pulse	
#define DCN_1_0__CTXID__OTG6_EXT_TIMING_SYNC_INTERRUPT_CONTROL	10

#define DCN_1_0__SRCID__OTG6_EXT_TIMING_SYNC_SIGNAL_INTERRUPT_CONTROL	0x38	// D6 : OTG ext sync signal interrupt	OTG6_IHC_EXT_TIMING_SYNC_SIGNAL_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE5	Level / Pulse	
#define DCN_1_0__CTXID__OTG6_EXT_TIMING_SYNC_SIGNAL_INTERRUPT_CONTROL	11

#define DCN_1_0__SRCID__OTG6_SET_VTOTAL_MIN_EVENT_INT	0x38	// D : OTG DRR event occurred interrupt	OTG6_IHC_SET_V_TOTAL_MIN_EVENT_OCCURED_INT	DISP_INTERRUPT_STATUS_CONTINUE5	Level / Pulse	
#define DCN_1_0__CTXID__OTG6_SET_VTOTAL_MIN_EVENT_INT	12

#define DCN_1_0__SRCID__OPTC_PERFCOUNTER_INT0_STATUS	0x39	// OPTC perfmon counter0 interrupt	OPTC_PERFMON_COUNTER0_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE17	Level / Pulse	
#define DCN_1_0__CTXID__OPTC_PERFCOUNTER_INT0_STATUS	0

#define DCN_1_0__SRCID__OPTC_PERFCOUNTER_INT1_STATUS	0x39	// OPTC perfmon counter1 interrupt	OPTC_PERFMON_COUNTER1_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE17	Level	
#define DCN_1_0__CTXID__OPTC_PERFCOUNTER_INT1_STATUS	1

#define DCN_1_0__SRCID__MMHUBBUB_PERFCOUNTER_INT0_STATUS	0x3A	// MMHUBBUB perfmon counter0 interrupt	MMHUBBUB_PERFMON_COUNTER0_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE17	Level / Pulse	
#define DCN_1_0__CTXID__MMHUBBUB_PERFCOUNTER_INT0_STATUS	0

#define DCN_1_0__SRCID__MMHUBBUB_PERFCOUNTER_INT1_STATUS	0x3A	// MMHUBBUB perfmon counter1 interrupt	MMHUBBUB_PERFMON_COUNTER1_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE17	Level	
#define DCN_1_0__CTXID__MMHUBBUB_PERFCOUNTER_INT1_STATUS	1

#define DCN_1_0__SRCID__AZ_PERFCOUNTER_INT0_STATUS	0x3B	// AZ perfmon counter0 interrupt	AZ_PERFMON_COUNTER0_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE18	Level / Pulse	
#define DCN_1_0__CTXID__AZ_PERFCOUNTER_INT0_STATUS	0

#define DCN_1_0__SRCID__AZ_PERFCOUNTER_INT1_STATUS	0x3B	// AZ perfmon counter1 interrupt	AZ_PERFMON_COUNTER1_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE18	Level	
#define DCN_1_0__CTXID__AZ_PERFCOUNTER_INT1_STATUS	1

#define DCN_1_0__SRCID__DC_D1_OTG_VSTARTUP	0x3C	// "OTG0 VSTARTUP event occurred interrupt, VSTARTUP event indicates a start of new frame"	OTG1_IHC_VSTARTUP_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE20	Level / Pulse
#define DCN_1_0__SRCID__DC_D2_OTG_VSTARTUP	0x3D	// "OTG1 VSTARTUP event occurred interrupt, VSTARTUP event indicates a start of new frame"	OTG2_IHC_VSTARTUP_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE20	Level / Pulse
#define DCN_1_0__SRCID__DC_D3_OTG_VSTARTUP	0x3E	// "OTG2 VSTARTUP event occurred interrupt, VSTARTUP event indicates a start of new frame"	OTG3_IHC_VSTARTUP_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE20	Level / Pulse
#define DCN_1_0__SRCID__DC_D4_OTG_VSTARTUP	0x3F	// "OTG3 VSTARTUP event occurred interrupt, VSTARTUP event indicates a start of new frame"	OTG4_IHC_VSTARTUP_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE20	Level / Pulse
#define DCN_1_0__SRCID__DC_D5_OTG_VSTARTUP	0x40	// "OTG4 VSTARTUP event occurred interrupt, VSTARTUP event indicates a start of new frame"	OTG5_IHC_VSTARTUP_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE20	Level / Pulse
#define DCN_1_0__SRCID__DC_D6_OTG_VSTARTUP	0x41	// "OTG5 VSTARTUP event occurred interrupt, VSTARTUP event indicates a start of new frame"	OTG6_IHC_VSTARTUP_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE20	Level / Pulse

#define DCN_1_0__SRCID__DC_D1_OTG_VREADY	0x42	// "OTG0 VREADY event occurred interrupt, VREADY event, VREADY event indicates the time DCHUB can start to request data for new frame"	OTG1_IHC_VREADY_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE20	Level / Pulse
#define DCN_1_0__SRCID__DC_D2_OTG_VREADY	0x43	// "OTG1 VREADY event occurred interrupt, VREADY event, VREADY event indicates the time DCHUB can start to request data for new frame"	OTG2_IHC_VREADY_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE20	Level / Pulse
#define DCN_1_0__SRCID__DC_D3_OTG_VREADY	0x44	// "OTG2 VREADY event occurred interrupt, VREADY event, VREADY event indicates the time DCHUB can start to request data for new frame"	OTG3_IHC_VREADY_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE20	Level / Pulse
#define DCN_1_0__SRCID__DC_D4_OTG_VREADY	0x45	// "OTG3 VREADY event occurred interrupt, VREADY event, VREADY event indicates the time DCHUB can start to request data for new frame"	OTG4_IHC_VREADY_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE20	Level / Pulse
#define DCN_1_0__SRCID__DC_D5_OTG_VREADY	0x46	// "OTG4 VREADY event occurred interrupt, VREADY event, VREADY event indicates the time DCHUB can start to request data for new frame"	OTG5_IHC_VREADY_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE20	Level / Pulse
#define DCN_1_0__SRCID__DC_D6_OTG_VREADY	0x47	// "OTG5 VREADY event occurred interrupt, VREADY event, VREADY event indicates the time DCHUB can start to request data for new frame"	OTG6_IHC_VREADY_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE20	Level / Pulse

#define DCN_1_0__SRCID__OTG0_VSYNC_NOM	0x48	// OTG0 vsync nom interrupt	OTG1_IHC_VSYNC_NOM_INTERRUPT	DISP_INTERRUPT_STATUS	Level / Pulse
#define DCN_1_0__SRCID__OTG1_VSYNC_NOM	0x49	// OTG1 vsync nom interrupt	OTG2_IHC_VSYNC_NOM_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE	Level / Pulse
#define DCN_1_0__SRCID__OTG2_VSYNC_NOM	0x4A	// OTG2 vsync nom interrupt	OTG3_IHC_VSYNC_NOM_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE2	Level / Pulse
#define DCN_1_0__SRCID__OTG3_VSYNC_NOM	0x4B	// OTG3 vsync nom interrupt	OTG4_IHC_VSYNC_NOM_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE3	Level / Pulse
#define DCN_1_0__SRCID__OTG4_VSYNC_NOM	0x4C	// OTG4 vsync nom interrupt	OTG5_IHC_VSYNC_NOM_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE4	Level / Pulse
#define DCN_1_0__SRCID__OTG5_VSYNC_NOM	0x4D	// OTG5 vsync nom interrupt	OTG6_IHC_VSYNC_NOM_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE5	Level / Pulse

#define DCN_1_0__SRCID__DCPG_DCFE8_POWER_UP_INT	0x4E	// Display pipe0 power up interrupt 	DCPG_IHC_DOMAIN8_POWER_UP_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE22	Level	
#define DCN_1_0__CTXID__DCPG_DCFE8_POWER_UP_INT	0

#define DCN_1_0__SRCID__DCPG_DCFE9_POWER_UP_INT	0x4E	// Display pipe1 power up interrupt 	DCPG_IHC_DOMAIN9_POWER_UP_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE22	Level	
#define DCN_1_0__CTXID__DCPG_DCFE9_POWER_UP_INT	1

#define DCN_1_0__SRCID__DCPG_DCFE10_POWER_UP_INT	0x4E	// Display pipe2 power up interrupt 	DCPG_IHC_DOMAIN10_POWER_UP_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE22	Level	
#define DCN_1_0__CTXID__DCPG_DCFE10_POWER_UP_INT	2

#define DCN_1_0__SRCID__DCPG_DCFE11_POWER_UP_INT	0x4E	// Display pipe3 power up interrupt 	DCPG_IHC_DOMAIN11_POWER_UP_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE22	Level	
#define DCN_1_0__CTXID__DCPG_DCFE11_POWER_UP_INT	3

#define DCN_1_0__SRCID__DCPG_DCFE12_POWER_UP_INT	0x4E	// Display pipe4 power up interrupt 	DCPG_IHC_DOMAIN12_POWER_UP_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE22	Level	
#define DCN_1_0__CTXID__DCPG_DCFE12_POWER_UP_INT	4

#define DCN_1_0__SRCID__DCPG_DCFE13_POWER_UP_INT	0x4E	// Display pipe5 power up interrupt 	DCPG_IHC_DOMAIN13_POWER_UP_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE22	Level	
#define DCN_1_0__CTXID__DCPG_DCFE13_POWER_UP_INT	5

#define DCN_1_0__SRCID__DCPG_DCFE14_POWER_UP_INT	0x4E	// Display pipe6 power up interrupt 	DCPG_IHC_DOMAIN14_POWER_UP_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE22	Level	
#define DCN_1_0__CTXID__DCPG_DCFE14_POWER_UP_INT	6

#define DCN_1_0__SRCID__DCPG_DCFE15_POWER_UP_INT	0x4E	// Display pipe7 power up interrupt 	DCPG_IHC_DOMAIN15_POWER_UP_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE22	Level	
#define DCN_1_0__CTXID__DCPG_DCFE15_POWER_UP_INT	7

#define DCN_1_0__SRCID__DCPG_DCFE8_POWER_DOWN_INT	0x4E	// Display pipe0 power down interrupt 	DCPG_IHC_DOMAIN8_POWER_DOWN_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE22	Level	
#define DCN_1_0__CTXID__DCPG_DCFE8_POWER_DOWN_INT	8

#define DCN_1_0__SRCID__DCPG_DCFE9_POWER_DOWN_INT	0x4E	// Display pipe1 power down interrupt 	DCPG_IHC_DOMAIN9_POWER_DOWN_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE22	Level	
#define DCN_1_0__CTXID__DCPG_DCFE9_POWER_DOWN_INT	9

#define DCN_1_0__SRCID__DCPG_DCFE10_POWER_DOWN_INT	0x4E	// Display pipe2 power down interrupt 	DCPG_IHC_DOMAIN10_POWER_DOWN_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE22	Level	
#define DCN_1_0__CTXID__DCPG_DCFE10_POWER_DOWN_INT	10

#define DCN_1_0__SRCID__DCPG_DCFE11_POWER_DOWN_INT	0x4E	// Display pipe3 power down interrupt 	DCPG_IHC_DOMAIN11_POWER_DOWN_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE22	Level	
#define DCN_1_0__CTXID__DCPG_DCFE11_POWER_DOWN_INT	11

#define DCN_1_0__SRCID__DCPG_DCFE12_POWER_DOWN_INT	0x4E	// Display pipe4 power down interrupt 	DCPG_IHC_DOMAIN12_POWER_DOWN_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE22	Level	
#define DCN_1_0__CTXID__DCPG_DCFE12_POWER_DOWN_INT	12

#define DCN_1_0__SRCID__DCPG_DCFE13_POWER_DOWN_INT	0x4E	// Display pipe5 power down interrupt 	DCPG_IHC_DOMAIN13_POWER_DOWN_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE22	Level	
#define DCN_1_0__CTXID__DCPG_DCFE13_POWER_DOWN_INT	13

#define DCN_1_0__SRCID__DCPG_DCFE14_POWER_DOWN_INT	0x4E	// Display pipe6 power down interrupt 	DCPG_IHC_DOMAIN14_POWER_DOWN_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE22	Level	
#define DCN_1_0__CTXID__DCPG_DCFE14_POWER_DOWN_INT	14

#define DCN_1_0__SRCID__DCPG_DCFE15_POWER_DOWN_INT	0x4E	// Display pipe7 power down interrupt 	DCPG_IHC_DOMAIN15_POWER_DOWN_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE22	Level	
#define DCN_1_0__CTXID__DCPG_DCFE15_POWER_DOWN_INT	15

#define DCN_1_0__SRCID__HUBP0_FLIP_INTERRUPT	0x4F	// Flip interrupt is generated when flip request is accepted by flip logic and surface is flipped from old surface to new surface.HUBP0_IHC_FLIP_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE17	Level / Pulse
#define DCN_1_0__SRCID__HUBP1_FLIP_INTERRUPT	0x50	// Flip interrupt is generated when flip request is accepted by flip logic and surface is flipped from old surface to new surface.HUBP1_IHC_FLIP_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE17	Level / Pulse
#define DCN_1_0__SRCID__HUBP2_FLIP_INTERRUPT	0x51	// Flip interrupt is generated when flip request is accepted by flip logic and surface is flipped from old surface to new surface.HUBP2_IHC_FLIP_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE17	Level / Pulse
#define DCN_1_0__SRCID__HUBP3_FLIP_INTERRUPT	0x52	// Flip interrupt is generated when flip request is accepted by flip logic and surface is flipped from old surface to new surface.HUBP3_IHC_FLIP_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE17	Level / Pulse
#define DCN_1_0__SRCID__HUBP4_FLIP_INTERRUPT	0x53	// Flip interrupt is generated when flip request is accepted by flip logic and surface is flipped from old surface to new surface.HUBP4_IHC_FLIP_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE17	Level / Pulse
#define DCN_1_0__SRCID__HUBP5_FLIP_INTERRUPT	0x54	// Flip interrupt is generated when flip request is accepted by flip logic and surface is flipped from old surface to new surface.HUBP5_IHC_FLIP_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE17	Level / Pulse
#define DCN_1_0__SRCID__HUBP6_FLIP_INTERRUPT	0x55	// Flip interrupt is generated when flip request is accepted by flip logic and surface is flipped from old surface to new surface.HUBP6_IHC_FLIP_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE17	Level / Pulse
#define DCN_1_0__SRCID__HUBP7_FLIP_INTERRUPT	0x56	// Flip interrupt is generated when flip request is accepted by flip logic and surface is flipped from old surface to new surface.HUBP7_IHC_FLIP_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE17	Level / Pulse

#define DCN_1_0__SRCID__OTG0_IHC_V_UPDATE_NO_LOCK_INTERRUPT	0x57	// "OTG0 VUPDATE event without lock interrupt, VUPDATE is update event for double buffered registers"	OTG0_IHC_V_UPDATE_NO_LOCK_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE22	Level / Pulse
#define DCN_1_0__SRCID__OTG1_IHC_V_UPDATE_NO_LOCK_INTERRUPT	0x58	// "OTG1 VUPDATE event without lock interrupt, VUPDATE is update event for double buffered registers"	OTG1_IHC_V_UPDATE_NO_LOCK_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE22	Level / Pulse
#define DCN_1_0__SRCID__OTG2_IHC_V_UPDATE_NO_LOCK_INTERRUPT	0x59	// "OTG2 VUPDATE event without lock interrupt, VUPDATE is update event for double buffered registers"	OTG2_IHC_V_UPDATE_NO_LOCK_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE22	Level / Pulse
#define DCN_1_0__SRCID__OTG3_IHC_V_UPDATE_NO_LOCK_INTERRUPT	0x5A	// "OTG3 VUPDATE event without lock interrupt, VUPDATE is update event for double buffered registers"	OTG3_IHC_V_UPDATE_NO_LOCK_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE22	Level / Pulse
#define DCN_1_0__SRCID__OTG4_IHC_V_UPDATE_NO_LOCK_INTERRUPT	0x5B	// "OTG4 VUPDATE event without lock interrupt, VUPDATE is update event for double buffered registers"	OTG4_IHC_V_UPDATE_NO_LOCK_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE22	Level / Pulse
#define DCN_1_0__SRCID__OTG5_IHC_V_UPDATE_NO_LOCK_INTERRUPT	0x5C	// "OTG5 VUPDATE event without lock interrupt, VUPDATE is update event for double buffered registers"	OTG5_IHC_V_UPDATE_NO_LOCK_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE22	Level / Pulse

#define DCN_1_0__SRCID__HUBP0_FLIP_AWAY_INTERRUPT	0x5D	// Flip_away interrupt is generated when all data for old surface is returned and old surface is not used again after the surface flip.HUBP0_IHC_FLIP_AWAY_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE17	Level / Pulse
#define DCN_1_0__SRCID__HUBP1_FLIP_AWAY_INTERRUPT	0x5E	// Flip_away interrupt is generated when all data for old surface is returned and old surface is not used again after the surface flip.HUBP1_IHC_FLIP_AWAY_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE17	Level / Pulse
#define DCN_1_0__SRCID__HUBP2_FLIP_AWAY_INTERRUPT	0x5F	// Flip_away interrupt is generated when all data for old surface is returned and old surface is not used again after the surface flip.HUBP2_IHC_FLIP_AWAY_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE17	Level / Pulse
#define DCN_1_0__SRCID__HUBP3_FLIP_AWAY_INTERRUPT	0x60	// Flip_away interrupt is generated when all data for old surface is returned and old surface is not used again after the surface flip.HUBP3_IHC_FLIP_AWAY_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE17	Level / Pulse
#define DCN_1_0__SRCID__HUBP4_FLIP_AWAY_INTERRUPT	0x61	// Flip_away interrupt is generated when all data for old surface is returned and old surface is not used again after the surface flip.HUBP4_IHC_FLIP_AWAY_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE17	Level / Pulse
#define DCN_1_0__SRCID__HUBP5_FLIP_AWAY_INTERRUPT	0x62	// Flip_away interrupt is generated when all data for old surface is returned and old surface is not used again after the surface flip.HUBP5_IHC_FLIP_AWAY_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE17	Level / Pulse
#define DCN_1_0__SRCID__HUBP6_FLIP_AWAY_INTERRUPT	0x63	// Flip_away interrupt is generated when all data for old surface is returned and old surface is not used again after the surface flip.HUBP6_IHC_FLIP_AWAY_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE17	Level / Pulse
#define DCN_1_0__SRCID__HUBP7_FLIP_AWAY_INTERRUPT	0x64	// Flip_away interrupt is generated when all data for old surface is returned and old surface is not used again after the surface flip.HUBP7_IHC_FLIP_AWAY_INTERRUPT	DISP_INTERRUPT_STATUS_CONTINUE17	Level / Pulse

#define DCN_1_0__SRCID__DMCUB_OUTBOX_HIGH_PRIORITY_READY_INT       0x68
#define DCN_1_0__CTXID__DMCUB_OUTBOX_HIGH_PRIORITY_READY_INT       6
#define DCN_1_0__SRCID__DMCUB_OUTBOX_LOW_PRIORITY_READY_INT        0x68 // DMCUB_IHC_outbox1_ready_int IHC_DMCUB_outbox1_ready_int_ack DMCUB_OUTBOX_LOW_PRIORITY_READY_INTERRUPT DISP_INTERRUPT_STATUS_CONTINUE24 Level/Pulse
#define DCN_1_0__CTXID__DMCUB_OUTBOX_LOW_PRIORITY_READY_INT        8

#endif // __IRQSRCS_DCN_1_0_H__
