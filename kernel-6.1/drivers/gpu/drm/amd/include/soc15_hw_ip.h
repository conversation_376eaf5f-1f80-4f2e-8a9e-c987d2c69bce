/*
 * Copyright (C) 2018  Advanced Micro Devices, Inc.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a
 * copy of this software and associated documentation files (the "Software"),
 * to deal in the Software without restriction, including without limitation
 * the rights to use, copy, modify, merge, publish, distribute, sublicense,
 * and/or sell copies of the Software, and to permit persons to whom the
 * Software is furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included
 * in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL
 * THE COPYRIGHT HOLDER(S) BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN
 * AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
 * CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */
#ifndef _soc15_hw_ip_HEADER
#define _soc15_hw_ip_HEADER

// HW ID
#define MP1_HWID                                           1
#define MP2_HWID                                           2
#define THM_HWID                                           3
#define SMUIO_HWID                                         4
#define FUSE_HWID                                          5
#define CLKA_HWID                                          6
#define PWR_HWID                                          10
#define GC_HWID                                           11
#define UVD_HWID                                          12
#define VCN_HWID                                          UVD_HWID
#define AUDIO_AZ_HWID                                     13
#define ACP_HWID                                          14
#define DCI_HWID                                          15
#define DMU_HWID                                         271
#define DCO_HWID                                          16
#define DIO_HWID                                         272
#define XDMA_HWID                                         17
#define DCEAZ_HWID                                        18
#define DAZ_HWID                                         274
#define SDPMUX_HWID                                       19
#define NTB_HWID                                          20
#define IOHC_HWID                                         24
#define L2IMU_HWID                                        28
#define VCE_HWID                                          32
#define MMHUB_HWID                                        34
#define ATHUB_HWID                                        35
#define DBGU_NBIO_HWID                                    36
#define DFX_HWID                                          37
#define DBGU0_HWID                                        38
#define DBGU1_HWID                                        39
#define OSSSYS_HWID                                       40
#define HDP_HWID                                          41
#define SDMA0_HWID                                        42
#define SDMA1_HWID                                        43
#define ISP_HWID                                          44
#define DBGU_IO_HWID                                      45
#define DF_HWID                                           46
#define CLKB_HWID                                         47
#define FCH_HWID                                          48
#define DFX_DAP_HWID                                      49
#define L1IMU_PCIE_HWID                                   50
#define L1IMU_NBIF_HWID                                   51
#define L1IMU_IOAGR_HWID                                  52
#define L1IMU3_HWID                                       53
#define L1IMU4_HWID                                       54
#define L1IMU5_HWID                                       55
#define L1IMU6_HWID                                       56
#define L1IMU7_HWID                                       57
#define L1IMU8_HWID                                       58
#define L1IMU9_HWID                                       59
#define L1IMU10_HWID                                      60
#define L1IMU11_HWID                                      61
#define L1IMU12_HWID                                      62
#define L1IMU13_HWID                                      63
#define L1IMU14_HWID                                      64
#define L1IMU15_HWID                                      65
#define WAFLC_HWID                                        66
#define FCH_USB_PD_HWID                                   67
#define SDMA2_HWID                                        68
#define SDMA3_HWID                                        69
#define PCIE_HWID                                         70
#define PCS_HWID                                          80
#define DDCL_HWID                                         89
#define SST_HWID                                          90
#define LSDMA_HWID                                        91
#define IOAGR_HWID                                       100
#define NBIF_HWID                                        108
#define IOAPIC_HWID                                      124
#define SYSTEMHUB_HWID                                   128
#define NTBCCP_HWID                                      144
#define UMC_HWID                                         150
#define SATA_HWID                                        168
#define USB_HWID                                         170
#define CCXSEC_HWID                                      176
#define XGMI_HWID                                        200
#define XGBE_HWID                                        216
#define MP0_HWID                                         255

#endif
