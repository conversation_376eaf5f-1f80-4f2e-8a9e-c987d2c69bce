/*
 * Copyright 2019 Advanced Micro Devices, Inc.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a
 * copy of this software and associated documentation files (the "Software"),
 * to deal in the Software without restriction, including without limitation
 * the rights to use, copy, modify, merge, publish, distribute, sublicense,
 * and/or sell copies of the Software, and to permit persons to whom the
 * Software is furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL
 * THE COPYRIGHT HOLDER(S) OR AUTHOR(S) BE LIABLE FOR ANY CLAIM, DAMAGES OR
 * OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE,
 * ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 * OTHER DEALINGS IN THE SOFTWARE.
 *
 */
#ifndef __SIENNA_CICHLID_PPT_H__
#define __SIENNA_CICHLID_PPT_H__

typedef enum {
  POWER_SOURCE_AC,
  POWER_SOURCE_DC,
  POWER_SOURCE_COUNT,
} POWER_SOURCE_e;

#define SIENNA_CICHLID_UMD_PSTATE_PROFILING_GFXCLK    1825
#define SIENNA_CICHLID_UMD_PSTATE_PROFILING_SOCCLK    960
#define SIENNA_CICHLID_UMD_PSTATE_PROFILING_MEMCLK    1000

#define DIMGREY_CAVEFISH_UMD_PSTATE_PROFILING_GFXCLK 1950
#define DIMGREY_CAVEFISH_UMD_PSTATE_PROFILING_SOCCLK 960
#define DIMGREY_CAVEFISH_UMD_PSTATE_PROFILING_MEMCLK 676

#define BEIGE_GOBY_UMD_PSTATE_PROFILING_GFXCLK 2200
#define BEIGE_GOBY_UMD_PSTATE_PROFILING_SOCCLK 960
#define BEIGE_GOBY_UMD_PSTATE_PROFILING_MEMCLK 1000

extern void sienna_cichlid_set_ppt_funcs(struct smu_context *smu);

#endif
