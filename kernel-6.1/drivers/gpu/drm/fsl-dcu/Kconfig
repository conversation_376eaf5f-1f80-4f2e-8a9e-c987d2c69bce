# SPDX-License-Identifier: GPL-2.0-only
config DRM_FSL_DCU
	tristate "DRM Support for Freescale DCU"
	depends on DRM && OF && ARM && COMMON_CLK
	select BACKLIGHT_CLASS_DEVICE
	select DRM_GEM_DMA_HELPER
	select DRM_KMS_HELPER
	select DRM_PANEL
	select R<PERSON><PERSON>P_MMIO
	select VIDEOMODE_HELPERS
	help
	  Choose this option if you have an Freescale DCU chipset.
	  If M is selected the module will be called fsl-dcu-drm.
