/* SPDX-License-Identifier: MIT */
/*
 * Copyright © 2022 Intel Corporation
 */

#ifndef __INTEL_SNPS_PHY_REGS__
#define __INTEL_SNPS_PHY_REGS__

#include "i915_reg_defs.h"

#define _SNPS_PHY_A_BASE			0x168000
#define _SNPS_PHY_B_BASE			0x169000
#define _SNPS_PHY(phy)				_PHY(phy, \
						     _SNPS_PHY_A_BASE, \
						     _SNPS_PHY_B_BASE)
#define _SNPS2(phy, reg)			(_SNPS_PHY(phy) - \
						 _SNPS_PHY_A_BASE + (reg))
#define _MMIO_SNPS(phy, reg)			_MMIO(_SNPS2(phy, reg))
#define _MMIO_SNPS_LN(ln, phy, reg)		_MMIO(_SNPS2(phy, \
							     (reg) + (ln) * 0x10))

#define SNPS_PHY_MPLLB_CP(phy)			_MMIO_SNPS(phy, 0x168000)
#define   SNPS_PHY_MPLLB_CP_INT			REG_GENMASK(31, 25)
#define   SNPS_PHY_MPLLB_CP_INT_GS		REG_GENMASK(23, 17)
#define   SNPS_PHY_MPLLB_CP_PROP		REG_GENMASK(15, 9)
#define   SNPS_PHY_MPLLB_CP_PROP_GS		REG_GENMASK(7, 1)

#define SNPS_PHY_MPLLB_DIV(phy)			_MMIO_SNPS(phy, 0x168004)
#define   SNPS_PHY_MPLLB_FORCE_EN		REG_BIT(31)
#define   SNPS_PHY_MPLLB_DIV_CLK_EN		REG_BIT(30)
#define   SNPS_PHY_MPLLB_DIV5_CLK_EN		REG_BIT(29)
#define   SNPS_PHY_MPLLB_V2I			REG_GENMASK(27, 26)
#define   SNPS_PHY_MPLLB_FREQ_VCO		REG_GENMASK(25, 24)
#define   SNPS_PHY_MPLLB_DIV_MULTIPLIER		REG_GENMASK(23, 16)
#define   SNPS_PHY_MPLLB_PMIX_EN		REG_BIT(10)
#define   SNPS_PHY_MPLLB_DP2_MODE		REG_BIT(9)
#define   SNPS_PHY_MPLLB_WORD_DIV2_EN		REG_BIT(8)
#define   SNPS_PHY_MPLLB_TX_CLK_DIV		REG_GENMASK(7, 5)
#define   SNPS_PHY_MPLLB_SHIM_DIV32_CLK_SEL	REG_BIT(0)

#define SNPS_PHY_MPLLB_FRACN1(phy)		_MMIO_SNPS(phy, 0x168008)
#define   SNPS_PHY_MPLLB_FRACN_EN		REG_BIT(31)
#define   SNPS_PHY_MPLLB_FRACN_CGG_UPDATE_EN	REG_BIT(30)
#define   SNPS_PHY_MPLLB_FRACN_DEN		REG_GENMASK(15, 0)

#define SNPS_PHY_MPLLB_FRACN2(phy)		_MMIO_SNPS(phy, 0x16800C)
#define   SNPS_PHY_MPLLB_FRACN_REM		REG_GENMASK(31, 16)
#define   SNPS_PHY_MPLLB_FRACN_QUOT		REG_GENMASK(15, 0)

#define SNPS_PHY_MPLLB_SSCEN(phy)		_MMIO_SNPS(phy, 0x168014)
#define   SNPS_PHY_MPLLB_SSC_EN			REG_BIT(31)
#define   SNPS_PHY_MPLLB_SSC_UP_SPREAD		REG_BIT(30)
#define   SNPS_PHY_MPLLB_SSC_PEAK		REG_GENMASK(29, 10)

#define SNPS_PHY_MPLLB_SSCSTEP(phy)		_MMIO_SNPS(phy, 0x168018)
#define   SNPS_PHY_MPLLB_SSC_STEPSIZE		REG_GENMASK(31, 11)

#define SNPS_PHY_MPLLB_DIV2(phy)		_MMIO_SNPS(phy, 0x16801C)
#define   SNPS_PHY_MPLLB_HDMI_PIXEL_CLK_DIV	REG_GENMASK(19, 18)
#define   SNPS_PHY_MPLLB_HDMI_DIV		REG_GENMASK(17, 15)
#define   SNPS_PHY_MPLLB_REF_CLK_DIV		REG_GENMASK(14, 12)
#define   SNPS_PHY_MPLLB_MULTIPLIER		REG_GENMASK(11, 0)

#define SNPS_PHY_REF_CONTROL(phy)		_MMIO_SNPS(phy, 0x168188)
#define   SNPS_PHY_REF_CONTROL_REF_RANGE	REG_GENMASK(31, 27)

#define SNPS_PHY_TX_REQ(phy)			_MMIO_SNPS(phy, 0x168200)
#define   SNPS_PHY_TX_REQ_LN_DIS_PWR_STATE_PSR	REG_GENMASK(31, 30)

#define SNPS_PHY_TX_EQ(ln, phy)			_MMIO_SNPS_LN(ln, phy, 0x168300)
#define   SNPS_PHY_TX_EQ_MAIN			REG_GENMASK(23, 18)
#define   SNPS_PHY_TX_EQ_POST			REG_GENMASK(15, 10)
#define   SNPS_PHY_TX_EQ_PRE			REG_GENMASK(7, 2)

#endif /* __INTEL_SNPS_PHY_REGS__ */
