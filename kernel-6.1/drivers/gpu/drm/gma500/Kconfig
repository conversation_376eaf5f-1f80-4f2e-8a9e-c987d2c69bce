# SPDX-License-Identifier: GPL-2.0-only
config DRM_GMA500
	tristate "Intel GMA500/600/3600/3650 KMS Framebuffer"
	depends on DRM && PCI && X86 && MMU
	select DRM_KMS_HELPER
	# GMA500 depends on ACPI_VIDEO when ACPI is enabled, just like i915
	select <PERSON>PI_VIDEO if ACPI
	select BAC<PERSON><PERSON><PERSON>HT_CLASS_DEVICE if ACPI
	select INPUT if ACPI
	select X86_PLATFORM_DEVICES if ACPI
	select ACPI_WMI if ACPI
	help
	  Say yes for an experimental 2D KMS framebuffer driver for the
	  Intel GMA500 (Poulsbo), Intel GMA600 (Moorestown/Oak Trail) and
	  Intel GMA3600/3650 (Cedar Trail).
