# SPDX-License-Identifier: MIT

config DRM_DP
	bool "DRM DisplayPort support"
	depends on DRM
	depends on DRM_KMS_HELPER
	default y if DRM_ANALOGIX_DP
	default y if !ROCKCHIP_MINI_KERNEL
	help
	  Choose this option to support DP interface.

config DRM_DP_AUX_BUS
	tristate
	depends on DRM
	depends on OF || COMPILE_TEST

config DRM_DISPLAY_HELPER
	tristate
	depends on DRM
	help
	  DRM helpers for display adapters.

config DRM_DISPLAY_DP_HELPER
	bool
	depends on DRM_DISPLAY_HELPER
	help
	  DRM display helpers for DisplayPort.

config DRM_DISPLAY_HDCP_HELPER
	bool
	depends on DRM_DISPLAY_HELPER
	help
	  DRM display helpers for HDCP.

config DRM_DISPLAY_HDMI_HELPER
	bool
	depends on DRM_DISPLAY_HELPER
	help
	  DRM display helpers for HDMI.

config DRM_DP_AUX_CHARDEV
	bool "DRM DP AUX Interface"
	depends on DRM && DRM_DISPL<PERSON>Y_<PERSON>EL<PERSON><PERSON>
	select DRM_DISPLAY_DP_HELPER
	help
	  Choose this option to enable a /dev/drm_dp_auxN node that allows to
	  read and write values to arbitrary DPCD registers on the DP aux
	  channel.

config DRM_DP_CEC
	bool "Enable DisplayPort CEC-Tunneling-over-AUX HDMI support"
	depends on DRM && DRM_DISPLAY_HELPER
	select DRM_DISPLAY_DP_HELPER
	select CEC_CORE
	help
	  Choose this option if you want to enable HDMI CEC support for
	  DisplayPort/USB-C to HDMI adapters.

	  Note: not all adapters support this feature, and even for those
	  that do support this they often do not hook up the CEC pin.
