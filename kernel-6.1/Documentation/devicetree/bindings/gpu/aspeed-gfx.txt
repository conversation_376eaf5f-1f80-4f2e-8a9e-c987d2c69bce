Device tree configuration for the GFX display device on the ASPEED SoCs

Required properties:
  - compatible
    * Must be one of the following:
      + aspeed,ast2500-gfx
      + aspeed,ast2400-gfx
    * In addition, the ASPEED pinctrl bindings require the 'syscon' property to
      be present

  - reg: Physical base address and length of the GFX registers

  - interrupts: interrupt number for the GFX device

  - clocks: clock number used to generate the pixel clock

  - resets: reset line that must be released to use the GFX device

  - memory-region:
    Phandle to a memory region to allocate from, as defined in
    Documentation/devicetree/bindings/reserved-memory/reserved-memory.txt


Example:

gfx: display@1e6e6000 {
	compatible = "aspeed,ast2500-gfx", "syscon";
	reg = <0x1e6e6000 0x1000>;
	reg-io-width = <4>;
	clocks = <&syscon ASPEED_CLK_GATE_D1CLK>;
	resets = <&syscon ASPEED_RESET_CRT1>;
	interrupts = <0x19>;
	memory-region = <&gfx_memory>;
};

gfx_memory: framebuffer {
	size = <0x01000000>;
	alignment = <0x01000000>;
	compatible = "shared-dma-pool";
	reusable;
};
