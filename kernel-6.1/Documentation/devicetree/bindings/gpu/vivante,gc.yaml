# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/gpu/vivante,gc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Vivante GPU Bindings

description: Vivante GPU core devices

maintainers:
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    const: vivante,gc

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  '#cooling-cells':
    const: 2

  assigned-clock-parents: true
  assigned-clock-rates: true
  assigned-clocks: true

  clocks:
    items:
      - description: AXI/master interface clock
      - description: GPU core clock
      - description: Shader clock (only required if GPU has feature PIPE_3D)
      - description: AHB/slave interface clock (only required if GPU can gate
          slave interface independently)
    minItems: 1

  clock-names:
    items:
      enum: [ bus, core, shader, reg ]
    minItems: 1
    maxItems: 4

  resets:
    maxItems: 1

  power-domains:
    maxItems: 1

required:
  - compatible
  - reg
  - interrupts
  - clocks
  - clock-names

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/imx6qdl-clock.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    gpu@130000 {
      compatible = "vivante,gc";
      reg = <0x00130000 0x4000>;
      interrupts = <0 9 IRQ_TYPE_LEVEL_HIGH>;
      clocks = <&clks IMX6QDL_CLK_GPU3D_AXI>,
               <&clks IMX6QDL_CLK_GPU3D_CORE>,
               <&clks IMX6QDL_CLK_GPU3D_SHADER>;
      clock-names = "bus", "core", "shader";
      power-domains = <&gpc 1>;
    };

...
