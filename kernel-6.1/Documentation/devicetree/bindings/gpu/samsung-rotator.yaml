# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/gpu/samsung-rotator.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Samsung SoC Image Rotator

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - "samsung,s5pv210-rotator"
      - "samsung,exynos4210-rotator"
      - "samsung,exynos4212-rotator"
      - "samsung,exynos5250-rotator"
  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  iommus:
    maxItems: 1

  power-domains:
    maxItems: 1

  clocks:
    maxItems: 1

  clock-names:
    items:
      - const: rotator

required:
  - compatible
  - reg
  - interrupts
  - clocks
  - clock-names

additionalProperties: false

examples:
  - |
    rotator@12810000 {
        compatible = "samsung,exynos4210-rotator";
        reg = <0x12810000 0x1000>;
        interrupts = <0 83 0>;
        clocks = <&clock 278>;
        clock-names = "rotator";
    };
