NVIDIA Tegra Graphics Processing Units

Required properties:
- compatible: "nvidia,<gpu>"
  Currently recognized values:
  - nvidia,gk20a
  - nvidia,gm20b
  - nvidia,gp10b
  - nvidia,gv11b
- reg: Physical base address and length of the controller's registers.
  Must contain two entries:
  - first entry for bar0
  - second entry for bar1
- interrupts: Must contain an entry for each entry in interrupt-names.
  See ../interrupt-controller/interrupts.txt for details.
- interrupt-names: Must include the following entries:
  - stall
  - nonstall
- vdd-supply: regulator for supply voltage. Only required for GPUs not using
  power domains.
- clocks: Must contain an entry for each entry in clock-names.
  See ../clocks/clock-bindings.txt for details.
- clock-names: Must include the following entries:
  - gpu
  - pwr
If the compatible string is "nvidia,gm20b", then the following clock
is also required:
  - ref
If the compatible string is "nvidia,gv11b", then the following clock is also
required:
  - fuse
- resets: Must contain an entry for each entry in reset-names.
  See ../reset/reset.txt for details.
- reset-names: Must include the following entries:
  - gpu
- power-domains: GPUs that make use of power domains can define this property
  instead of vdd-supply. Currently "nvidia,gp10b" makes use of this.

Optional properties:
- iommus: A reference to the IOMMU. See ../iommu/iommu.txt for details.

Example for GK20A:

	gpu@57000000 {
		compatible = "nvidia,gk20a";
		reg = <0x0 0x57000000 0x0 0x01000000>,
		      <0x0 0x58000000 0x0 0x01000000>;
		interrupts = <GIC_SPI 157 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 158 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "stall", "nonstall";
		vdd-supply = <&vdd_gpu>;
		clocks = <&tegra_car TEGRA124_CLK_GPU>,
			 <&tegra_car TEGRA124_CLK_PLL_P_OUT5>;
		clock-names = "gpu", "pwr";
		resets = <&tegra_car 184>;
		reset-names = "gpu";
		iommus = <&mc TEGRA_SWGROUP_GPU>;
	};

Example for GM20B:

	gpu@57000000 {
		compatible = "nvidia,gm20b";
		reg = <0x0 0x57000000 0x0 0x01000000>,
		      <0x0 0x58000000 0x0 0x01000000>;
		interrupts = <GIC_SPI 157 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 158 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "stall", "nonstall";
		clocks = <&tegra_car TEGRA210_CLK_GPU>,
			 <&tegra_car TEGRA210_CLK_PLL_P_OUT5>,
			 <&tegra_car TEGRA210_CLK_PLL_G_REF>;
		clock-names = "gpu", "pwr", "ref";
		resets = <&tegra_car 184>;
		reset-names = "gpu";
		iommus = <&mc TEGRA_SWGROUP_GPU>;
	};

Example for GP10B:

	gpu@17000000 {
		compatible = "nvidia,gp10b";
		reg = <0x0 0x17000000 0x0 0x1000000>,
		      <0x0 0x18000000 0x0 0x1000000>;
		interrupts = <GIC_SPI 70 IRQ_TYPE_LEVEL_HIGH
			      GIC_SPI 71 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "stall", "nonstall";
		clocks = <&bpmp TEGRA186_CLK_GPCCLK>,
			 <&bpmp TEGRA186_CLK_GPU>;
		clock-names = "gpu", "pwr";
		resets = <&bpmp TEGRA186_RESET_GPU>;
		reset-names = "gpu";
		power-domains = <&bpmp TEGRA186_POWER_DOMAIN_GPU>;
		iommus = <&smmu TEGRA186_SID_GPU>;
	};

Example for GV11B:

	gpu@17000000 {
		compatible = "nvidia,gv11b";
		reg = <0x17000000 0x1000000>,
		      <0x18000000 0x1000000>;
		interrupts = <GIC_SPI 70 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 71 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "stall", "nonstall";
		clocks = <&bpmp TEGRA194_CLK_GPCCLK>,
			 <&bpmp TEGRA194_CLK_GPU_PWR>,
			 <&bpmp TEGRA194_CLK_FUSE>;
		clock-names = "gpu", "pwr", "fuse";
		resets = <&bpmp TEGRA194_RESET_GPU>;
		reset-names = "gpu";
		dma-coherent;

		power-domains = <&bpmp TEGRA194_POWER_DOMAIN_GPU>;
		iommus = <&smmu TEGRA194_SID_GPU>;
	};
