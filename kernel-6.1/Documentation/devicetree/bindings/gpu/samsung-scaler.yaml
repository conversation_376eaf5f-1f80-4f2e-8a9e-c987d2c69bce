# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/gpu/samsung-scaler.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Samsung Exynos SoC Image Scaler

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - samsung,exynos5420-scaler
      - samsung,exynos5433-scaler

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks: {}
  clock-names: {}
  iommus: {}
  power-domains: {}

if:
  properties:
    compatible:
      contains:
        const: samsung,exynos5420-scaler

then:
  properties:
    clocks:
      items:
        - description: mscl clock

    clock-names:
      items:
        - const: mscl

else:
  properties:
    clocks:
      items:
        - description: pclk clock
        - description: aclk clock
        - description: aclk_xiu clock

    clock-names:
      items:
        - const: pclk
        - const: aclk
        - const: aclk_xiu

required:
  - compatible
  - reg
  - interrupts
  - clocks
  - clock-names

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/exynos5420.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>

    scaler@12800000 {
        compatible = "samsung,exynos5420-scaler";
        reg = <0x12800000 0x1294>;
        interrupts = <GIC_SPI 220 IRQ_TYPE_LEVEL_HIGH>;
        clocks = <&clock CLK_MSCL0>;
        clock-names = "mscl";
    };

...
