# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/gpu/samsung-g2d.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Samsung SoC 2D Graphics Accelerator

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - samsung,s5pv210-g2d    # in S5PV210 & Exynos4210 SoC
      - samsung,exynos4212-g2d # in Exynos4x12 SoCs
      - samsung,exynos5250-g2d

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks: {}
  clock-names: {}
  iommus: {}
  power-domains: {}

if:
  properties:
    compatible:
      contains:
        const: samsung,exynos5250-g2d

then:
  properties:
    clocks:
      items:
        - description: fimg2d clock
    clock-names:
      items:
        - const: fimg2d

else:
  properties:
    clocks:
      items:
        - description: sclk_fimg2d clock
        - description: fimg2d clock
    clock-names:
      items:
        - const: sclk_fimg2d
        - const: fimg2d

required:
  - compatible
  - reg
  - interrupts
  - clocks
  - clock-names

additionalProperties: false

examples:
  - |
    g2d@12800000 {
        compatible = "samsung,s5pv210-g2d";
        reg = <0x12800000 0x1000>;
        interrupts = <0 89 0>;
        clocks = <&clock 177>, <&clock 277>;
        clock-names = "sclk_fimg2d", "fimg2d";
    };

...
