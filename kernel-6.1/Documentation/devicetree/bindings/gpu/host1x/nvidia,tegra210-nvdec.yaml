# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: "http://devicetree.org/schemas/gpu/host1x/nvidia,tegra210-nvdec.yaml#"
$schema: "http://devicetree.org/meta-schemas/core.yaml#"

title: Device tree binding for NVIDIA Tegra NVDEC

description: |
  NVDEC is the hardware video decoder present on NVIDIA Tegra210
  and newer chips. It is located on the Host1x bus and typically
  programmed through Host1x channels.

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON><PERSON><PERSON> <<EMAIL>>

properties:
  $nodename:
    pattern: "^nvdec@[0-9a-f]*$"

  compatible:
    enum:
      - nvidia,tegra210-nvdec
      - nvidia,tegra186-nvdec
      - nvidia,tegra194-nvdec

  reg:
    maxItems: 1

  clocks:
    maxItems: 1

  clock-names:
    items:
      - const: nvdec

  resets:
    maxItems: 1

  reset-names:
    items:
      - const: nvdec

  power-domains:
    maxItems: 1

  iommus:
    maxItems: 1

  dma-coherent: true

  interconnects:
    items:
      - description: DMA read memory client
      - description: DMA read 2 memory client
      - description: DMA write memory client

  interconnect-names:
    items:
      - const: dma-mem
      - const: read-1
      - const: write

  nvidia,host1x-class:
    description: |
      Host1x class of the engine, used to specify the targeted engine
      when programming the engine through Host1x channels or when
      configuring engine-specific behavior in Host1x.
    default: 0xf0
    $ref: /schemas/types.yaml#/definitions/uint32

required:
  - compatible
  - reg
  - clocks
  - clock-names
  - resets
  - reset-names
  - power-domains

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/tegra186-clock.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/memory/tegra186-mc.h>
    #include <dt-bindings/power/tegra186-powergate.h>
    #include <dt-bindings/reset/tegra186-reset.h>

    nvdec@15480000 {
            compatible = "nvidia,tegra186-nvdec";
            reg = <0x15480000 0x40000>;
            clocks = <&bpmp TEGRA186_CLK_NVDEC>;
            clock-names = "nvdec";
            resets = <&bpmp TEGRA186_RESET_NVDEC>;
            reset-names = "nvdec";

            power-domains = <&bpmp TEGRA186_POWER_DOMAIN_NVDEC>;
            interconnects = <&mc TEGRA186_MEMORY_CLIENT_NVDECSRD &emc>,
                            <&mc TEGRA186_MEMORY_CLIENT_NVDECSRD1 &emc>,
                            <&mc TEGRA186_MEMORY_CLIENT_NVDECSWR &emc>;
            interconnect-names = "dma-mem", "read-1", "write";
            iommus = <&smmu TEGRA186_SID_NVDEC>;
    };
