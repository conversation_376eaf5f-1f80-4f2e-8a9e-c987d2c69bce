# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/addac/adi,ad74413r.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Analog Devices AD74412R/AD74413R device

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description: |
  The AD74412R and AD74413R are quad-channel software configurable input/output
  solutions for building and process control applications. They contain
  functionality for analog output, analog input, digital input, resistance
  temperature detector, and thermocouple measurements integrated
  into a single chip solution with an SPI interface.
  The devices feature a 16-bit ADC and four configurable 13-bit DACs to provide
  four configurable input/output channels and a suite of diagnostic functions.
  The AD74413R differentiates itself from the AD74412R by being HART-compatible.
    https://www.analog.com/en/products/ad74412r.html
    https://www.analog.com/en/products/ad74413r.html

properties:
  compatible:
    enum:
      - adi,ad74412r
      - adi,ad74413r

  reg:
    maxItems: 1

  '#address-cells':
    const: 1

  '#size-cells':
    const: 0

  spi-max-frequency:
    maximum: 1000000

  spi-cpol: true

  interrupts:
    maxItems: 1

  refin-supply: true

  shunt-resistor-micro-ohms:
    description:
      Shunt (sense) resistor value in micro-Ohms.
    default: 100000000

required:
  - compatible
  - reg
  - spi-max-frequency
  - spi-cpol
  - refin-supply

additionalProperties: false

patternProperties:
  "^channel@[0-3]$":
    type: object
    additionalProperties: false
    description: Represents the external channels which are connected to the device.

    properties:
      reg:
        description: |
          The channel number. It can have up to 4 channels numbered from 0 to 3.
        minimum: 0
        maximum: 3

      adi,ch-func:
        $ref: /schemas/types.yaml#/definitions/uint32
        description: |
          Channel function.
          HART functions are not supported on AD74412R.
          0 - CH_FUNC_HIGH_IMPEDANCE
          1 - CH_FUNC_VOLTAGE_OUTPUT
          2 - CH_FUNC_CURRENT_OUTPUT
          3 - CH_FUNC_VOLTAGE_INPUT
          4 - CH_FUNC_CURRENT_INPUT_EXT_POWER
          5 - CH_FUNC_CURRENT_INPUT_LOOP_POWER
          6 - CH_FUNC_RESISTANCE_INPUT
          7 - CH_FUNC_DIGITAL_INPUT_LOGIC
          8 - CH_FUNC_DIGITAL_INPUT_LOOP_POWER
          9 - CH_FUNC_CURRENT_INPUT_EXT_POWER_HART
          10 - CH_FUNC_CURRENT_INPUT_LOOP_POWER_HART
        minimum: 0
        maximum: 10
        default: 0

      adi,gpo-comparator:
        type: boolean
        description: |
          Whether to configure GPO as a comparator or not.
          When not configured as a comparator, the GPO will be treated as an
          output-only GPIO.

    required:
      - reg

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>
    #include <dt-bindings/interrupt-controller/irq.h>
    #include <dt-bindings/iio/addac/adi,ad74413r.h>

    spi {
      #address-cells = <1>;
      #size-cells = <0>;

      cs-gpios = <&gpio 17 GPIO_ACTIVE_LOW>;
      status = "okay";

      ad74413r@0 {
        compatible = "adi,ad74413r";
        reg = <0>;
        spi-max-frequency = <1000000>;
        spi-cpol;

        #address-cells = <1>;
        #size-cells = <0>;

        interrupt-parent = <&gpio>;
        interrupts = <26 IRQ_TYPE_EDGE_FALLING>;

        refin-supply = <&ad74413r_refin>;

        channel@0 {
          reg = <0>;

          adi,ch-func = <CH_FUNC_VOLTAGE_OUTPUT>;
        };

        channel@1 {
          reg = <1>;

          adi,ch-func = <CH_FUNC_CURRENT_OUTPUT>;
        };

        channel@2 {
          reg = <2>;

          adi,ch-func = <CH_FUNC_DIGITAL_INPUT_LOGIC>;
          adi,gpo-comparator;
        };

        channel@3 {
          reg = <3>;

          adi,ch-func = <CH_FUNC_CURRENT_INPUT_EXT_POWER>;
        };
      };
    };
...
