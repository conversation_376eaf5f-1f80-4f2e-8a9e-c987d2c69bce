# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/accel/kionix,kxsd9.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Kionix KXSD9 Accelerometer

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  3 axis 12 bit accelerometer with +-8G range on all axes. Also has a
  12 bit auxiliary ADC channel. Interface is either SPI or I2C.

properties:
  compatible:
    const: kionix,kxsd9

  reg:
    maxItems: 1

  vdd-supply: true
  iovdd-supply: true

  interrupts:
    maxItems: 1

  mount-matrix:
    description: an optional 3x3 mounting rotation matrix.

required:
  - compatible
  - reg

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

unevaluatedProperties: false

examples:
  - |
    # include <dt-bindings/interrupt-controller/irq.h>
    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        accel@18 {
            compatible = "kionix,kxsd9";
            reg = <0x18>;
            iovdd-supply = <&iovdd>;
            vdd-supply = <&vdd>;
            interrupts = <57 IRQ_TYPE_EDGE_FALLING>;
            mount-matrix = "-0.984807753012208", "0",  "-0.173648177666930",
                           "0",                  "-1", "0",
                           "-0.173648177666930", "0",  "0.984807753012208";
        };
    };
  - |
    # include <dt-bindings/interrupt-controller/irq.h>
    spi {
        #address-cells = <1>;
        #size-cells = <0>;
        accel@0 {
            compatible = "kionix,kxsd9";
            reg = <0>;
            spi-max-frequency = <10000000>;
        };
    };
...
