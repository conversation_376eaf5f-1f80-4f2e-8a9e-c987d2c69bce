# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/accel/adi,adis16201.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: ADIS16201 Dual Axis Inclinometer and similar

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Two similar parts from external interface point of view.
  SPI interface.
    https://www.analog.com/en/products/adis16201.html
    https://www.analog.com/en/products/adis16209.html

properties:
  compatible:
    enum:
      - adi,adis16201
      - adi,adis16209

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  vdd-supply: true

required:
  - compatible
  - reg

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    spi0 {
        #address-cells = <1>;
        #size-cells = <0>;

        accelerometer@0 {
            compatible = "adi,adis16201";
            reg = <0>;
            spi-max-frequency = <2500000>;
            interrupt-parent = <&gpio0>;
            interrupts = <0 IRQ_TYPE_LEVEL_HIGH>;
        };
    };
...
