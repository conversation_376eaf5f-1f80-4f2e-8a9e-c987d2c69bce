# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/accel/adi,adxl367.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Analog Devices ADXL367 3-Axis Digital Accelerometer

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description: |
  The ADXL367 is an ultralow power, 3-axis MEMS accelerometer.

  The ADXL367 does not alias input signals by to achieve ultralow power
  consumption, it samples the full bandwidth of the sensor at all
  data rates. Measurement ranges of +-2g, +-4g, and +-8g are available,
  with a resolution of 0.25mg/LSB on the +-2 g range.

  In addition to its ultralow power consumption, the ADXL367
  has many features to enable true system level power reduction.
  It includes a deep multimode output FIFO, a built-in micropower
  temperature sensor, and an internal ADC for synchronous conversion
  of an additional analog input.
    https://www.analog.com/en/products/adxl367.html

properties:
  compatible:
    enum:
      - adi,adxl367

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  vdd-supply: true
  vddio-supply: true

required:
  - compatible
  - reg
  - interrupts

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>

    i2c {
      #address-cells = <1>;
      #size-cells = <0>;

      accelerometer@53 {
        compatible = "adi,adxl367";
        reg = <0x53>;
        interrupt-parent = <&gpio>;
        interrupts = <25 IRQ_TYPE_EDGE_RISING>;
      };
    };
  - |
    #include <dt-bindings/interrupt-controller/irq.h>

    spi {
      #address-cells = <1>;
      #size-cells = <0>;

      accelerometer@0 {
        compatible = "adi,adxl367";
        reg = <0>;
        spi-max-frequency = <1000000>;
        interrupt-parent = <&gpio>;
        interrupts = <25 IRQ_TYPE_EDGE_RISING>;
      };
    };
