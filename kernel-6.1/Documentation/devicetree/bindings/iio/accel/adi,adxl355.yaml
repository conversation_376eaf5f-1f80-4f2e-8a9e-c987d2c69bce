# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/accel/adi,adxl355.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Analog Devices ADXL355 3-Axis, Low noise MEMS Accelerometer

maintainers:
  - <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>

description: |
  Analog Devices ADXL355 3-Axis, Low noise MEMS Accelerometer that supports
  both I2C & SPI interfaces
    https://www.analog.com/en/products/adxl355.html

properties:
  compatible:
    enum:
      - adi,adxl355

  reg:
    maxItems: 1

  interrupts:
    minItems: 1
    maxItems: 3
    description: |
      Type for DRDY should be IRQ_TYPE_EDGE_RISING.
      Three configurable interrupt lines exist.

  interrupt-names:
    description: Specify which interrupt line is in use.
    items:
      enum:
        - INT1
        - INT2
        - DRDY
    minItems: 1
    maxItems: 3

  vdd-supply:
    description: Regulator that provides power to the sensor

  vddio-supply:
    description: Regulator that provides power to the bus

required:
  - compatible
  - reg

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

unevaluatedProperties: false

examples:
  - |
        #include <dt-bindings/gpio/gpio.h>
        #include <dt-bindings/interrupt-controller/irq.h>
        i2c {
                #address-cells = <1>;
                #size-cells = <0>;

                /* Example for a I2C device node */
                accelerometer@1d {
                        compatible = "adi,adxl355";
                        reg = <0x1d>;
                        interrupt-parent = <&gpio>;
                        interrupts = <25 IRQ_TYPE_EDGE_RISING>;
                        interrupt-names = "DRDY";
                };
        };
  - |
        #include <dt-bindings/gpio/gpio.h>
        #include <dt-bindings/interrupt-controller/irq.h>
        spi {
                #address-cells = <1>;
                #size-cells = <0>;

                accelerometer@0 {
                        compatible = "adi,adxl355";
                        reg = <0>;
                        spi-max-frequency = <1000000>;
                        interrupt-parent = <&gpio>;
                        interrupts = <25 IRQ_TYPE_EDGE_RISING>;
                        interrupt-names = "DRDY";
                };
        };
