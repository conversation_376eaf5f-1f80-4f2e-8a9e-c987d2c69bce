# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/accel/adi,adxl372.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Analog Devices ADXL372 3-Axis, +/-(200g) Digital Accelerometer

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Analog Devices ADXL372 3-Axis, +/-(200g) Digital Accelerometer that supports
  both I2C & SPI interfaces
    https://www.analog.com/en/products/adxl372.html

properties:
  compatible:
    enum:
      - adi,adxl372

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

required:
  - compatible
  - reg
  - interrupts

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

unevaluatedProperties: false

examples:
  - |
        #include <dt-bindings/gpio/gpio.h>
        #include <dt-bindings/interrupt-controller/irq.h>
        i2c0 {
                #address-cells = <1>;
                #size-cells = <0>;

                /* Example for a I2C device node */
                accelerometer@53 {
                        compatible = "adi,adxl372";
                        reg = <0x53>;
                        interrupt-parent = <&gpio>;
                        interrupts = <25 IRQ_TYPE_EDGE_FALLING>;
                };
        };
  - |
        #include <dt-bindings/gpio/gpio.h>
        #include <dt-bindings/interrupt-controller/irq.h>
        spi0 {
                #address-cells = <1>;
                #size-cells = <0>;

                accelerometer@0 {
                        compatible = "adi,adxl372";
                        reg = <0>;
                        spi-max-frequency = <1000000>;
                        interrupt-parent = <&gpio>;
                        interrupts = <25 IRQ_TYPE_EDGE_FALLING>;
                };
        };
