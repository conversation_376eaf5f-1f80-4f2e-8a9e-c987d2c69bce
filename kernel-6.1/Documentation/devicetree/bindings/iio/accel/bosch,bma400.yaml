# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/accel/bosch,bma400.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Bosch BMA400 triaxial acceleration sensor

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Acceleration and temperature iio sensors with an i2c interface

  Specifications about the sensor can be found at:
    https://ae-bst.resource.bosch.com/media/_tech/media/datasheets/BST-BMA400-DS000.pdf

properties:
  compatible:
    enum:
      - bosch,bma400

  reg:
    maxItems: 1

  vdd-supply:
    description: phandle to the regulator that provides power to the accelerometer

  vddio-supply:
    description: phandle to the regulator that provides power to the sensor's IO

  interrupts:
    maxItems: 1

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>
    #include <dt-bindings/interrupt-controller/irq.h>
    i2c {
      #address-cells = <1>;
      #size-cells = <0>;
      accelerometer@14 {
        compatible = "bosch,bma400";
        reg = <0x14>;
        vdd-supply = <&vdd>;
        vddio-supply = <&vddio>;
        interrupt-parent = <&gpio0>;
        interrupts = <0 IRQ_TYPE_LEVEL_HIGH>;
      };
    };
