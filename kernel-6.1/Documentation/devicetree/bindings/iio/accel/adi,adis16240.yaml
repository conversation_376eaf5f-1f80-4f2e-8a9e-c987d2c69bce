# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/accel/adi,adis16240.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: ADIS16240 Programmable Impact Sensor and Recorder driver

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description: |
  ADIS16240 Programmable Impact Sensor and Recorder driver that supports
  SPI interface.
    https://www.analog.com/en/products/adis16240.html

properties:
  compatible:
    enum:
      - adi,adis16240

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

required:
  - compatible
  - reg
  - interrupts

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>
    #include <dt-bindings/interrupt-controller/irq.h>
    spi0 {
        #address-cells = <1>;
        #size-cells = <0>;

        /* Example for a SPI device node */
        accelerometer@0 {
            compatible = "adi,adis16240";
            reg = <0>;
            spi-max-frequency = <2500000>;
            interrupt-parent = <&gpio0>;
            interrupts = <0 IRQ_TYPE_LEVEL_HIGH>;
        };
    };
