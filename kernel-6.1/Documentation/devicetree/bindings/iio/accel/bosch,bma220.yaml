# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/accel/bosch,bma220.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Bosch BMA220 Trixial Acceleration Sensor

maintainers:
  - <PERSON> <Jonathan.<PERSON>@huawei.com>

properties:
  compatible:
    enum:
      - bosch,bma220

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  vdda-supply: true
  vddd-supply: true
  vddio-supply: true

required:
  - compatible
  - reg

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    spi0 {
        #address-cells = <1>;
        #size-cells = <0>;

        accelerometer@0 {
            compatible = "bosch,bma220";
            reg = <0>;
            spi-max-frequency = <2500000>;
            interrupt-parent = <&gpio0>;
            interrupts = <0 IRQ_TYPE_LEVEL_HIGH>;
        };
    };
...
