# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/accel/adi,adxl313.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Analog Devices ADXL312, ADXL313, and ADXL314 3-Axis Digital Accelerometers

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Analog Devices ADXL312, ADXL313, and ADXL314 3-Axis Digital Accelerometer that
  support both I2C & SPI interfaces.
    https://www.analog.com/en/products/adxl312.html
    https://www.analog.com/en/products/adxl313.html
    https://www.analog.com/en/products/adxl314.html

properties:
  compatible:
    enum:
      - adi,adxl312
      - adi,adxl313
      - adi,adxl314

  reg:
    maxItems: 1

  spi-3wire: true

  vs-supply:
    description: Regulator that supplies power to the accelerometer

  vdd-supply:
    description: Regulator that supplies the digital interface supply voltage

  interrupts:
    minItems: 1
    maxItems: 2

  interrupt-names:
    minItems: 1
    maxItems: 2
    items:
      enum:
        - INT1
        - INT2

required:
  - compatible
  - reg

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>
    #include <dt-bindings/interrupt-controller/irq.h>
    i2c0 {
        #address-cells = <1>;
        #size-cells = <0>;

        /* Example for a I2C device node */
        accelerometer@53 {
            compatible = "adi,adxl313";
            reg = <0x53>;
            interrupt-parent = <&gpio0>;
            interrupts = <0 IRQ_TYPE_LEVEL_HIGH>;
            interrupt-names = "INT1";
        };
    };
  - |
    #include <dt-bindings/gpio/gpio.h>
    #include <dt-bindings/interrupt-controller/irq.h>
    spi {
        #address-cells = <1>;
        #size-cells = <0>;

        /* Example for a SPI device node */
        accelerometer@0 {
            compatible = "adi,adxl313";
            reg = <0>;
            spi-max-frequency = <5000000>;
            interrupt-parent = <&gpio0>;
            interrupts = <0 IRQ_TYPE_LEVEL_HIGH>;
            interrupt-names = "INT1";
        };
    };
