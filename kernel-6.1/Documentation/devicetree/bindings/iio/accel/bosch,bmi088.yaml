# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/accel/bosch,bmi088.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Bosch BMI088 IMU accelerometer part

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Acceleration part of the IMU sensor with an SPI interface
  Specifications about the sensor can be found at:
    https://www.bosch-sensortec.com/media/boschsensortec/downloads/datasheets/bst-bmi088-ds001.pdf

properties:
  compatible:
    enum:
      - bosch,bmi085-accel
      - bosch,bmi088-accel
      - bosch,bmi090l-accel

  reg:
    maxItems: 1

  vdd-supply: true

  vddio-supply: true

  interrupts:
    minItems: 1
    maxItems: 2
    description: |
      Type should be either IRQ_TYPE_LEVEL_HIGH or IRQ_TYPE_LEVEL_LOW.
      Two configurable interrupt lines exist.

  interrupt-names:
    description: Specify which interrupt line is in use.
    items:
      enum:
        - INT1
        - INT2
    minItems: 1
    maxItems: 2

required:
  - compatible
  - reg

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    spi {
      #address-cells = <1>;
      #size-cells = <0>;
      bmi088-accel@1 {
        compatible = "bosch,bmi088-accel";
        reg = <1>;
        spi-max-frequency = <10000000>;
        interrupt-parent = <&gpio6>;
        interrupts = <19 IRQ_TYPE_LEVEL_LOW>;
        interrupt-names = "INT2";
      };
    };
...
