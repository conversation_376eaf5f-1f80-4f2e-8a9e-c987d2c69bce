# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/gyroscope/invensense,mpu3050.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Invensense MPU-3050 Gyroscope

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    const: invensense,mpu3050

  reg:
    maxItems: 1

  vdd-supply: true

  vlogic-supply: true

  interrupts:
    minItems: 1
    description:
      Interrupt mapping for the trigger interrupt from the internal oscillator.

  mount-matrix: true

  i2c-gate:
    $ref: /schemas/i2c/i2c-controller.yaml
    unevaluatedProperties: false
    description: |
      The MPU-3050 will pass through and forward the I2C signals from the
      incoming I2C bus, alternatively drive traffic to a slave device (usually
      an accelerometer) on its own initiative. Therefore is supports an
      i2c-gate subnode.

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    i2c {
        #address-cells = <1>;
        #size-cells = <0>;
        gyroscope@68 {
            compatible = "invensense,mpu3050";
            reg = <0x68>;
            interrupt-parent = <&foo>;
            interrupts = <12 IRQ_TYPE_EDGE_FALLING>;
            vdd-supply = <&bar>;
            vlogic-supply = <&baz>;

            i2c-gate {
                #address-cells = <1>;
                #size-cells = <0>;

                magnetometer@c {
                    compatible = "asahi-kasei,ak8975";
                    reg = <0x0c>;
                };
            };
        };
    };
...
