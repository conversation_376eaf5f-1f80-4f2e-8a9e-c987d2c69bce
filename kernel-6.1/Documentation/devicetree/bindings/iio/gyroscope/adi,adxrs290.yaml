# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
# Copyright 2020 Analog Devices Inc.
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/gyroscope/adi,adxrs290.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Analog Devices ADXRS290 Dual-Axis MEMS Gyroscope

maintainers:
  - <PERSON>shant Malpani <<EMAIL>>

description: |
  Bindings for the Analog Devices ADXRS290 dual-axis MEMS gyroscope device.
  https://www.analog.com/media/en/technical-documentation/data-sheets/ADXRS290.pdf

properties:
  compatible:
    const: adi,adxrs290

  reg:
    maxItems: 1

  spi-max-frequency:
    maximum: 5000000

  spi-cpol: true

  spi-cpha: true

  interrupts:
    maxItems: 1

required:
  - compatible
  - reg
  - spi-max-frequency
  - spi-cpol
  - spi-cpha

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    spi {
        #address-cells = <1>;
        #size-cells = <0>;
        gyro@0 {
                   compatible = "adi,adxrs290";
                   reg = <0>;
                   spi-max-frequency = <5000000>;
                   spi-cpol;
                   spi-cpha;
                   interrupt-parent = <&gpio>;
                   interrupts = <25 IRQ_TYPE_EDGE_RISING>;
        };
    };
...
