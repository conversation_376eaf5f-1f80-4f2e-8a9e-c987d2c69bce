# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/gyroscope/nxp,fxas21002c.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: NXP FXAS21002C Gyroscope

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description: |
  3 axis digital gyroscope device with an I2C and SPI interface.
  http://www.nxp.com/products/sensors/gyroscopes/3-axis-digital-gyroscope:FXAS21002C

properties:
  compatible:
    const: nxp,fxas21002c

  reg:
    maxItems: 1

  vdd-supply:
    description: Regulator that provides power to the sensor

  vddio-supply:
    description: Regulator that provides power to the bus

  reset-gpios:
    maxItems: 1
    description: GPIO connected to reset

  interrupts:
    minItems: 1
    maxItems: 2
    description: Either interrupt may be triggered on rising or falling edges.

  interrupt-names:
    minItems: 1
    maxItems: 2
    items:
      enum:
        - INT1
        - INT2

  drive-open-drain:
    type: boolean
    description: the interrupt/data ready line will be configured as open drain,
                 which is useful if several sensors share the same interrupt
                 line.

  spi-max-frequency:
    maximum: 2000000

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>

    i2c0 {
        #address-cells = <1>;
        #size-cells = <0>;

        gyroscope@20 {
          compatible = "nxp,fxas21002c";
          reg = <0x20>;

          vdd-supply = <&reg_peri_3p15v>;
          vddio-supply = <&reg_peri_3p15v>;

          interrupt-parent = <&gpio1>;
          interrupts = <7 IRQ_TYPE_EDGE_RISING>;
          interrupt-names = "INT1";
        };
    };
    spi0 {
        #address-cells = <1>;
        #size-cells = <0>;

        gyroscope@0 {
          compatible = "nxp,fxas21002c";
          reg = <0x0>;

          spi-max-frequency = <2000000>;

          interrupt-parent = <&gpio2>;
          interrupts = <7 IRQ_TYPE_EDGE_RISING>;
          interrupt-names = "INT2";
        };
    };
