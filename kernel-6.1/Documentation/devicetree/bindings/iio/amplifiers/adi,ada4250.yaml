# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/amplifiers/adi,ada4250.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: ADA4250 Programmable Gain Instrumentation Amplifier

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description: |
  Precision Low Power, 110kHz, 26uA, Programmable Gain Instrumentation Amplifier.

properties:
  compatible:
    enum:
      - adi,ada4250

  reg:
    maxItems: 1

  avdd-supply: true

  adi,refbuf-enable:
    description:
      Enable internal buffer to drive the reference pin.
    type: boolean

required:
  - compatible
  - reg
  - avdd-supply

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

unevaluatedProperties: false

examples:
  - |
    spi {
      #address-cells = <1>;
      #size-cells = <0>;
      amplifier@0 {
        compatible = "adi,ada4250";
        reg = <0>;
        avdd-supply = <&avdd>;
      };
    };
...
