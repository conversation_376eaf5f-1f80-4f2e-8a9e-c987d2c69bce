# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/health/ti,afe4403.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Texas Instruments AFE4403 Heart rate and Pulse Oximeter

maintainers:
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    const: ti,afe4403

  reg:
    maxItems: 1

  tx-supply:
    description: Supply to transmitting LEDs.

  interrupts:
    maxItems: 1
    description: Connected to ADC_RDY pin.

  reset-gpios: true

required:
  - compatible
  - reg

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>
    #include <dt-bindings/interrupt-controller/irq.h>
    spi {
        #address-cells = <1>;
        #size-cells = <0>;

        heart_mon@0 {
            compatible = "ti,afe4403";
            reg = <0>;
            spi-max-frequency = <10000000>;
            tx-supply = <&vbat>;
            interrupt-parent = <&gpio1>;
            interrupts = <28 IRQ_TYPE_EDGE_RISING>;
            reset-gpios = <&gpio1 16 GPIO_ACTIVE_LOW>;
        };
    };
...
