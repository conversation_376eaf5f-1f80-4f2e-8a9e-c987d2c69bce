# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/health/maxim,max30102.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Maxim MAX30102 heart rate and pulse oximeter and MAX30105 particle-sensor

maintainers:
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - maxim,max30102
      - maxim,max30105

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1
    description: Connected to ADC_RDY pin.

  maxim,red-led-current-microamp:
    description: RED LED current. Each step is approximately 200 microamps.
    minimum: 0
    maximum: 50800

  maxim,ir-led-current-microamp:
    description: IR LED current. Each step is approximately 200 microamps.
    minimum: 0
    maximum: 50800

  maxim,green-led-current-microamp:
    description: Green LED current. Each step is approximately 200 microamps.
    minimum: 0
    maximum: 50800

allOf:
  - if:
      properties:
        compatible:
          contains:
            const: maxim,max30102
    then:
      properties:
        maxim,green-led-current-microamp: false

additionalProperties: false

required:
  - compatible
  - reg
  - interrupts

examples:
  - |
    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        heart-rate@57 {
            compatible = "maxim,max30102";
            reg = <0x57>;
            maxim,red-led-current-microamp = <7000>;
            maxim,ir-led-current-microamp = <7000>;
            interrupt-parent = <&gpio1>;
            interrupts = <16 2>;
        };
    };
...
