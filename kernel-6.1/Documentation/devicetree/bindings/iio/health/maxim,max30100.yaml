# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/health/maxim,max30100.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Maxim MAX30100 heart rate and pulse oximeter sensor

maintainers:
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    const: maxim,max30100

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1
    description: Connected to ADC_RDY pin.

  maxim,led-current-microamp:
    minItems: 2
    maxItems: 2
    description: |
      LED current whilst the engine is running. First indexed value is
      the configuration for the RED LED, and second value is for the IR LED.

additionalProperties: false

required:
  - compatible
  - reg
  - interrupts

examples:
  - |
    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        heart-rate@57 {
            compatible = "maxim,max30100";
            reg = <0x57>;
            maxim,led-current-microamp = <24000 50000>;
            interrupt-parent = <&gpio1>;
            interrupts = <16 2>;
        };
    };
...
