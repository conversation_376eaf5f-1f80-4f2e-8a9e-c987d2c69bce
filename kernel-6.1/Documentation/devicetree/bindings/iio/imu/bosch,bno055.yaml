# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/imu/bosch,bno055.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Bosch BNO055

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Inertial Measurement Unit with Accelerometer, Gyroscope, Magnetometer and
  internal MCU for sensor fusion
  https://www.bosch-sensortec.com/products/smart-sensors/bno055/

properties:
  compatible:
    enum:
      - bosch,bno055

  reg:
    maxItems: 1

  reset-gpios:
    maxItems: 1

  clocks:
    maxItems: 1

required:
  - compatible

additionalProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>
    serial {
      imu {
        compatible = "bosch,bno055";
        reset-gpios = <&gpio0 54 GPIO_ACTIVE_LOW>;
        clocks = <&imu_clk>;
      };
    };

  - |
    #include <dt-bindings/gpio/gpio.h>
    i2c {
      #address-cells = <1>;
      #size-cells = <0>;

      imu@28 {
        compatible = "bosch,bno055";
        reg = <0x28>;
        reset-gpios = <&gpio0 54 GPIO_ACTIVE_LOW>;
        clocks = <&imu_clk>;
      };
    };
