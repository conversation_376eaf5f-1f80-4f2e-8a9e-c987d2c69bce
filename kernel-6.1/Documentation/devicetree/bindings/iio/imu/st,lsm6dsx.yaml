# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/imu/st,lsm6dsx.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: STM 6-axis (acc + gyro) IMU Mems sensors

maintainers:
  - <PERSON> <<EMAIL>>

description:
  Devices have both I2C and SPI interfaces.

properties:
  compatible:
    oneOf:
      - enum:
          - st,lsm6ds3
          - st,lsm6ds3h
          - st,lsm6dsl
          - st,lsm6dsm
          - st,ism330dlc
          - st,lsm6dso
          - st,asm330lhh
          - st,lsm6dsox
          - st,lsm6dsr
          - st,lsm6ds3tr-c
          - st,ism330dhcx
          - st,lsm9ds1-imu
          - st,lsm6ds0
          - st,lsm6dsrx
          - st,lsm6dst
          - st,lsm6dsop
      - items:
          - const: st,asm330lhhx
          - const: st,lsm6dsr
      - items:
          - const: st,lsm6dstx
          - const: st,lsm6dst

  reg:
    maxItems: 1

  interrupts:
    minItems: 1
    maxItems: 2
    description:
      Supports up to 2 interrupt lines via the INT1 and INT2 pins.

  vdd-supply:
    description: if defined provides VDD power to the sensor.

  vddio-supply:
    description: if defined provides VDD IO power to the sensor.

  st,drdy-int-pin:
    $ref: '/schemas/types.yaml#/definitions/uint32'
    description: |
      The pin on the package that will be used to signal data ready
    enum:
      - 1
      - 2

  st,pullups:
    type: boolean
    description: enable/disable internal i2c controller pullup resistors.

  st,disable-sensor-hub:
    type: boolean
    description:
      Enable/disable internal i2c controller slave autoprobing at bootstrap.
      Disable sensor-hub is useful if i2c controller clock/data lines are
      connected through a pull-up with other chip lines (e.g. SDO/SA0).

  drive-open-drain:
    type: boolean
    description:
      The interrupt/data ready line will be configured as open drain, which
      is useful if several sensors share the same interrupt line.

  wakeup-source:
    $ref: /schemas/types.yaml#/definitions/flag

required:
  - compatible
  - reg

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        imu@6b {
            compatible = "st,lsm6dsm";
            reg = <0x6b>;
            interrupt-parent = <&gpio0>;
            interrupts = <0 IRQ_TYPE_EDGE_RISING>;
        };
    };
...
