# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/imu/nxp,fxos8700.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Freescale FXOS8700 Inertial Measurement Unit

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Accelerometer and magnetometer combo device with an i2c and SPI interface.
  https://www.nxp.com/products/sensors/motion-sensors/6-axis/digital-motion-sensor-3d-accelerometer-2g-4g-8g-plus-3d-magnetometer:FXOS8700CQ

properties:
  compatible:
    enum:
      - nxp,fxos8700

  reg:
    maxItems: 1

  interrupts:
    minItems: 1
    maxItems: 2

  interrupt-names:
    minItems: 1
    maxItems: 2
    items:
      enum:
        - INT1
        - INT2

  drive-open-drain:
    type: boolean

required:
  - compatible
  - reg

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>
    #include <dt-bindings/interrupt-controller/irq.h>
    i2c0 {
        #address-cells = <1>;
        #size-cells = <0>;

        fxos8700@1e {
          compatible = "nxp,fxos8700";
          reg = <0x1e>;

          interrupt-parent = <&gpio2>;
          interrupts = <7 IRQ_TYPE_EDGE_RISING>;
          interrupt-names = "INT1";
        };
    };
  - |
    #include <dt-bindings/gpio/gpio.h>
    #include <dt-bindings/interrupt-controller/irq.h>
    spi0 {
        #address-cells = <1>;
        #size-cells = <0>;

        fxos8700@0 {
          compatible = "nxp,fxos8700";
          reg = <0>;

          spi-max-frequency = <1000000>;
          interrupt-parent = <&gpio1>;
          interrupts = <7 IRQ_TYPE_EDGE_RISING>;
          interrupt-names = "INT2";
        };
    };
