# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/dac/ti,dac5571.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Texas Instruments DAC5571 Family

maintainers:
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - ti,dac5571
      - ti,dac6571
      - ti,dac7571
      - ti,dac5574
      - ti,dac6574
      - ti,dac7574
      - ti,dac5573
      - ti,dac6573
      - ti,dac7573
      - ti,dac121c081

  reg:
    maxItems: 1

  vref-supply:
    description:
      Reference voltage must be supplied to establish the scaling of the
      output voltage.

required:
  - compatible
  - reg
  - vref-supply

additionalProperties: false

examples:
  - |
    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        dac@4c {
            compatible = "ti,dac5571";
            reg = <0x4C>;
            vref-supply = <&vdd_supply>;
        };
    };
...
