# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/dac/adi,ad7293.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: AD7293 12-Bit Power Amplifier Current Controller with ADC,
       DACs, Temperature and Current Sensors

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description: |
   Power Amplifier drain current controller containing functionality
   for general-purpose monitoring and control of current, voltage,
   and temperature, integrated into a single chip solution with an
   SPI-compatible interface.

   https://www.analog.com/en/products/ad7293.html

properties:
  compatible:
    enum:
      - adi,ad7293

  avdd-supply: true

  vdrive-supply: true

  reset-gpios:
    maxItems: 1

  reg:
    maxItems: 1

  spi-max-frequency:
    maximum: 1000000

required:
  - compatible
  - reg
  - avdd-supply
  - vdrive-supply

additionalProperties: false

examples:
  - |
    spi {
      #address-cells = <1>;
      #size-cells = <0>;
      ad7293@0 {
        compatible = "adi,ad7293";
        reg = <0>;
        spi-max-frequency = <1000000>;
        avdd-supply = <&avdd>;
        vdrive-supply = <&vdrive>;
        reset-gpios = <&gpio 10 0>;
      };
    };
...
