# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/dac/adi,ad5755.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Analog Devices AD5755 Multi-Channel DAC

maintainers:
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - adi,ad5755
      - adi,ad5755-1
      - adi,ad5757
      - adi,ad5735
      - adi,ad5737

  reg:
    maxItems: 1

  spi-cpha:
    description: Either this or spi-cpol but not both.
  spi-cpol: true

  adi,ext-dc-dc-compenstation-resistor:
    $ref: /schemas/types.yaml#/definitions/flag
    description:
      Set if the hardware have an external resistor and thereby bypasses
      the internal compensation resistor.

  adi,dc-dc-phase:
    $ref: /schemas/types.yaml#/definitions/uint32
    enum: [0, 1, 2, 3]
    description: |
      Valid values for DC DC Phase control is:
      0: All dc-to-dc converters clock on the same edge.
      1: Channel A and Channel B clock on the same edge,
         Channel C and Channel D clock on opposite edges.
      2: Channel A and Channel C clock on the same edge,
         Channel B and Channel D clock on opposite edges.
      3: Channel A, Channel B, Channel C, and Channel D
         clock 90 degrees out of phase from each other.

  adi,dc-dc-freq-hz:
    enum: [250000, 410000, 650000]

  adi,dc-dc-max-microvolt:
    description:
      Maximum allowed Vboost voltage supplied by the dc-to-dc converter.
    enum: [23000000, 24500000, 27000000, 29500000]

  "#address-cells":
    const: 1

  "#size-cells":
    const: 0

  "#io-channel-cells":
    const: 1

required:
  - compatible
  - reg

patternProperties:
  "^channel@[0-7]$":
    type: object
    description: Child node to describe a channel
    properties:
      reg:
        maxItems: 1

      adi,mode:
        $ref: /schemas/types.yaml#/definitions/uint32
        minimum: 0
        maximum: 6
        description: |
          Valid values for DAC modes is:
          0: 0 V to 5 V voltage range.
          1: 0 V to 10 V voltage range.
          2: Plus minus 5 V voltage range.
          3: Plus minus 10 V voltage range.
          4: 4 mA to 20 mA current range.
          5: 0 mA to 20 mA current range.
          6: 0 mA to 24 mA current range.

      adi,ext-current-sense-resistor:
        $ref: /schemas/types.yaml#/definitions/flag
        description:
          Set if the hardware has an external current sense resistor

      adi,enable-voltage-overrange:
        $ref: /schemas/types.yaml#/definitions/flag
        description: Enable voltage overrange

      adi,slew:
        $ref: /schemas/types.yaml#/definitions/uint32-array
        description: |
          Array of slewrate settings should contain 3 fields:
          1: Should be either 0 or 1 in order to enable or disable slewrate.
          2: Slew rate update frequency
          3: Slew step size
        items:
          - enum: [0, 1]
          - enum: [64000, 32000, 16000, 8000, 4000, 2000, 1000, 500, 250, 125, 64, 32, 16, 8, 4, 0]
          - enum: [1, 2, 4, 16, 32, 64, 128, 256]

    required:
      - reg

    additionalProperties: false

oneOf:
  - required:
      - spi-cpha
  - required:
      - spi-cpol

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

unevaluatedProperties: false

examples:
  - |
    spi {
        #address-cells = <1>;
        #size-cells = <0>;

        dac@0 {
            #address-cells = <1>;
            #size-cells = <0>;
            compatible = "adi,ad5755";
            reg = <0>;
            spi-max-frequency = <1000000>;
            spi-cpha;
            adi,dc-dc-phase = <0>;
            adi,dc-dc-freq-hz = <410000>;
            adi,dc-dc-max-microvolt = <23000000>;
            channel@0 {
                reg = <0>;
                adi,mode = <4>;
                adi,ext-current-sense-resistor;
                adi,slew = <0 64000 1>;
            };
            channel@1 {
                reg = <1>;
                adi,mode = <4>;
                adi,ext-current-sense-resistor;
                adi,slew = <0 64000 1>;
            };
            channel@2 {
                reg = <2>;
                adi,mode = <4>;
                adi,ext-current-sense-resistor;
                adi,slew = <0 64000 1>;
            };
            channel@3 {
                reg = <3>;
                adi,mode = <4>;
                adi,ext-current-sense-resistor;
                adi,slew = <0 64000 1>;
            };
        };
    };
...
