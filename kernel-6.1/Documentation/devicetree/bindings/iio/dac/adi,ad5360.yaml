# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/dac/adi,ad5360.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Analog Devices AD5360 and similar DACs

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - adi,ad5360
      - adi,ad5361
      - adi,ad5363
      - adi,ad5370
      - adi,ad5371
      - adi,ad5372
      - adi,ad5373

  reg:
    maxItems: 1

  vref0-supply: true
  vref1-supply: true
  vref2-supply: true

required:
  - compatible
  - reg
  - vref0-supply
  - vref1-supply

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#
  - if:
      properties:
        compatible:
          contains:
            enum:
              - adi,ad5360
              - adi,ad5361
              - adi,ad5363
              - adi,ad5370
              - adi,ad5372
              - adi,ad5373
    then:
      properties:
        vref2-supply: false
  - if:
      properties:
        compatible:
          contains:
            enum:
              - adi,ad5371
    then:
      required:
        - vref2-supply

unevaluatedProperties: false

examples:
  - |
    spi {
        #address-cells = <1>;
        #size-cells = <0>;
        dac@0 {
            reg = <0>;
            compatible = "adi,ad5371";
            vref0-supply = <&dac_vref0>;
            vref1-supply = <&dac_vref1>;
            vref2-supply = <&dac_vref2>;
        };
    };
...
