# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/dac/adi,ltc2688.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Analog Devices LTC2688 DAC

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description: |
  Analog Devices LTC2688 16 channel, 16 bit, +-15V DAC
  https://www.analog.com/media/en/technical-documentation/data-sheets/ltc2688.pdf

properties:
  compatible:
    enum:
      - adi,ltc2688

  reg:
    maxItems: 1

  vcc-supply:
    description: Analog Supply Voltage Input.

  iovcc-supply:
    description: Digital Input/Output Supply Voltage.

  vref-supply:
    description:
      Reference Input/Output. The voltage at the REF pin sets the full-scale
      range of all channels. If not provided the internal reference is used and
      also provided on the VREF pin".

  clr-gpios:
    description:
      If specified, it will be asserted during driver probe. As the line is
      active low, it should be marked GPIO_ACTIVE_LOW.
    maxItems: 1

  '#address-cells':
    const: 1

  '#size-cells':
    const: 0

patternProperties:
  "^channel@([0-9]|1[0-5])$":
    type: object
    additionalProperties: false

    properties:
      reg:
        description: The channel number representing the DAC output channel.
        maximum: 15

      adi,toggle-mode:
        description:
          Set the channel as a toggle enabled channel. Toggle operation enables
          fast switching of a DAC output between two different DAC codes without
          any SPI transaction.
        type: boolean

      adi,output-range-microvolt:
        description: Specify the channel output full scale range.
        oneOf:
          - items:
              - const: 0
              - enum: [5000000, 10000000]
          - items:
              - const: -5000000
              - const: 5000000
          - items:
              - const: -10000000
              - const: 10000000
          - items:
              - const: -15000000
              - const: 15000000

      adi,overrange:
        description: Enable 5% overrange over the selected full scale range.
        type: boolean

      clocks:
        maxItems: 1

      adi,toggle-dither-input:
        description:
          Selects the TGPx pin to be associated with this channel. This setting
          only makes sense for toggle or dither enabled channels. If
          @adi,toggle-mode is not set and this property is given, the channel is
          assumed to be a dither capable channel. Note that multiple channels
          can be mapped to the same pin. If this setting is given, the
          respective @clock must also be provided. Mappings between this and
          input pins
            0 - TGP1
            1 - TGP2
            2 - TGP3
        $ref: /schemas/types.yaml#/definitions/uint32
        enum: [0, 1, 2]

    dependencies:
      adi,toggle-dither-input: [ clocks ]

    required:
      - reg

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |

    spi {
          #address-cells = <1>;
          #size-cells = <0>;
          ltc2688: ltc2688@0 {
                  compatible = "adi,ltc2688";
                  reg = <0>;

                  vcc-supply = <&vcc>;
                  iovcc-supply = <&vcc>;
                  vref-supply = <&vref>;

                  #address-cells = <1>;
                  #size-cells = <0>;
                  channel@0 {
                          reg = <0>;
                          adi,toggle-mode;
                          adi,overrange;
                  };

                  channel@1 {
                          reg = <1>;
                          adi,output-range-microvolt = <0 10000000>;

                          clocks = <&clock_tgp3>;
                          adi,toggle-dither-input = <2>;
                  };
          };
    };

...
