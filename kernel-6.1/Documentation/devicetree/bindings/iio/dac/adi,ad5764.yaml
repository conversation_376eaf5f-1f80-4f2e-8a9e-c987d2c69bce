# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/dac/adi,ad5764.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Analog Devices AD5744 and AD5764 DAC families

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

properties:

  compatible:
    enum:
      - adi,ad5744
      - adi,ad5744r
      - adi,ad5764
      - adi,ad5764r

  reg:
    maxItems: 1

  vrefAB-supply: true
  vrefCD-supply: true

required:
  - compatible
  - reg

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#
  - if:
      properties:
        compatible:
          contains:
            enum:
              - adi,ad5744
              - adi,ad5764
    then:
      required:
        - vrefAB-supply
        - vrefCD-supply

unevaluatedProperties: false

examples:
  - |
    spi {
        #address-cells = <1>;
        #size-cells = <0>;

        dac@0 {
            compatible = "adi,ad5744";
            reg = <0>;
            vrefAB-supply = <&dac_vref>;
            vrefCD-supply = <&dac_vref>;
        };
    };
...
