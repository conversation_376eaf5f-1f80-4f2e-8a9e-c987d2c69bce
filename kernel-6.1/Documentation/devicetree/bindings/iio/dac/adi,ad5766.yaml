# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
# Copyright 2020 Analog Devices Inc.
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/dac/adi,ad5766.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Analog Devices AD5766 DAC device driver

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description: |
  Bindings for the Analog Devices AD5766 current DAC device. Datasheet can be
  found here:
    https://www.analog.com/media/en/technical-documentation/data-sheets/ad5766-5767.pdf

properties:
  compatible:
    enum:
      - adi,ad5766
      - adi,ad5767

  output-range-microvolts:
    $ref: /schemas/types.yaml#/definitions/int32-array
    maxItems: 2
    description: Select converter output range.

  reg:
    maxItems: 1

  spi-max-frequency:
    maximum: 1000000

  spi-cpol: true

  reset-gpios:
    description: GPIO spec for the RESET pin. As the line is active low, it
      should be marked GPIO_ACTIVE_LOW.
    maxItems: 1

required:
  - compatible
  - output-range-microvolts
  - reg
  - spi-max-frequency
  - spi-cpol

additionalProperties: false

examples:
  - |
    spi {
          #address-cells = <1>;
          #size-cells = <0>;

          ad5766@0 {
              compatible = "adi,ad5766";
              output-range-microvolts = <(-5000000) 5000000>;
              reg = <0>;
              spi-cpol;
              spi-max-frequency = <1000000>;
              reset-gpios = <&gpio 22 0>;
            };
      };
