# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/dac/adi,ad5592r.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Analog Devices AD5592R/AD5593R DAC/ADC

maintainers:
  - <PERSON> <micha<PERSON>.<EMAIL>>

properties:
  compatible:
    enum:
      - adi,ad5592r
      - adi,ad5593r

  reg:
    maxItems: 1

  spi-max-frequency:
    maximum: 30000000

  spi-cpol: true

  "#address-cells":
    const: 1

  "#size-cells":
    const: 0

  "#io-channel-cells":
    const: 1

  vref-supply:
    description: If not set internal 2.5V reference used.

  reset-gpios:
    maxItems: 1

  gpio-controller:
    description: Marks the device node as a GPIO controller.

  "#gpio-cells":
    const: 2
    description:
      The first cell is the GPIO number and the second cell specifies
      GPIO flags, as defined in <dt-bindings/gpio/gpio.h>.

required:
  - compatible
  - reg
  - "#address-cells"
  - "#size-cells"

allOf:
  - if:
      properties:
        compatible:
          contains:
            const: adi,ad5592r
    then:
      required:
        - spi-cpol
    else:
      properties:
        spi-cpol: false

additionalProperties: false

patternProperties:
  "^(channel@)[0-7]$":
    type: object
    description: Child node to describe a channel
    properties:
      reg:
        minimum: 0
        maximum: 7

      adi,mode:
        $ref: /schemas/types.yaml#/definitions/uint32
        enum: [0, 1, 2, 3, 8]
        description: |
          Mode or function of this channel.
          Macros specifying the valid values can be found in
          <dt-bindings/iio/adi,ad5592r.h>.

          The following values are currently supported:
          * CH_MODE_UNUSED (the pin is unused)
          * CH_MODE_ADC (the pin is ADC input)
          * CH_MODE_DAC (the pin is DAC output)
          * CH_MODE_DAC_AND_ADC (the pin is DAC output but can be monitored
            by an ADC, since there is no disadvantage this should be
            considered as the preferred DAC mode)
          * CH_MODE_GPIO (the pin is registered with GPIOLIB)

      adi,off-state:
        $ref: /schemas/types.yaml#/definitions/uint32
        enum: [0, 1, 2, 3]
        description: |
          State of this channel when unused or the device gets removed.
          Macros specifying the  valid values can be found in
          <dt-bindings/iio/adi,ad5592r.h>.
          * CH_OFFSTATE_PULLDOWN (the pin is pulled down)
          * CH_OFFSTATE_OUT_LOW  (the pin is output low)
          * CH_OFFSTATE_OUT_HIGH (the pin is output high)
          * CH_OFFSTATE_OUT_TRISTATE (the pin is tristated output)

    required:
      - reg
      - adi,mode

    additionalProperties: false

examples:
  - |
    #include <dt-bindings/iio/adi,ad5592r.h>
    spi {
        #address-cells = <1>;
        #size-cells = <0>;

        addac@0 {
            compatible = "adi,ad5592r";
            #size-cells = <0>;
            #address-cells = <1>;
            #gpio-cells = <2>;
            reg = <0>;

            spi-max-frequency = <1000000>;
            spi-cpol;

            vref-supply = <&vref>;
            reset-gpios = <&gpio0 86 0>;
            gpio-controller;

            channel@0 {
                reg = <0>;
                adi,mode = <CH_MODE_DAC>;
            };
            channel@1 {
                reg = <1>;
                adi,mode = <CH_MODE_ADC>;
            };
            channel@2 {
                reg = <2>;
                adi,mode = <CH_MODE_DAC_AND_ADC>;
            };
            channel@3 {
                reg = <3>;
                adi,mode = <CH_MODE_DAC_AND_ADC>;
                adi,off-state = <CH_OFFSTATE_PULLDOWN>;
            };
            channel@4 {
                reg = <4>;
                adi,mode = <CH_MODE_UNUSED>;
                adi,off-state = <CH_OFFSTATE_PULLDOWN>;
            };
            channel@5 {
                reg = <5>;
                adi,mode = <CH_MODE_GPIO>;
                adi,off-state = <CH_OFFSTATE_PULLDOWN>;
            };
            channel@6 {
                reg = <6>;
                adi,mode = <CH_MODE_GPIO>;
                adi,off-state = <CH_OFFSTATE_PULLDOWN>;
            };
            channel@7 {
                reg = <7>;
                adi,mode = <CH_MODE_GPIO>;
                adi,off-state = <CH_OFFSTATE_PULLDOWN>;
            };
        };
        ad5593r@10 {
            compatible = "adi,ad5593r";
            #size-cells = <0>;
            #address-cells = <1>;
            #gpio-cells = <2>;
            reg = <0x10>;
            gpio-controller;

            channel@0 {
                reg = <0>;
                adi,mode = <CH_MODE_DAC>;
                adi,off-state = <CH_OFFSTATE_PULLDOWN>;
            };
            channel@1 {
                reg = <1>;
                adi,mode = <CH_MODE_ADC>;
                adi,off-state = <CH_OFFSTATE_PULLDOWN>;
            };
            channel@2 {
                reg = <2>;
                adi,mode = <CH_MODE_DAC_AND_ADC>;
                adi,off-state = <CH_OFFSTATE_PULLDOWN>;
            };
            channel@6 {
                reg = <6>;
                adi,mode = <CH_MODE_GPIO>;
                adi,off-state = <CH_OFFSTATE_PULLDOWN>;
            };
        };
    };
...
