# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/dac/adi,ad5758.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Analog Devices AD5758 DAC

maintainers:
  - <PERSON> <Michael.<PERSON>@analog.com>

properties:
  compatible:
    const: adi,ad5758

  reg:
    maxItems: 1

  spi-cpha: true

  adi,dc-dc-mode:
    $ref: /schemas/types.yaml#/definitions/uint32
    enum: [1, 2, 3]
    description: |
      Mode of operation of the dc-to-dc converter
      Dynamic Power Control (DPC)
      In this mode, the AD5758 circuitry senses the output voltage and
      dynamically regulates the supply voltage, VDPC+, to meet compliance
      requirements plus an optimized headroom voltage for the output buffer.

      Programmable Power Control (PPC)
      In this mode, the VDPC+ voltage is user-programmable to a fixed level
      that needs to accommodate the maximum output load required.

      The output of the DAC core is either converted to a current or
      voltage output at the VIOUT pin. Only one mode can be enabled at
      any one time.

      The following values are currently supported:
      * 1: DPC current mode
      * 2: DPC voltage mode
      * 3: PPC current mode

      Depending on the selected output mode (voltage or current) one of the
      two properties must be present:

  adi,range-microvolt:
    description: |
      Voltage output range specified as <minimum, maximum>
    oneOf:
      - items:
          - const: 0
          - enum: [5000000, 10000000]
      - items:
          - const: -5000000
          - const: 5000000
      - items:
          - const: -10000000
          - const: 10000000

  adi,range-microamp:
    description: |
      Current output range specified as <minimum, maximum>
    oneOf:
      - items:
          - const: 0
          - enum: [20000, 24000]
      - items:
          - const: 4
          - const: 24000
      - items:
          - const: -20000
          - const: 20000
      - items:
          - const: -24000
          - const: 24000
      - items:
          - const: -1000
          - const: 22000

  reset-gpios: true

  adi,dc-dc-ilim-microamp:
    enum: [150000, 200000, 250000, 300000, 350000, 400000]
    description: |
      The dc-to-dc converter current limit.

  adi,slew-time-us:
    description: |
      The time it takes for the output to reach the full scale [uS]
    minimum: 133
    maximum: 1023984375

required:
  - compatible
  - reg
  - spi-cpha
  - adi,dc-dc-mode

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#
  - if:
      properties:
        adi,dc-dc-mode:
          contains:
            enum: [1, 3]
    then:
      properties:
        adi,range-microvolt: false
      required:
        - adi,range-microamp
    else:
      properties:
        adi,range-microamp: false
      required:
        - adi,range-microvolt

unevaluatedProperties: false

examples:
  - |
    spi {
        #address-cells = <1>;
        #size-cells = <0>;

        dac@0 {
            compatible = "adi,ad5758";
            reg = <0>;
            spi-max-frequency = <1000000>;
            spi-cpha;

            reset-gpios = <&gpio 22 0>;

            adi,dc-dc-mode = <2>;
            adi,range-microvolt = <0 10000000>;
            adi,dc-dc-ilim-microamp = <200000>;
            adi,slew-time-us = <125000>;
        };
    };
...
