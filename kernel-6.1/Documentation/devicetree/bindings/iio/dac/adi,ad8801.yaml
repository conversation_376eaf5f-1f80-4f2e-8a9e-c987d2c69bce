# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/dac/adi,ad8801.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Analog Devices AD8801 and AD8803 DACs

maintainers:
  - <PERSON> <<EMAIL>>

properties:

  compatible:
    enum:
      - adi,ad8801
      - adi,ad8803

  reg:
    maxItems: 1

  vrefh-supply: true
  vrefl-supply: true

required:
  - compatible
  - reg
  - vrefh-supply

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#
  - if:
      properties:
        compatible:
          contains:
            const: adi,ad8803
    then:
      required:
        - vrefl-supply
    else:
      properties:
        vrefl-supply: false

unevaluatedProperties: false

examples:
  - |
    spi {
        #address-cells = <1>;
        #size-cells = <0>;

        dac@0 {
            compatible = "adi,ad8803";
            reg = <0>;
            vrefl-supply = <&dac_vrefl>;
            vrefh-supply = <&dac_vrefh>;
        };
    };
...
