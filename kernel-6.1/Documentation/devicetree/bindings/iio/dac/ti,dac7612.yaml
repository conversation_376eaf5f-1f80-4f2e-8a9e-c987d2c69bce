# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/dac/ti,dac7612.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Texas Instruments DAC7612 family of DACs

description:
  The DAC7612 is a dual, 12-bit digital-to-analog converter (DAC) with
  guaranteed 12-bit monotonicity performance over the industrial temperature
  range. Is is programmable through an SPI interface.

maintainers:
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - ti,dac7612
      - ti,dac7612u
      - ti,dac7612ub

  reg:
    maxItems: 1

  ti,loaddacs-gpios:
    description:
      DACs are loaded when the pin connected to this GPIO is pulled low.
    maxItems: 1

required:
  - compatible
  - reg

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>
    spi {
        #address-cells = <1>;
        #size-cells = <0>;

        dac@1 {
            compatible = "ti,dac7612";
            reg = <0x1>;
            ti,loaddacs-gpios = <&msmgpio 25 GPIO_ACTIVE_LOW>;
        };
    };
...
