# SPDX-License-Identifier: GPL-2.0 OR BSD-2-Clause
# Copyright 2019 <PERSON> <<EMAIL>>
%YAML 1.2
---
$id: "http://devicetree.org/schemas/iio/dac/lltc,ltc1660.yaml#"
$schema: "http://devicetree.org/meta-schemas/core.yaml#"

title: Linear Technology Micropower octal 8-Bit and 10-Bit DACs

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Bindings for the Linear Technology Micropower octal 8-Bit and 10-Bit DAC.
  Datasheet can be found here: https://www.analog.com/media/en/technical-documentation/data-sheets/166560fa.pdf

properties:
  compatible:
    enum:
      - lltc,ltc1660
      - lltc,ltc1665

  reg:
    maxItems: 1

  spi-max-frequency:
    maximum: 5000000

  vref-supply:
    description: Phandle to the external reference voltage supply.

required:
  - compatible
  - reg
  - vref-supply

additionalProperties: false

examples:
  - |
    spi {
      #address-cells = <1>;
      #size-cells = <0>;

      dac@0 {
        compatible = "lltc,ltc1660";
        reg = <0>;
        spi-max-frequency = <5000000>;
        vref-supply = <&vref_reg>;
      };
    };
