# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/dac/adi,ad7303.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Analog Devices AD7303 DAC

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    const: adi,ad7303

  reg:
    maxItems: 1

  Vdd-supply:
    description:
      Used to calculate output channel scalling if REF-supply not specified.
  REF-supply:
    description:
      If not provided, Vdd/2 is used as the reference voltage.

  spi-max-frequency:
    maximum: 30000000

required:
  - compatible
  - reg
  - Vdd-supply

additionalProperties: false

examples:
  - |
    spi {
        #address-cells = <1>;
        #size-cells = <0>;

        dac@4 {
            compatible = "adi,ad7303";
            reg = <4>;
            spi-max-frequency = <10000000>;
            Vdd-supply = <&vdd_supply>;
            REF-supply = <&vref_supply>;
        };
    };
...
