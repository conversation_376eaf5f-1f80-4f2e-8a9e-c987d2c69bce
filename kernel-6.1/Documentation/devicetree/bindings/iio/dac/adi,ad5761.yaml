# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/dac/adi,ad5761.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Analog Devices AD5761 and similar DACs

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

properties:

  compatible:
    enum:
      - adi,ad5721
      - adi,ad5721r
      - adi,ad5761
      - adi,ad5761r

  reg:
    maxItems: 1

  vref-supply:
    description: If not supplied, internal reference will be used.

required:
  - compatible
  - reg

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#
  - if:
      properties:
        compatible:
          contains:
            enum:
              - adi,ad5721
              - adi,ad5761
    then:
      required:
        - vref-supply

unevaluatedProperties: false

examples:
  - |
    spi {
        #address-cells = <1>;
        #size-cells = <0>;

        dac@0 {
            compatible = "adi,ad5721";
            reg = <0>;
            vref-supply = <&dac_vref>;
        };
    };
...
