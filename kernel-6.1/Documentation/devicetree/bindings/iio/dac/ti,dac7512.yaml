# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/dac/ti,dac7512.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Texas Instruments DAC7512 DAC

maintainers:
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    const: ti,dac7512

  reg:
    maxItems: 1

  spi-max-frequency:
    description:
      Maximum frequency is reduced for supply voltage of less than 3.6V
    maximum: 30000000

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    spi {
        #address-cells = <1>;
        #size-cells = <0>;

        dac@0 {
            compatible = "ti,dac7512";
            reg = <0>; /* CS0 */
            spi-max-frequency = <1000000>;
        };
    };
...
