# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/dac/dpot-dac.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: DAC emulation using a digital potentiometer

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  It is assumed that the dpot is used as a voltage divider between the
  current dpot wiper setting and the maximum resistance of the dpot. The
  divided voltage is provided by a vref regulator.

                  .------.
   .-----------.  |      |
   | vref      |--'    .---.
   | regulator |--.    |   |
   '-----------'  |    | d |
                  |    | p |
                  |    | o |  wiper
                  |    | t |<---------+
                  |    |   |
                  |    '---'       dac output voltage
                  |      |
                  '------+------------+

properties:
  compatible:
    const: dpot-dac

  vref-supply:
    description: Regulator supplying the voltage divider.

  io-channels:
    maxItems: 1
    description: |
      Channel node of the dpot to be used for the voltage division.

  io-channel-names:
    const: dpot

  "#io-channel-cells":
    const: 1

required:
  - compatible
  - vref-supply
  - io-channels
  - io-channel-names

additionalProperties: false

examples:
  - |
    dac {
        compatible = "dpot-dac";
        vref-supply = <&reg_3v3>;
        io-channels = <&dpot 0>;
        io-channel-names = "dpot";
    };
...
