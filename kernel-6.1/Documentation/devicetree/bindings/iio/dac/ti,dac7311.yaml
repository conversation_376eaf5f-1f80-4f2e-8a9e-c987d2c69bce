# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/dac/ti,dac7311.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Texas Instruments DAC5311 and similar SPI DACs

maintainers:
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - ti,dac7311
      - ti,dac6311
      - ti,dac5311

  reg:
    maxItems: 1

  vref-supply:
    description:
      Reference voltage must be supplied to establish the scaling of the
      output voltage.

required:
  - compatible
  - reg
  - vref-supply

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

unevaluatedProperties: false

examples:
  - |
    spi {
        #address-cells = <1>;
        #size-cells = <0>;

        dac@0 {
            compatible = "ti,dac7311";
            reg = <0>; /* CS0 */
            spi-max-frequency = <1000000>;
            vref-supply = <&vdd_supply>;
        };
    };
...
