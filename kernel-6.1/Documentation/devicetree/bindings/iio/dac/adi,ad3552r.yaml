# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
# Copyright 2020 Analog Devices Inc.
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/dac/adi,ad3552r.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Analog Devices AD2552R DAC device driver

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description: |
  Bindings for the Analog Devices AD3552R DAC device and similar.
  Datasheet can be found here:
    https://www.analog.com/media/en/technical-documentation/data-sheets/ad3542r.pdf
    https://www.analog.com/media/en/technical-documentation/data-sheets/ad3552r.pdf

properties:
  compatible:
    enum:
      - adi,ad3542r
      - adi,ad3552r

  reg:
    maxItems: 1

  spi-max-frequency:
    maximum: 30000000

  reset-gpios:
    maxItems: 1

  ldac-gpios:
    description: |
      LDAC pin to be used as a hardware trigger to update the DAC channels.
    maxItems: 1

  vref-supply:
    description:
      The regulator to use as an external reference. If it does not exists the
      internal reference will be used. External reference must be 2.5V

  adi,vref-out-en:
    description: Vref I/O driven by internal vref to 2.5V. If not set, Vref pin
      will be floating.
    type: boolean

  adi,sdo-drive-strength:
    description: |
      Configure SDIO0 and SDIO1 strength levels:
        - 0: low SDO drive strength.
        - 1: medium low SDO drive strength.
        - 2: medium high SDO drive strength.
        - 3: high SDO drive strength
    $ref: /schemas/types.yaml#/definitions/uint32
    enum: [0, 1, 2, 3]

  '#address-cells':
    const: 1

  '#size-cells':
    const: 0

patternProperties:
  "^channel@([0-1])$":
    type: object
    description: Configurations of the DAC Channels

    additionalProperties: false

    properties:
      reg:
        description: Channel number
        enum: [0, 1]

      adi,output-range-microvolt: true

      custom-output-range-config:
        type: object
        additionalProperties: false
        description: Configuration of custom range when
          adi,output-range-microvolt is not present.
          The formulas for calculation the output voltages are
            Vout_fs = 2.5 + [(GainN + Offset/1024) * 2.5 * Rfbx * 1.03]
            Vout_zs = 2.5 - [(GainP + Offset/1024) * 2.5 * Rfbx * 1.03]

        properties:
          adi,gain-offset:
            description: Gain offset used in the above formula
            $ref: /schemas/types.yaml#/definitions/int32
            maximum: 511
            minimum: -511

          adi,gain-scaling-p-inv-log2:
            description: GainP = 1 / ( 2 ^ adi,gain-scaling-p-inv-log2)
            $ref: /schemas/types.yaml#/definitions/uint32
            enum: [0, 1, 2, 3]

          adi,gain-scaling-n-inv-log2:
            description: GainN = 1 / ( 2 ^ adi,gain-scaling-n-inv-log2)
            $ref: /schemas/types.yaml#/definitions/uint32
            enum: [0, 1, 2, 3]

          adi,rfb-ohms:
            description: Feedback Resistor

        required:
          - adi,gain-offset
          - adi,gain-scaling-p-inv-log2
          - adi,gain-scaling-n-inv-log2
          - adi,rfb-ohms

    required:
      - reg

    oneOf:
      # If adi,output-range-microvolt is missing,
      # custom-output-range-config must be used
      - required:
          - adi,output-range-microvolt

      - required:
          - custom-output-range-config

allOf:
  - if:
      properties:
        compatible:
          contains:
            const: adi,ad3542r
    then:
      patternProperties:
        "^channel@([0-1])$":
          type: object
          properties:
            adi,output-range-microvolt:
              description: |
                Voltage output range of the channel as <minimum, maximum>
                Required connections:
                  Rfb1x for: 0 to 2.5 V; 0 to 3V; 0 to 5 V;
                  Rfb2x for: 0 to 10 V; 2.5 to 7.5V; -5 to 5 V;
              oneOf:
                - items:
                    - const: 0
                    - enum: [2500000, 3000000, 5000000, 10000000]
                - items:
                    - const: -2500000
                    - const: 7500000
                - items:
                    - const: -5000000
                    - const: 5000000

          required:
            - adi,output-range-microvolt

  - if:
      properties:
        compatible:
          contains:
            const: adi,ad3552r
    then:
      patternProperties:
        "^channel@([0-1])$":
          type: object
          properties:
            adi,output-range-microvolt:
              description: |
                Voltage output range of the channel as <minimum, maximum>
                Required connections:
                  Rfb1x for: 0 to 2.5 V; 0 to 5 V;
                  Rfb2x for: 0 to 10 V; -5 to 5 V;
                  Rfb4x for: -10 to 10V
              oneOf:
                - items:
                    - const: 0
                    - enum: [2500000, 5000000, 10000000]
                - items:
                    - const: -5000000
                    - const: 5000000
                - items:
                    - const: -10000000
                    - const: 10000000

required:
  - compatible
  - reg
  - spi-max-frequency

additionalProperties: false

examples:
  - |
    spi {
      #address-cells = <1>;
      #size-cells = <0>;
      ad3552r@0 {
              compatible = "adi,ad3552r";
              reg = <0>;
              spi-max-frequency = <20000000>;
              #address-cells = <1>;
              #size-cells = <0>;
              channel@0 {
                      reg = <0>;
                      adi,output-range-microvolt = <0 10000000>;
              };
              channel@1 {
                      reg = <1>;
                      custom-output-range-config {
                              adi,gain-offset = <5>;
                              adi,gain-scaling-p-inv-log2 = <1>;
                              adi,gain-scaling-n-inv-log2 = <2>;
                              adi,rfb-ohms = <1>;
                      };
            };
        };
    };
...
