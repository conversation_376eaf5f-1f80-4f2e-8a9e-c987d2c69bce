# SPDX-License-Identifier: GPL-2.0 OR BSD-2-Clause
%YAML 1.2
---
$id: "http://devicetree.org/schemas/iio/dac/lltc,ltc2632.yaml#"
$schema: "http://devicetree.org/meta-schemas/core.yaml#"

title: Linear Technology LTC263x 12-/10-/8-Bit Rail-to-Rail DAC

maintainers:
  - <PERSON> <michael.henner<PERSON>@analog.com>

description: |
  Bindings for the Linear Technology LTC2632/2634/2636 DAC
  Datasheet can be found here: https://www.analog.com/media/en/technical-documentation/data-sheets/LTC263[246].pdf

properties:
  compatible:
    enum:
      - lltc,ltc2632-l12
      - lltc,ltc2632-l10
      - lltc,ltc2632-l8
      - lltc,ltc2632-h12
      - lltc,ltc2632-h10
      - lltc,ltc2632-h8
      - lltc,ltc2634-l12
      - lltc,ltc2634-l10
      - lltc,ltc2634-l8
      - lltc,ltc2634-h12
      - lltc,ltc2634-h10
      - lltc,ltc2634-h8
      - lltc,ltc2636-l12
      - lltc,ltc2636-l10
      - lltc,ltc2636-l8
      - lltc,ltc2636-h12
      - lltc,ltc2636-h10
      - lltc,ltc2636-h8

  reg:
    maxItems: 1

  spi-max-frequency:
    maximum: 2000000

  vref-supply:
    description:
      Phandle to the external reference voltage supply. This should
      only be set if there is an external reference voltage connected to the VREF
      pin. If the property is not set the internal reference is used.

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    vref: regulator-vref {
        compatible = "regulator-fixed";
        regulator-name = "vref-ltc2632";
        regulator-min-microvolt = <1250000>;
        regulator-max-microvolt = <1250000>;
        regulator-always-on;
    };

    spi {
      #address-cells = <1>;
      #size-cells = <0>;

      dac@0 {
        compatible = "lltc,ltc2632-l12";
        reg = <0>;    /* CS0 */
        spi-max-frequency = <1000000>;
        vref-supply = <&vref>;
      };
    };
...
