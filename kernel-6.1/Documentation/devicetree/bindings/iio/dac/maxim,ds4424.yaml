# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/dac/maxim,ds4424.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Maxim Integrated DS4422/DS4424 7-bit Sink/Source Current DAC

maintainers:
  - <PERSON> <ih<PERSON><EMAIL>>

description: |
  Datasheet publicly available at:
  https://datasheets.maximintegrated.com/en/ds/DS4422-DS4424.pdf

properties:
  compatible:
    enum:
      - maxim,ds4422
      - maxim,ds4424

  reg:
    maxItems: 1

  vcc-supply: true

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        dac@10 {
            compatible = "maxim,ds4424";
            reg = <0x10>; /* When A0, A1 pins are ground */
            vcc-supply = <&vcc_3v3>;
        };
    };
...
