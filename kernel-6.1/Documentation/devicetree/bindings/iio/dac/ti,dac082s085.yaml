# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/dac/ti,dac082s085.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Texas Instruments DAC082s085 and similar DACs

description:
  A family of Texas Instruments 8/10/12-bit 2/4-channel DACs

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - ti,dac082s085
      - ti,dac102s085
      - ti,dac122s085
      - ti,dac084s085
      - ti,dac104s085
      - ti,dac124s085

  reg:
    maxItems: 1

  spi-cpha: true
  spi-cpol:
    description:
      Must be either spi-cpha, or spi-cpol but not both.

  vref-supply:
    description: Needed to provide output scaling.

required:
  - compatible
  - reg
  - vref-supply

oneOf:
  - required:
      - spi-cpha
  - required:
      - spi-cpol

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

unevaluatedProperties: false

examples:
  - |
    vref_2v5_reg: regulator-vref {
        compatible = "regulator-fixed";
        regulator-name = "2v5";
        regulator-min-microvolt = <2500000>;
        regulator-max-microvolt = <2500000>;
        regulator-always-on;
    };
    spi {
        #address-cells = <1>;
        #size-cells = <0>;

        dac@0 {
            compatible = "ti,dac082s085";
            reg = <0>;
            spi-max-frequency = <40000000>;
            spi-cpol;
            vref-supply = <&vref_2v5_reg>;
        };
    };
...
