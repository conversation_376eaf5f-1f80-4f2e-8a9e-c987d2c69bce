# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/afe/temperature-transducer.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Temperature Transducer

maintainers:
  - <PERSON> <liam<PERSON><EMAIL>>

description: |
  A temperature transducer is a device that converts a thermal quantity
  into any other physical quantity. This binding applies to temperature to
  voltage (like the LTC2997), and temperature to current (like the AD590)
  linear transducers.
  In both cases these are assumed to be connected to a voltage ADC.

  When an io-channel measures the output voltage of a temperature analog front
  end such as a temperature transducer, the interesting measurement is almost
  always the corresponding temperature, not the voltage output. This binding
  describes such a circuit.

  The general transfer function here is (using SI units)
    V(T) = Rsense * Isense(T)
    T = (Isense(T) / alpha) + offset
    T = 1 / (Rsense * alpha) * (V + offset * Rsense * alpha)

  When using a temperature to voltage transducer, Rsense is set to 1.

  The following circuits show a temperature to current and a temperature to
  voltage transducer that can be used with this binding.

           VCC
          -----
            |
        +---+---+
        | AD590 |                               VCC
        +---+---+                              -----
            |                                    |
            V proportional to T             +----+----+
            |                          D+ --+         |
            +---- Vout                      | LTC2997 +--- Vout
            |                          D- --+         |
        +---+----+                          +---------+
        | Rsense |                               |
        +---+----+                             -----
            |                                   GND
          -----
           GND

properties:
  compatible:
    const: temperature-transducer

  io-channels:
    maxItems: 1
    description: |
      Channel node of a voltage io-channel.

  '#io-channel-cells':
    const: 0

  sense-offset-millicelsius:
    description: |
      Temperature offset.
      This offset is commonly used to convert from Kelvins to degrees Celsius.
      In that case, sense-offset-millicelsius would be set to <(-273150)>.
    default: 0

  sense-resistor-ohms:
    description: |
      The sense resistor.
      By default sense-resistor-ohms cancels out the resistor making the
      circuit behave like a temperature transducer.
    default: 1

  alpha-ppm-per-celsius:
    description: |
      Sometimes referred to as output gain, slope, or temperature coefficient.

      alpha is expressed in parts per million which can be micro-amps per
      degrees Celsius or micro-volts per degrees Celsius. The is the main
      characteristic of a temperature transducer and should be stated in the
      datasheet.

additionalProperties: false

required:
  - compatible
  - io-channels
  - alpha-ppm-per-celsius

examples:
  - |
    ad950: temperature-sensor-0 {
        compatible = "temperature-transducer";
        #io-channel-cells = <0>;
        io-channels = <&temp_adc 3>;

        sense-offset-millicelsius = <(-273150)>; /* Kelvin to degrees Celsius */
        sense-resistor-ohms = <8060>;
        alpha-ppm-per-celsius = <1>; /* 1 uA/K */
    };
  - |
    znq_tmp: temperature-sensor-1 {
        compatible = "temperature-transducer";
        #io-channel-cells = <0>;
        io-channels = <&temp_adc 2>;

        sense-offset-millicelsius = <(-273150)>; /* Kelvin to degrees Celsius */
        alpha-ppm-per-celsius = <4000>; /* 4 mV/K */
    };
...
