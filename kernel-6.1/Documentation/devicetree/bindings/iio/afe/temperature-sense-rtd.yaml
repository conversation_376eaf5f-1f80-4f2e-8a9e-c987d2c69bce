# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/afe/temperature-sense-rtd.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Temperature Sense RTD

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  RTDs (Resistance Temperature Detectors) are a kind of temperature sensors
  used to get a linear voltage to temperature reading within a give range
  (usually 0 to 100 degrees Celsius).

  When an io-channel measures the output voltage across an RTD such as a
  PT1000, the interesting measurement is almost always the corresponding
  temperature, not the voltage output. This binding describes such a circuit.

  The general transfer function here is (using SI units)

    V = R(T) * iexc
    R(T) = r0 * (1 + alpha * T)
    T = 1 / (alpha * r0 * iexc) * (V - r0 * iexc)

  The following circuit matches what's in the examples section.

           5V0
          -----
            |
        +---+----+
        |  R 5k  |
        +---+----+
            |
            V 1mA
            |
            +---- Vout
            |
        +---+----+
        | PT1000 |
        +---+----+
            |
          -----
           GND

properties:
  compatible:
    const: temperature-sense-rtd

  io-channels:
    maxItems: 1
    description: |
      Channel node of a voltage io-channel.

  '#io-channel-cells':
    const: 0

  excitation-current-microamp:
    description: The current fed through the RTD sensor.

  alpha-ppm-per-celsius:
    description: |
      alpha can also be expressed in micro-ohms per ohm Celsius. It's a linear
      approximation of the resistance versus temperature relationship
      between 0 and 100 degrees Celsius.

      alpha = (R_100 - R_0) / (100 * R_0)

      Where, R_100 is the resistance of the sensor at 100 degrees Celsius, and
      R_0 (or r-naught-ohms) is the resistance of the sensor at 0 degrees
      Celsius.

      Pure platinum has an alpha of 3925. Industry standards such as IEC60751
      and ASTM E-1137 specify an alpha of 3850.

  r-naught-ohms:
    description: |
      Resistance of the sensor at 0 degrees Celsius.
      Common values are 100 for PT100, 500 for PT500, and 1000 for PT1000

additionalProperties: false
required:
  - compatible
  - io-channels
  - excitation-current-microamp
  - alpha-ppm-per-celsius
  - r-naught-ohms

examples:
  - |
    pt1000_1: temperature-sensor0 {
        compatible = "temperature-sense-rtd";
        #io-channel-cells = <0>;
        io-channels = <&temp_adc1 0>;

        excitation-current-microamp = <1000>; /* i = U/R = 5 / 5000 */
        alpha-ppm-per-celsius = <3908>;
        r-naught-ohms = <1000>;
    };
...
