# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/afe/current-sense-shunt.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Current Sense Shunt

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  When an io-channel measures the voltage over a current sense shunt,
  the interesting measurement is almost always the current through the
  shunt, not the voltage over it. This binding describes such a current
  sense circuit.

properties:
  compatible:
    const: current-sense-shunt

  io-channels:
    maxItems: 1
    description: |
      Channel node of a voltage io-channel.

  "#io-channel-cells":
    const: 0

  shunt-resistor-micro-ohms:
    description: The shunt resistance.

required:
  - compatible
  - io-channels
  - shunt-resistor-micro-ohms

additionalProperties: false

examples:
  - |
    i2c {
        #address-cells = <1>;
        #size-cells = <0>;
        tiadc: adc@48 {
            compatible = "ti,ads1015";
            reg = <0x48>;
            #io-channel-cells = <1>;

            #address-cells = <1>;
            #size-cells = <0>;

            channel@0 { /* IN0,IN1 differential */
                reg = <0>;
                ti,gain = <1>;
                ti,datarate = <4>;
            };
        };
    };
    sysi {
        compatible = "current-sense-shunt";
        io-channels = <&tiadc 0>;
        #io-channel-cells = <0>;

        /* Divide the voltage by 3300000/1000000 (or 3.3) for the current. */
        shunt-resistor-micro-ohms = <3300000>;
    };
...
