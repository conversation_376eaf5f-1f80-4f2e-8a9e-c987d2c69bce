# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/cdc/adi,ad7746.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: AD7746 24-Bit Capacitance-to-Digital Converter with Temperature Sensor

maintainers:
  - <PERSON> <michael.henner<PERSON>@analog.com>

description: |
  AD7746 24-Bit Capacitance-to-Digital Converter with Temperature Sensor

  Specifications about the part can be found at:
  https://www.analog.com/media/en/technical-documentation/data-sheets/ad7291.pdf

properties:
  compatible:
    enum:
      - adi,ad7745
      - adi,ad7746
      - adi,ad7747

  reg:
    maxItems: 1

  adi,excitation-vdd-permille:
    description: |
      Set VDD per mille to be used as the excitation voltage.
    $ref: /schemas/types.yaml#/definitions/uint32
    enum: [125, 250, 375, 500]

  adi,exca-output-en:
    description: Enables the EXCA pin as the excitation output.
    type: boolean

  adi,exca-output-invert:
    description: |
      Inverts the excitation output in the EXCA pin.
      Normally only one of the EXCX pins would be inverted, check the following
      application notes for more details
      https://www.analog.com/media/en/technical-documentation/application-notes/AN-1585.pdf
    type: boolean

  adi,excb-output-en:
    description: Enables the EXCB pin as the excitation output.
    type: boolean

  adi,excb-output-invert:
    description: Inverts the excitation output in the EXCB pin.
    type: boolean

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    i2c {
      #address-cells = <1>;
      #size-cells = <0>;

      ad7746: cdc@48 {
        compatible = "adi,ad7746";
        reg = <0x48>;
        adi,excitation-vdd-permille = <125>;

        adi,exca-output-en;
        adi,exca-output-invert;
        adi,excb-output-en;
        adi,excb-output-invert;
      };
    };
...
