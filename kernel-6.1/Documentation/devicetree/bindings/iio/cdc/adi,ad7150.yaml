# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/cdc/adi,ad7150.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Analog device AD7150 and similar capacitance to digital convertors.

maintainers:
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - adi,ad7150
      - adi,ad7151
      - adi,ad7156

  reg:
    maxItems: 1

  vdd-supply: true

  interrupts: true

allOf:
  - if:
      properties:
        compatible:
          contains:
            enum:
              - adi,ad7150
              - adi,ad7156
    then:
      properties:
        interrupts:
          minItems: 2
          maxItems: 2
  - if:
      properties:
        compatible:
          contains:
            const: adi,ad7151
    then:
      properties:
        interrupts:
          minItems: 1
          maxItems: 1

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        cdc@48 {
            compatible = "adi,ad7150";
            reg = <0x48>;
            interrupts = <25 2>, <26 2>;
            interrupt-parent = <&gpio>;
        };
    };
...
