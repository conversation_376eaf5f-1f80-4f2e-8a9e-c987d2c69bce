# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/frequency/adi,admv4420.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: ADMV4420 K Band Downconverter

maintainers:
  - Cristian <PERSON> <<EMAIL>>

description:
  The ADMV4420 is a highly integrated, double balanced, active
  mixer with an integrated fractional-N synthesizer, ideally suited
  for next generation K band satellite communications

properties:
  compatible:
    enum:
      - adi,admv4420

  reg:
    maxItems: 1

  spi-max-frequency:
    maximum: 1000000

  adi,lo-freq-khz:
    description: LO Frequency
    $ref: /schemas/types.yaml#/definitions/uint32

  adi,ref-ext-single-ended-en:
    description: External reference selected.
    type: boolean

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    spi {
      #address-cells = <1>;
      #size-cells = <0>;
      mixer@0 {
        compatible = "adi,admv4420";
        reg = <0>;
        spi-max-frequency = <1000000>;
        adi,lo-freq-khz = <16750000>;
        adi,ref-ext-single-ended-en;
      };
    };
...
