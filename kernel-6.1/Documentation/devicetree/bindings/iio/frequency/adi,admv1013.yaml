# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/frequency/adi,admv1013.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: ADMV1013 Microwave Upconverter

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description: |
   Wideband, microwave upconverter optimized for point to point microwave
   radio designs operating in the 24 GHz to 44 GHz frequency range.

   https://www.analog.com/en/products/admv1013.html

properties:
  compatible:
    enum:
      - adi,admv1013

  reg:
    maxItems: 1

  spi-max-frequency:
    maximum: 1000000

  clocks:
    description:
      Definition of the external clock.
    minItems: 1

  clock-names:
    items:
      - const: lo_in

  vcm-supply:
    description:
      Analog voltage regulator.

  adi,detector-enable:
    description:
      Enable the Envelope Detector available at output pins VENV_P and
      VENV_N. Disable to reduce power consumption.
    type: boolean

  adi,input-mode:
    description:
      Select the input mode.
      iq - in-phase quadrature (I/Q) input
      if - complex intermediate frequency (IF) input
    enum: [iq, if]

  adi,quad-se-mode:
    description:
      Switch the LO path from differential to single-ended operation.
      se-neg - Single-Ended Mode, Negative Side Disabled.
      se-pos - Single-Ended Mode, Positive Side Disabled.
      diff - Differential Mode.
    enum: [se-neg, se-pos, diff]

  '#clock-cells':
    const: 0

required:
  - compatible
  - reg
  - clocks
  - clock-names
  - vcm-supply

additionalProperties: false

examples:
  - |
    spi {
      #address-cells = <1>;
      #size-cells = <0>;
      admv1013@0{
        compatible = "adi,admv1013";
        reg = <0>;
        spi-max-frequency = <1000000>;
        clocks = <&admv1013_lo>;
        clock-names = "lo_in";
        vcm-supply = <&vcm>;
        adi,quad-se-mode = "diff";
        adi,detector-enable;
      };
    };
...
