# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/frequency/adi,adrf6780.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: ADRF6780 Microwave Upconverter

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description: |
   Wideband, microwave upconverter optimized for point to point microwave
   radio designs operating in the 5.9 GHz to 23.6 GHz frequency range.

   https://www.analog.com/en/products/adrf6780.html

properties:
  compatible:
    enum:
      - adi,adrf6780

  reg:
    maxItems: 1

  spi-max-frequency:
    maximum: 1000000

  clocks:
    description:
      Definition of the external clock.
    minItems: 1

  clock-names:
    items:
      - const: lo_in

  clock-output-names:
    maxItems: 1

  adi,vga-buff-en:
    description:
      RF Variable Gain Amplifier Buffer Enable. Gain is controlled by
      the voltage on the VATT pin.
    type: boolean

  adi,lo-buff-en:
    description:
      Local Oscillator Amplifier Enable. Disable to put the part in
      a power down state.
    type: boolean

  adi,if-mode-en:
    description:
      Intermediate Frequency Mode Enable. Either IF Mode or I/Q Mode
      can be enabled at a time.
    type: boolean

  adi,iq-mode-en:
    description:
      I/Q Mode Enable. Either IF Mode or I/Q Mode can be enabled at a
      time.
    type: boolean

  adi,lo-x2-en:
    description:
      Double the Local Oscillator output frequency from the Local
      Oscillator Input Frequency. Either LOx1 or LOx2 can be enabled
      at a time.
    type: boolean

  adi,lo-ppf-en:
    description:
      Local Oscillator input frequency equal to the Local Oscillator
      output frequency (LO x1). Either LOx1 or LOx2 can be enabled
      at a time.
    type: boolean

  adi,lo-en:
    description:
      Enable additional cirtuitry in the LO chain. Disable to put the
      part in a power down state.
    type: boolean

  adi,uc-bias-en:
    description:
      Enable all bias circuitry thourghout the entire part.
      Disable to put the part in a power down state.
    type: boolean

  adi,lo-sideband:
    description:
      Switch to the Lower LO Sideband. By default the Upper LO
      sideband is enabled.
    type: boolean

  adi,vdet-out-en:
    description:
      VDET Output Select Enable. Expose the RF detector output to the
      VDET external pin.
    type: boolean

  '#clock-cells':
    const: 0

dependencies:
  adi,lo-x2-en: [ "adi,lo-en" ]
  adi,lo-ppf-en: [ "adi,lo-en" ]

required:
  - compatible
  - reg
  - clocks
  - clock-names

additionalProperties: false

examples:
  - |
    spi {
      #address-cells = <1>;
      #size-cells = <0>;
      adrf6780@0 {
        compatible = "adi,adrf6780";
        reg = <0>;
        spi-max-frequency = <1000000>;
        clocks = <&adrf6780_lo>;
        clock-names = "lo_in";
      };
    };
...
