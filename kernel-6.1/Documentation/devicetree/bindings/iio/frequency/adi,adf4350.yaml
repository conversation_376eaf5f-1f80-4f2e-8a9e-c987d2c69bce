# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/frequency/adi,adf4350.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Analog Devices ADF4350/ADF4351 wideband synthesizer

maintainers:
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - adi,adf4350
      - adi,adf4351

  reg:
    maxItems: 1

  spi-max-frequency:
    maximum: 20000000

  clocks:
    maxItems: 1
    description: Clock to provide CLKIN reference clock signal.

  clock-names:
    const: clkin

  gpios:
    maxItems: 1
    description: Lock detect GPIO.

  adi,channel-spacing:
    $ref: /schemas/types.yaml#/definitions/uint32
    description:
      Channel spacing in Hz (influences MODULUS).

  adi,power-up-frequency:
    $ref: /schemas/types.yaml#/definitions/uint32
    description:
      If set the PLL tunes to this frequency (in Hz) on driver probe.

  adi,reference-div-factor:
    $ref: /schemas/types.yaml#/definitions/uint32
    description:
      If set the driver skips dynamic calculation and uses this default
      value instead.

  adi,reference-doubler-enable:
    $ref: /schemas/types.yaml#/definitions/flag
    description: Enables reference doubler.

  adi,reference-div2-enable:
    $ref: /schemas/types.yaml#/definitions/flag
    description: Enables reference divider.

  adi,phase-detector-polarity-positive-enable:
    $ref: /schemas/types.yaml#/definitions/flag
    description: Enables positive phase detector polarity. Default negative.

  adi,lock-detect-precision-6ns-enable:
    $ref: /schemas/types.yaml#/definitions/flag
    description: Enables 6ns lock detect precision. Default = 10ns.

  adi,lock-detect-function-integer-n-enable:
    $ref: /schemas/types.yaml#/definitions/flag
    description:
      Enables lock detect for integer-N mode. Default = factional-N mode.

  adi,charge-pump-current:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: Charge pump current in mA. Default = 2500mA.

  adi,muxout-select:
    $ref: /schemas/types.yaml#/definitions/uint32
    minimum: 0
    maximum: 6
    description: |
      On chip multiplexer output selection.
      Valid values for the multiplexer output are:
      0: Three-State Output (default)
      1: DVDD
      2: DGND
      3: R-Counter output
      4: N-Divider output
      5: Analog lock detect
      6: Digital lock detect

  adi,low-spur-mode-enable:
    $ref: /schemas/types.yaml#/definitions/flag
    description: Enables low spur mode. Default = Low noise mode.

  adi,cycle-slip-reduction-enable:
    $ref: /schemas/types.yaml#/definitions/flag
    description: Enables cycle slip reduction.

  adi,charge-cancellation-enable:
    $ref: /schemas/types.yaml#/definitions/flag
    description:
      Enabled charge pump charge cancellation for integer-N modes.

  adi,anti-backlash-3ns-enable:
    $ref: /schemas/types.yaml#/definitions/flag
    description:
      Enables 3ns antibacklash pulse width for integer-N modes.

  adi,band-select-clock-mode-high-enable:
    $ref: /schemas/types.yaml#/definitions/flag
    description: Enables faster band selection logic.

  adi,12bit-clk-divider:
    $ref: /schemas/types.yaml#/definitions/uint32
    description:
      Clock divider value used when adi,12bit-clkdiv-mode != 0

  adi,clk-divider-mode:
    $ref: /schemas/types.yaml#/definitions/uint32
    enum: [0, 1, 2]
    description: |
      Valid values for the clkdiv mode are:
      0: Clock divider off (default)
      1: Fast lock enable
      2: Phase resync enable

  adi,aux-output-enable:
    $ref: /schemas/types.yaml#/definitions/flag
    description: Enables auxiliary RF output.

  adi,aux-output-fundamental-enable:
    $ref: /schemas/types.yaml#/definitions/flag
    description: |
      Selects fundamental VCO output on the auxiliary RF output.
      Default = Output of RF dividers.

  adi,mute-till-lock-enable:
    $ref: /schemas/types.yaml#/definitions/flag
    description: Enables Mute-Till-Lock-Detect function.

  adi,output-power:
    $ref: /schemas/types.yaml#/definitions/uint32
    enum: [0, 1, 2, 3]
    description: |
      Output power selection.
      Valid values for the power mode are:
      0: -4dBm (default)
      1: -1dBm
      2: +2dBm
      3: +5dBm

  adi,aux-output-power:
    $ref: /schemas/types.yaml#/definitions/uint32
    enum: [0, 1, 2, 3]
    description: |
      Auxiliary output power selection.
      Valid values for the power mode are:
      0: -4dBm (default)
      1: -1dBm
      2: +2dBm
      3: +5dBm

additionalProperties: false

required:
  - compatible
  - reg
  - clocks

examples:
  - |
    spi {
        #address-cells = <1>;
        #size-cells = <0>;

        pll@4 {
            compatible = "adi,adf4351";
            reg = <4>;
            spi-max-frequency = <10000000>;
            clocks = <&clk0_ad9523 9>;
            clock-names = "clkin";
            adi,channel-spacing = <10000>;
            adi,power-up-frequency = <2400000000>;
            adi,phase-detector-polarity-positive-enable;
            adi,charge-pump-current = <2500>;
            adi,output-power = <3>;
            adi,mute-till-lock-enable;
        };
    };
...
