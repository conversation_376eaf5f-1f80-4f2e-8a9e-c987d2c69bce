# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/frequency/adf4371.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Analog Devices ADF4371/ADF4372 Wideband Synthesizers

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description: |
  Analog Devices ADF4371/ADF4372 SPI Wideband Synthesizers
  https://www.analog.com/media/en/technical-documentation/data-sheets/adf4371.pdf
  https://www.analog.com/media/en/technical-documentation/data-sheets/adf4372.pdf

properties:
  compatible:
    enum:
      - adi,adf4371
      - adi,adf4372

  reg:
    maxItems: 1

  clocks:
    description:
      Definition of the external clock (see clock/clock-bindings.txt)
    maxItems: 1

  clock-names:
    description:
      Must be "clkin"
    maxItems: 1

  adi,mute-till-lock-en:
    type: boolean
    description:
      If this property is present, then the supply current to RF8P and RF8N
      output stage will shut down until the ADF4371/ADF4372 achieves lock as
      measured by the digital lock detect circuitry.

required:
  - compatible
  - reg
  - clocks
  - clock-names

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

unevaluatedProperties: false

examples:
  - |
    spi0 {
        #address-cells = <1>;
        #size-cells = <0>;

        frequency@0 {
                compatible = "adi,adf4371";
                reg = <0>;
                spi-max-frequency = <1000000>;
                clocks = <&adf4371_clkin>;
                clock-names = "clkin";
        };
    };
...
