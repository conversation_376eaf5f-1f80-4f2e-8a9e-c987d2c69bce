# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/frequency/adi,admv1014.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: ADMV1014 Microwave Downconverter

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description: |
   Wideband, microwave downconverter optimized for point to point microwave
   radio designs operating in the 24 GHz to 44 GHz frequency range.

   https://www.analog.com/en/products/admv1014.html

properties:
  compatible:
    enum:
      - adi,admv1014

  reg:
    maxItems: 1

  spi-max-frequency:
    maximum: 1000000

  clocks:
    maxItems: 1

  clock-names:
    items:
      - const: lo_in
    description:
      External clock that provides the Local Oscilator input.

  vcm-supply:
    description:
      Common-mode voltage regulator.

  vcc-if-bb-supply:
    description:
      BB and IF supply voltage regulator.

  vcc-vga-supply:
    description:
      RF Amplifier supply voltage regulator.

  vcc-vva-supply:
    description:
      VVA Control Circuit supply voltage regulator.

  vcc-lna-3p3-supply:
    description:
      Low Noise Amplifier 3.3V supply voltage regulator.

  vcc-lna-1p5-supply:
    description:
      Low Noise Amplifier 1.5V supply voltage regulator.

  vcc-bg-supply:
    description:
      Band Gap Circuit supply voltage regulator.

  vcc-quad-supply:
    description:
      Quadruple supply voltage regulator.

  vcc-mixer-supply:
    description:
      Mixer supply voltage regulator.

  adi,input-mode:
    description:
      Select the input mode.
      iq - in-phase quadrature (I/Q) input
      if - complex intermediate frequency (IF) input
    enum: [iq, if]

  adi,detector-enable:
    description:
      Digital Rx Detector Enable. The Square Law Detector output is
      available at output pin VDET.
    type: boolean

  adi,p1db-compensation-enable:
    description:
      Turn on bits to optimize P1dB.
    type: boolean

  adi,quad-se-mode:
    description:
      Switch the LO path from differential to single-ended operation.
      se-neg - Single-Ended Mode, Negative Side Disabled.
      se-pos - Single-Ended Mode, Positive Side Disabled.
      diff - Differential Mode.
    enum: [se-neg, se-pos, diff]

required:
  - compatible
  - reg
  - clocks
  - clock-names
  - vcm-supply

additionalProperties: false

examples:
  - |
    spi {
      #address-cells = <1>;
      #size-cells = <0>;
      converter@0 {
        compatible = "adi,admv1014";
        reg = <0>;
        spi-max-frequency = <1000000>;
        clocks = <&admv1014_lo>;
        clock-names = "lo_in";
        vcm-supply = <&vcm>;
        vcc-if-bb-supply = <&vcc_if_bb>;
        vcc-vga-supply = <&vcc_vga>;
        vcc-vva-supply = <&vcc_vva>;
        vcc-lna-3p3-supply = <&vcc_lna_3p3>;
        vcc-lna-1p5-supply = <&vcc_lna_1p5>;
        vcc-bg-supply = <&vcc_bg>;
        vcc-quad-supply = <&vcc_quad>;
        vcc-mixer-supply = <&vcc_mixer>;
        adi,quad-se-mode = "diff";
        adi,detector-enable;
        adi,p1db-compensation-enable;
      };
    };
...
