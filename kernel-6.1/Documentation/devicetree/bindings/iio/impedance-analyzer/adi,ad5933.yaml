# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/impedance-analyzer/adi,ad5933.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Analog Devices AD5933/AD5934 Impedance Converter, Network Analyzer

maintainers:
  - <PERSON><PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

description: |
  https://www.analog.com/media/en/technical-documentation/data-sheets/AD5933.pdf
  https://www.analog.com/media/en/technical-documentation/data-sheets/AD5934.pdf

properties:
  compatible:
    enum:
      - adi,ad5933
      - adi,ad5934

  reg:
    maxItems: 1

  vdd-supply:
    description: |
      The regulator supply for DVDD, AVDD1 and AVDD2 when they
      are connected together.  Used to calculate voltage scaling of measurement
      channels.

  clocks:
    maxItems: 1

  clock-names:
    const: mclk

additionalProperties: false

required:
  - compatible
  - reg
  - vdd-supply

examples:
  - |
    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        impedance-analyzer@d {
            compatible = "adi,ad5933";
            reg = <0x0d>;
            vdd-supply = <&vdd_supply>;
            clocks = <&ref_clk>;
            clock-names = "mclk";
        };
    };
...
