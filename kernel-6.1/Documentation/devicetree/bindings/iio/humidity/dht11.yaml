# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/humidity/dht11.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: DHT11 humidity + temperature sensor

maintainers:
  - <PERSON> <<EMAIL>>

description: |
   A simple and low cost module providing a non standard single GPIO based
   interface.  It is believed the part is made by aosong but don't have
   absolute confirmation of this, or what the aosong part number is.

properties:
  compatible:
    const: dht11

  reg:
    maxItems: 1

  gpios:
    maxItems: 1
    description:
      Single, interrupt capable, GPIO used to communicate with the device.

required:
  - compatible
  - gpios

additionalProperties: false

examples:
  - |
    humidity_sensor {
        compatible = "dht11";
        gpios = <&gpio0 6 0>;
    };
...
