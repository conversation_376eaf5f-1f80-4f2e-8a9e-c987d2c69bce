# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/humidity/st,hts221.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: HTS221 STM humidity + temperature sensor

maintainers:
  - <PERSON> <<EMAIL>>

description: |
   Humidity and temperature sensor with I2C interface and data ready
   interrupt.

properties:
  compatible:
    const: st,hts221

  reg:
    maxItems: 1

  drive-open-drain:
    type: boolean
    description:
      The interrupt/data ready line will be configured as open drain, which
      is useful if several sensors share the same interrupt line.

  vdd-supply: true

  interrupts:
    maxItems: 1

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        hts221@5f {
            compatible = "st,hts221";
            reg = <0x5f>;
            interrupt-parent = <&gpio0>;
            interrupts = <0 IRQ_TYPE_EDGE_RISING>;
        };
    };
...
