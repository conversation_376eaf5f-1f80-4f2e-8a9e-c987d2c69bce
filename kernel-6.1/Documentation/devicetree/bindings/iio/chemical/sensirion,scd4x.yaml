# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/chemical/sensirion,scd4x.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Sensirion SCD4X carbon dioxide sensor

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description: |
  Air quality sensor capable of measuring co2 concentration, temperature
  and relative humidity.

properties:
  compatible:
    enum:
      - sensirion,scd40
      - sensirion,scd41

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  vdd-supply: true

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    i2c {
      #address-cells = <1>;
      #size-cells = <0>;

      co2-sensor@62 {
        compatible = "sensirion,scd41";
        reg = <0x62>;
      };
    };
