# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/chemical/ams,ccs811.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: AMS CCS811 VOC Sensor

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description: |
  Ultra-Low Power Digital Gas Sensor for Monitoring Indoor Air Quality.

properties:
  compatible:
    enum:
      - ams,ccs811
  reg:
    maxItems: 1

  reset-gpios:
    description: GPIO connected to the nRESET line. This is an active low
                 input to CCS811.
    maxItems: 1

  wakeup-gpios:
    description: GPIO connected to the nWAKE line. This is an active low
                 input to CCS811.
    maxItems: 1

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>
    i2c {
      #address-cells = <1>;
      #size-cells = <0>;

      voc@5b {
        compatible = "ams,ccs811";
        reg = <0x5b>;
        reset-gpios = <&gpioa 11 GPIO_ACTIVE_LOW>;
        wakeup-gpios = <&gpioa 12 GPIO_ACTIVE_LOW>;
      };
    };

...
