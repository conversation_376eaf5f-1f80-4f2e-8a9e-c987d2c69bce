# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/chemical/senseair,sunrise.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Senseair Sunrise 006-0-0007 CO2 Sensor

maintainers:
  - <PERSON><PERSON><PERSON> <jaco<PERSON>@jmondi.org>

description: |
  Senseair Sunrise 006-0-0007 is a NDIR CO2 sensor. It supports I2C or UART buses
  for communications and control.

  Datasheets:
    https://rmtplusstoragesenseair.blob.core.windows.net/docs/Dev/publicerat/PSP11704.pdf
    https://rmtplusstoragesenseair.blob.core.windows.net/docs/Dev/publicerat/PSH11649.pdf
    https://rmtplusstoragesenseair.blob.core.windows.net/docs/Dev/publicerat/TDE5531.pdf
    https://rmtplusstoragesenseair.blob.core.windows.net/docs/Market/publicerat/TDE7318.pdf

properties:
  compatible:
    const: senseair,sunrise-006-0-0007

  reg:
    maxItems: 1

  ndry-gpios:
    maxItems: 1
    description:
      Phandle to the GPIO line connected to the nDRY pin. Typically active low.

  en-gpios:
    maxItems: 1
    description:
      Phandle to the GPIO line connected to the EN pin. Typically active high.

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    i2c {
      #address-cells = <1>;
      #size-cells = <0>;

      co2-sensor@68 {
        compatible = "senseair,sunrise-006-0-0007";
        reg = <0x68>;
      };
    };
