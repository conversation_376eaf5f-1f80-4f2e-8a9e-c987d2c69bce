# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/chemical/atlas,sensor.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Atlas Scientific OEM + EZO sensors

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Atlas Scientific OEM + EZO sensors connected via I2C

  Datasheets:
    http://www.atlas-scientific.com/_files/_datasheets/_oem/DO_oem_datasheet.pdf
    http://www.atlas-scientific.com/_files/_datasheets/_oem/EC_oem_datasheet.pdf
    http://www.atlas-scientific.com/_files/_datasheets/_oem/ORP_oem_datasheet.pdf
    http://www.atlas-scientific.com/_files/_datasheets/_oem/pH_oem_datasheet.pdf
    http://www.atlas-scientific.com/_files/_datasheets/_oem/RTD_oem_datasheet.pdf
    http://www.atlas-scientific.com/_files/_datasheets/_probe/EZO_CO2_Datasheet.pdf
    https://www.atlas-scientific.com/files/EZO_O2_datasheet.pdf
    https://www.atlas-scientific.com/files/EZO_HUM_Datasheet.pdf

properties:
  compatible:
    enum:
      - atlas,do-sm
      - atlas,ec-sm
      - atlas,orp-sm
      - atlas,ph-sm
      - atlas,rtd-sm
      - atlas,co2-ezo
      - atlas,o2-ezo
      - atlas,hum-ezo

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    i2c {
      #address-cells = <1>;
      #size-cells = <0>;

      atlas@66 {
        compatible = "atlas,orp-sm";
        reg = <0x66>;
        interrupt-parent = <&gpio1>;
        interrupts = <16 2>;
      };
    };
