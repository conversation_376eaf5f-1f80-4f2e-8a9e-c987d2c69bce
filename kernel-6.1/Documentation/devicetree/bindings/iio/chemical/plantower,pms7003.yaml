# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/chemical/plantower,pms7003.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Plantower PMS7003 air pollution sensor

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description: |
  Air pollution sensor capable of measuring mass concentration of dust
  particles.

properties:
  compatible:
    enum:
      - plantower,pms1003
      - plantower,pms3003
      - plantower,pms5003
      - plantower,pms6003
      - plantower,pms7003
      - plantower,pmsa003

  vcc-supply:
    description: regulator that provides power to the sensor

  plantower,set-gpios:
    description: GPIO connected to the SET line
    maxItems: 1

  reset-gpios:
    description: GPIO connected to the RESET line
    maxItems: 1

required:
  - compatible
  - vcc-supply

additionalProperties: false

examples:
  - |
    serial {
      air-pollution-sensor {
        compatible = "plantower,pms7003";
        vcc-supply = <&reg_vcc5v0>;
      };
    };

...
