# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/sigma-delta-modulator.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Device-Tree bindings for sigma delta modulator

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    description: |
      "sd-modulator" can be used as a generic SD modulator,
      if the modulator is not specified in the compatible list.
    enum:
      - sd-modulator
      - ads1201

  '#io-channel-cells':
    const: 0

required:
  - compatible
  - '#io-channel-cells'

additionalProperties: false

examples:
  - |
    ads1202: adc {
      compatible = "sd-modulator";
      #io-channel-cells = <0>;
    };

...
