# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/atmel,sama5d2-adc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: AT91 SAMA5D2 Analog to Digital Converter (ADC)

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - atmel,sama5d2-adc
      - microchip,sam9x60-adc
      - microchip,sama7g5-adc

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks:
    maxItems: 1

  clock-names:
    const: adc_clk

  vref-supply: true
  vddana-supply: true

  atmel,min-sample-rate-hz:
    description: Minimum sampling rate, it depends on SoC.

  atmel,max-sample-rate-hz:
    description: Maximum sampling rate, it depends on SoC.

  atmel,startup-time-ms:
    description: Startup time expressed in ms, it depends on SoC.

  atmel,trigger-edge-type:
    $ref: '/schemas/types.yaml#/definitions/uint32'
    description:
      One of possible edge types for the ADTRG hardware trigger pin.
      When the specific edge type is detected, the conversion will
      start. Should be one of IRQ_TYPE_EDGE_RISING, IRQ_TYPE_EDGE_FALLING
      or IRQ_TYPE_EDGE_BOTH.
    enum: [1, 2, 3]

  dmas:
    maxItems: 1

  dma-names:
    const: rx

  "#io-channel-cells":
    const: 1

additionalProperties: false

required:
  - compatible
  - reg
  - interrupts
  - clocks
  - clock-names
  - vref-supply
  - vddana-supply
  - atmel,min-sample-rate-hz
  - atmel,max-sample-rate-hz
  - atmel,startup-time-ms

examples:
  - |
    #include <dt-bindings/dma/at91.h>
    #include <dt-bindings/interrupt-controller/irq.h>
    soc {
        #address-cells = <1>;
        #size-cells = <1>;

        adc@fc030000 {
            compatible = "atmel,sama5d2-adc";
            reg = <0xfc030000 0x100>;
            interrupts = <40 IRQ_TYPE_LEVEL_HIGH 7>;
            clocks = <&adc_clk>;
            clock-names = "adc_clk";
            atmel,min-sample-rate-hz = <200000>;
            atmel,max-sample-rate-hz = <20000000>;
            atmel,startup-time-ms = <4>;
            vddana-supply = <&vdd_3v3_lp_reg>;
            vref-supply = <&vdd_3v3_lp_reg>;
            atmel,trigger-edge-type = <IRQ_TYPE_EDGE_BOTH>;
            dmas = <&dma0 (AT91_XDMAC_DT_MEM_IF(0) | AT91_XDMAC_DT_PER_IF(1) | AT91_XDMAC_DT_PERID(25))>;
            dma-names = "rx";
            #io-channel-cells = <1>;
        };
    };
...
