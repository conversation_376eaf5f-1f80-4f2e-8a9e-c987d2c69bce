# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/ti,ads7950.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Texas Instruments ADS7950 and similar ADCs

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Family of 4-16 channel, 8-12 bit ADCs with SPI interface.

properties:
  compatible:
    enum:
      - ti,ads7950
      - ti,ads7951
      - ti,ads7952
      - ti,ads7953
      - ti,ads7954
      - ti,ads7955
      - ti,ads7956
      - ti,ads7957
      - ti,ads7958
      - ti,ads7959
      - ti,ads7960
      - ti,ads7961

  reg:
    maxItems: 1

  spi-max-frequency:
    maximum: 20000000

  vref-supply:
    description: Supplies the 2.5V or 5V reference voltage

  "#io-channel-cells":
    const: 1

required:
  - compatible
  - reg
  - vref-supply
  - "#io-channel-cells"

additionalProperties: false

examples:
  - |
    spi {
        #address-cells = <1>;
        #size-cells = <0>;

        adc@0 {
            compatible = "ti,ads7957";
            reg = <0>;
            vref-supply = <&refin_supply>;
            spi-max-frequency = <10000000>;
            #io-channel-cells = <1>;
        };
    };
...
