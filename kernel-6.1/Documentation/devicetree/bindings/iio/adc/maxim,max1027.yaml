# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/maxim,max1027.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Maxim MAX1027 and similar ADCs

maintainers:
  - <PERSON><PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

description: |
  300ks/s SPI ADCs with temperature sensors.

properties:
  compatible:
    enum:
        # 10-bit 8 channels
      - maxim,max1027
        # 10-bit 12 channels
      - maxim,max1029
        # 10-bit 16 channels
      - maxim,max1031
         # 12-bit 8 channels
      - maxim,max1227
         # 12-bit 12 channels
      - maxim,max1229
         # 12-bit 16 channels
      - maxim,max1231

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  spi-max-frequency:
    maximum: 10000000

  "#io-channel-cells":
    const: 1

required:
  - compatible
  - reg

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    spi {
       #address-cells = <1>;
       #size-cells = <0>;
        maxadc: adc@0 {
            compatible = "maxim,max1027";
            reg = <0>;
            #io-channel-cells = <1>;
            interrupt-parent = <&gpio5>;
            interrupts = <15 IRQ_TYPE_EDGE_RISING>;
            spi-max-frequency = <1000000>;
        };
    };
...
