# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/ti,ads131e08.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Texas Instruments ADS131E0x 4-, 6- and 8-Channel ADCs

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  The ADS131E0x are a family of multichannel, simultaneous sampling,
  24-bit, delta-sigma, analog-to-digital converters (ADCs) with a
  built-in programmable gain amplifier (PGA), internal reference
  and an onboard oscillator.
  The communication with ADC chip is via the SPI bus (mode 1).

  https://www.ti.com/lit/ds/symlink/ads131e08.pdf

properties:
  compatible:
    enum:
      - ti,ads131e04
      - ti,ads131e06
      - ti,ads131e08

  reg:
    maxItems: 1

  spi-cpha: true

  clocks:
    description: |
      Device tree identifier to the clock source (2.048 MHz).
      Note: clock source is selected using CLKSEL pin.
    maxItems: 1

  clock-names:
    items:
      - const: adc-clk

  interrupts:
    description: |
      IRQ line for the ADC data ready.
    maxItems: 1

  vref-supply:
    description: |
      Optional external voltage reference. If not supplied, internal voltage
      reference is used.

  ti,vref-internal:
    description: |
      Select the internal voltage reference value.
      0: 2.4V
      1: 4.0V
      If this field is left empty, 2.4V is selected.
      Note: internal voltage reference is used only if vref-supply is not supplied.
    $ref: /schemas/types.yaml#/definitions/uint32
    enum: [0, 1]
    default: 0

  '#address-cells':
    const: 1

  '#size-cells':
    const: 0

required:
  - compatible
  - reg
  - spi-cpha
  - clocks
  - clock-names
  - interrupts

patternProperties:
  "^channel@([0-7])$":
    $ref: "adc.yaml"
    type: object
    description: |
      Represents the external channels which are connected to the ADC.

    properties:
      reg:
        description: |
          The channel number.
          Up to 4 channels, numbered from 0 to 3 for ti,ads131e04.
          Up to 6 channels, numbered from 0 to 5 for ti,ads131e06.
          Up to 8 channels, numbered from 0 to 7 for ti,ads131e08.
        items:
          minimum: 0
          maximum: 7

      ti,gain:
        description: |
          The PGA gain value for the channel.
          If this field is left empty, PGA gain 1 is used.
        $ref: /schemas/types.yaml#/definitions/uint32
        enum: [1, 2, 4, 8, 12]
        default: 1

      ti,mux:
        description: |
          Channel input selection(muliplexer).
          0: Normal input.
          1: Input shorted to (VREFP + VREFN) / 2 (for offset or noise measurements).
          3: MVDD (for supply measurement)
          4: Temperature sensor
          If this field is left empty, normal input is selected.
        $ref: /schemas/types.yaml#/definitions/uint32
        enum: [0, 1, 3, 4]
        default: 0

    required:
      - reg

    additionalProperties: false

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>

    spi {
      #address-cells = <1>;
      #size-cells = <0>;

      adc@0 {
        compatible = "ti,ads131e08";
        reg = <0>;
        spi-max-frequency = <1000000>;
        spi-cpha;
        clocks = <&clk2048k>;
        clock-names = "adc-clk";
        interrupt-parent = <&gpio5>;
        interrupts = <28 IRQ_TYPE_EDGE_FALLING>;
        vref-supply = <&adc_vref>;

        #address-cells = <1>;
        #size-cells = <0>;

        channel@0 {
          reg = <0>;
        };

        channel@1 {
          reg = <1>;
        };

        channel@2 {
          reg = <2>;
          ti,gain = <2>;
        };

        channel@3 {
          reg = <3>;
        };

        channel@4 {
          reg = <4>;
        };

        channel@5 {
          reg = <5>;
        };

        channel@6 {
          reg = <6>;
        };

        channel@7 {
          reg = <7>;
          ti,mux = <4>;
        };
      };
    };
