# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/nxp,imx8qxp-adc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: NXP IMX8QXP ADC bindings

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description:
  Supports the ADC found on the IMX8QXP SoC.

properties:
  compatible:
    const: nxp,imx8qxp-adc

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks:
    maxItems: 2

  clock-names:
    items:
      - const: per
      - const: ipg

  assigned-clocks:
    maxItems: 1

  assigned-clock-rates:
    maxItems: 1

  power-domains:
    maxItems: 1

  "#io-channel-cells":
    const: 1

required:
  - compatible
  - reg
  - interrupts
  - clocks
  - clock-names
  - assigned-clocks
  - assigned-clock-rates
  - power-domains
  - "#io-channel-cells"

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/firmware/imx/rsrc.h>
    soc {
        #address-cells = <2>;
        #size-cells = <2>;
        adc@5a880000 {
            compatible = "nxp,imx8qxp-adc";
            reg = <0x0 0x5a880000 0x0 0x10000>;
            interrupts = <GIC_SPI 240 IRQ_TYPE_LEVEL_HIGH>;
            clocks = <&clk IMX_SC_R_ADC_0>,
                     <&clk IMX_SC_R_ADC_0>;
            clock-names = "per", "ipg";
            assigned-clocks = <&clk IMX_SC_R_ADC_0>;
            assigned-clock-rates = <24000000>;
            power-domains = <&pd IMX_SC_R_ADC_0>;
            #io-channel-cells = <1>;
        };
    };
...
