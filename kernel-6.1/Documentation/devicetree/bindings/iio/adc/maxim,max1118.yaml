# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/maxim,max1118.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Maxim MAX1118 and similar ADCs

maintainers:
  - Akinob<PERSON> Mita <<EMAIL>>

description: |
    Dual channel 8bit ADCs.

properties:
  compatible:
    enum:
      - maxim,max1117
      - maxim,max1118
      - maxim,max1119

  reg:
    maxItems: 1

  spi-max-frequency:
    maximum: 5000000

  vref-supply:
    description: External reference, needed to establish input scaling

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#
  - if:
      properties:
        compatible:
          contains:
            const: maxim,max1118
    then:
      required:
        - vref-supply
    else:
      properties:
        vref-supply: false

required:
  - compatible
  - reg

unevaluatedProperties: false

examples:
  - |
    spi {
        #address-cells = <1>;
        #size-cells = <0>;

        adc@0 {
            compatible = "maxim,max1118";
            reg = <0>;
            vref-supply = <&adc_vref>;
            spi-max-frequency = <1000000>;
        };
    };
...
