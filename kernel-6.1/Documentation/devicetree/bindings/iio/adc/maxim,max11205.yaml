# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/maxim,max11205.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Maxim MAX11205 ADC

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description: |
  The MAX11205 is an ultra-low-power (< 300FA max active current),
  high-resolution, serial-output ADC.

  https://datasheets.maximintegrated.com/en/ds/MAX11205.pdf

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

properties:
  compatible:
    enum:
      - maxim,max11205a
      - maxim,max11205b

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  spi-max-frequency:
    maximum: 5000000

  spi-cpha: true

  vref-supply:
    description:
      The regulator supply for the ADC reference voltage. This is a differential
      reference. It is equal to the V_REFP - V_REFN. The maximum value is 3.6V.

required:
  - compatible
  - reg
  - interrupts
  - spi-max-frequency
  - spi-cpha
  - vref-supply

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    spi {
        #address-cells = <1>;
        #size-cells = <0>;
        adc@0 {
            compatible = "maxim,max11205a";
            reg = <0>;
            spi-max-frequency = <5000000>;
            spi-cpha;
            interrupt-parent = <&gpio>;
            interrupts = <19 IRQ_TYPE_EDGE_FALLING>;
            vref-supply = <&max11205_vref>;
        };
    };
...
