# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/x-powers,axp209-adc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: X-Powers AXP ADC bindings

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description: |
  ADC is frequently used as a provider to consumers of the ADC channels.
  <PERSON><PERSON> is a child of an axp209 multifunction device
  ADC channels and their indexes per variant:

  AXP209
  ------
   0 | acin_v
   1 | acin_i
   2 | vbus_v
   3 | vbus_i
   4 | pmic_temp
   5 | gpio0_v
   6 | gpio1_v
   7 | ipsout_v
   8 | batt_v
   9 | batt_chrg_i
  10 | batt_dischrg_i
  11 | ts_v

  AXP22x
  ------
   0 | pmic_temp
   1 | batt_v
   2 | batt_chrg_i
   3 | batt_dischrg_i
   4 | ts_v

  AXP813
  ------
   0 | pmic_temp
   1 | gpio0_v
   2 | batt_v
   3 | batt_chrg_i
   4 | batt_dischrg_i
   5 | ts_v


properties:
  compatible:
    oneOf:
      - const: x-powers,axp209-adc
      - const: x-powers,axp221-adc
      - const: x-powers,axp813-adc

      - items:
          - const: x-powers,axp803-adc
          - const: x-powers,axp813-adc

  "#io-channel-cells":
    const: 1

additionalProperties: false

examples:
  - |
    axp221 {
        adc {
            compatible = "x-powers,axp221-adc";
            #io-channel-cells = <1>;
        };
    };
...
