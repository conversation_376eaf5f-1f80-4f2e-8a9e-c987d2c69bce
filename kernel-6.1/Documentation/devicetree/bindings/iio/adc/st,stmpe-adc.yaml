# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/st,stmpe-adc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: ADC on an STMPE multifunction device.

maintainers:
  - <PERSON> <<EMAIL>>

description:
  This ADC forms part of an ST microelectronics STMPE multifunction device .
  The ADC is shared with the STMPE touchscreen. As a result some ADC related
  settings are specified in the parent node.
  The node should be a child node of the stmpe node to which it belongs.

properties:
  compatible:
    const: st,stmpe-adc

  st,norequest-mask:
    $ref: /schemas/types.yaml#/definitions/uint32
    description:
      Bitmask specifying which ADC channels should _not_ be
      requestable due to different usage (e.g. touch).

  "#io-channel-cells":
    const: 1

required:
  - compatible

additionalProperties: false

examples:
  - |
    stmpe {
        stmpe_adc {
            compatible = "st,stmpe-adc";
            st,norequest-mask = <0x0F>; /* dont use ADC CH3-0 */
        };
    };
...
