# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/maxim,max9611.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Maxim MAX9611 and similar current sense amplifiers with integrated ADCs

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description: |
   These devices combine a high-side current sense amplifier with a 12 bit ADC.
   They have an i2c interface.

properties:
  compatible:
    enum:
      - maxim,max9611
      - maxim,max9612

  reg:
    maxItems: 1

  shunt-resistor-micro-ohms:
    description: |
      Value in micro Ohms of the shunt resistor connected between the RS+ and
      RS- inputs, across which the current is measured.  Value needed to compute
      the scaling of the measured current.

additionalProperties: false

required:
  - compatible
  - reg
  - shunt-resistor-micro-ohms

examples:
  - |
    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        adc@7c {
            compatible = "maxim,max9611";
            reg = <0x7c>;
            shunt-resistor-micro-ohms = <5000>;
        };
    };
...
