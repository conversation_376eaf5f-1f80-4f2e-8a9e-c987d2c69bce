# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/adi,ad9467.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Analog Devices AD9467 and similar High-Speed ADCs

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  The AD9467 and the parts similar with it, are high-speed analog-to-digital
  converters (ADCs), operating in the range of 100 to 500 mega samples
  per second (MSPS). Some parts support higher MSPS and some
  lower MSPS, suitable for the intended application of each part.

  All the parts support the register map described by Application Note AN-877
   https://www.analog.com/media/en/technical-documentation/application-notes/AN-877.pdf

  https://www.analog.com/media/en/technical-documentation/data-sheets/AD9265.pdf
  https://www.analog.com/media/en/technical-documentation/data-sheets/AD9434.pdf
  https://www.analog.com/media/en/technical-documentation/data-sheets/AD9467.pdf

properties:
  compatible:
    enum:
      - adi,ad9265
      - adi,ad9434
      - adi,ad9467

  reg:
    maxItems: 1

  clocks:
    maxItems: 1

  clock-names:
    items:
      - const: adc-clk

  powerdown-gpios:
    description:
      Pin that controls the powerdown mode of the device.
    maxItems: 1

  reset-gpios:
    description:
      Reset pin for the device.
    maxItems: 1

required:
  - compatible
  - reg
  - clocks
  - clock-names

additionalProperties: false

examples:
  - |
    spi {
        #address-cells = <1>;
        #size-cells = <0>;

        adc@0 {
          compatible = "adi,ad9467";
          reg = <0>;
          clocks = <&adc_clk>;
          clock-names = "adc-clk";
        };
    };
...
