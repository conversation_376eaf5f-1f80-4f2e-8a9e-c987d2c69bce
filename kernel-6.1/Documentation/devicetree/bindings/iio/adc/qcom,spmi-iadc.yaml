# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/qcom,spmi-iadc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm's SPMI PMIC current ADC

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  QPNP PMIC current ADC (IADC) provides interface to clients to read current.
  A 16 bit ADC is used for current measurements. IADC can measure the current
  through an external resistor (channel 1) or internal (built-in) resistor
  (channel 0). When using an external resistor it is to be described by
  qcom,external-resistor-micro-ohms property.

properties:
  compatible:
    const: qcom,spmi-iadc

  reg:
    description: IADC base address and length in the SPMI PMIC register map
    maxItems: 1

  qcom,external-resistor-micro-ohms:
    description:
      Sensor resistor value. If not defined value of 10000 micro Ohms
      will be used.

  interrupts:
    maxItems: 1
    description:
      End of conversion interrupt.

  "#io-channel-cells":
    const: 1

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    spmi_bus {
        #address-cells = <1>;
        #size-cells = <0>;
        pmic_iadc: adc@3600 {
            compatible = "qcom,spmi-iadc";
            reg = <0x3600>;
            interrupts = <0x0 0x36 0x0 IRQ_TYPE_EDGE_RISING>;
            qcom,external-resistor-micro-ohms = <10000>;
            #io-channel-cells  = <1>;
        };
    };
...
