# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/holt,hi8435.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Holt Integrated Circuits HI-8435 SPI threshold detector

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Datasheet: http://www.holtic.com/documents/427-hi-8435_v-rev-lpdf.do

properties:
  compatible:
    const: holt,hi8435

  reg:
    maxItems: 1

  gpios:
    description:
      GPIO used for controlling the reset pin
    maxItems: 1

  "#io-channel-cells":
    const: 1

required:
  - compatible
  - reg

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

unevaluatedProperties: false

examples:
  - |
    spi {
        #address-cells = <1>;
        #size-cells = <0>;
        threshold-detector@0 {
            compatible = "holt,hi8435";
            reg = <0>;
            gpios = <&gpio6 1 0>;
            spi-max-frequency = <1000000>;
        };
    };
...
