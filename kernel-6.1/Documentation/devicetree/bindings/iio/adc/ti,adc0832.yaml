# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/ti,adc0832.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Texas Instruments ADC0832 and similar ADCs

maintainers:
  - Akinob<PERSON> Mita <<EMAIL>>

description: |
  8 bit ADCs with 1, 2, 4 or 8 inputs for single ended or differential
  conversion.

properties:
  compatible:
    enum:
      - ti,adc0831
      - ti,adc0832
      - ti,adc0834
      - ti,adc0838

  reg:
    maxItems: 1

  vref-supply:
    description: External reference, needed to establish input scaling

  "#io-channel-cells":
    const: 1

required:
  - compatible
  - reg
  - vref-supply

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

unevaluatedProperties: false

examples:
  - |
    spi {
        #address-cells = <1>;
        #size-cells = <0>;

        adc@0 {
            compatible = "ti,adc0832";
            reg = <0>;
            vref-supply = <&vdd_supply>;
            spi-max-frequency = <200000>;
            #io-channel-cells = <1>;
        };
    };
...
