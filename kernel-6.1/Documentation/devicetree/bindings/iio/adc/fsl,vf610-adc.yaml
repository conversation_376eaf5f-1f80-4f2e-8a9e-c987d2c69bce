# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/fsl,vf610-adc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: ADC found on Freescale vf610 and similar SoCs

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description:
  ADCs found on vf610/i.MX6slx and upward SoCs from Freescale.

properties:
  compatible:
    oneOf:
      - items:
          - enum:
              - fsl,imx6sx-adc
              - fsl,imx6ul-adc
          - const: fsl,vf610-adc
      - items:
          - const: fsl,vf610-adc

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks:
    description: ADC source clock (ipg clock)
    maxItems: 1

  clock-names:
    const: adc

  vref-supply:
    description: ADC reference voltage supply.

  fsl,adck-max-frequency:
    $ref: /schemas/types.yaml#/definitions/uint32-array
    minItems: 3
    maxItems: 3
    description: |
      Maximum frequencies from datasheet operating requirements.
      Three values necessary to cover the 3 conversion modes.
      * Frequency in normal mode (ADLPC=0, ADHSC=0)
      * Frequency in high-speed mode (ADLPC=0, ADHSC=1)
      * Frequency in low-power mode (ADLPC=1, ADHSC=0)

  min-sample-time:
    $ref: /schemas/types.yaml#/definitions/uint32
    description:
      Minimum sampling time in nanoseconds. This value has
      to be chosen according to the conversion mode and the connected analog
      source resistance (R_as) and capacitance (C_as). Refer the datasheet's
      operating requirements. A safe default across a wide range of R_as and
      C_as as well as conversion modes is 1000ns.

  "#io-channel-cells":
    const: 1

required:
  - compatible
  - reg
  - interrupts
  - clocks
  - clock-names
  - vref-supply

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/vf610-clock.h>
    adc@4003b000 {
        compatible = "fsl,vf610-adc";
        reg = <0x4003b000 0x1000>;
        interrupts = <0 53 0x04>;
        clocks = <&clks VF610_CLK_ADC0>;
        clock-names = "adc";
        fsl,adck-max-frequency = <30000000>, <40000000>, <20000000>;
        vref-supply = <&reg_vcc_3v3_mcu>;
        min-sample-time = <10000>;
    };
...
