# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/envelope-detector.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: ADC envelope detector using a DAC and a comparator

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  The DAC is used to find the peak level of an alternating voltage input
  signal by a binary search using the output of a comparator wired to
  an interrupt pin. Like so:
                          _
                         | \
    input +------>-------|+ \
                         |   \
           .-------.     |    }---.
           |       |     |   /    |
           |    dac|-->--|- /     |
           |       |     |_/      |
           |       |              |
           |       |              |
           |    irq|------<-------'
           |       |
           '-------'

properties:
  compatible:
    const: axentia,tse850-envelope-detector

  io-channels:
    maxItems: 1
    description: Channel node of the dac to be used for comparator input.

  io-channel-names:
    const: dac

  interrupts:
    maxItems: 1

  interrupt-names:
    const: comp

required:
  - compatible
  - io-channels
  - io-channel-names
  - interrupts
  - interrupt-names

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    i2c {
        #address-cells = <1>;
        #size-cells = <0>;
        dpot: dpot@28 {
            compatible = "microchip,mcp4651-104";
            reg = <0x28>;
            #io-channel-cells = <1>;
        };
    };

    dac: dac {
        compatible = "dpot-dac";
        vref-supply = <&reg_3v3>;
        io-channels = <&dpot 0>;
        io-channel-names = "dpot";
        #io-channel-cells = <1>;
    };

    envelope-detector {
        compatible = "axentia,tse850-envelope-detector";
        io-channels = <&dac 0>;
        io-channel-names = "dac";

        interrupt-parent = <&gpio>;
        interrupts = <3 IRQ_TYPE_EDGE_FALLING>;
        interrupt-names = "comp";
    };
...
