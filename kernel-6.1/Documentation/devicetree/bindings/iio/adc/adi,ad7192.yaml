# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
# Copyright 2019 Analog Devices Inc.
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/adi,ad7192.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Analog Devices AD7192 ADC device driver

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Bindings for the Analog Devices AD7192 ADC device. Datasheet can be
  found here:
  https://www.analog.com/media/en/technical-documentation/data-sheets/AD7192.pdf

properties:
  compatible:
    enum:
      - adi,ad7190
      - adi,ad7192
      - adi,ad7193
      - adi,ad7195

  reg:
    maxItems: 1

  spi-cpol: true

  spi-cpha: true

  clocks:
    maxItems: 1
    description: phandle to the master clock (mclk)

  clock-names:
    items:
      - const: mclk

  interrupts:
    maxItems: 1

  dvdd-supply:
    description: DVdd voltage supply

  avdd-supply:
    description: AVdd voltage supply

  adi,rejection-60-Hz-enable:
    description: |
      This bit enables a notch at 60 Hz when the first notch of the sinc
      filter is at 50 Hz. When REJ60 is set, a filter notch is placed at
      60 Hz when the sinc filter first notch is at 50 Hz. This allows
      simultaneous 50 Hz/ 60 Hz rejection.
    type: boolean

  adi,refin2-pins-enable:
    description: |
      External reference applied between the P1/REFIN2(+) and P0/REFIN2(−) pins.
    type: boolean

  adi,buffer-enable:
    description: |
      Enables the buffer on the analog inputs. If cleared, the analog inputs
      are unbuffered, lowering the power consumption of the device. If this
      bit is set, the analog inputs are buffered, allowing the user to place
      source impedances on the front end without contributing gain errors to
      the system.
    type: boolean

  adi,burnout-currents-enable:
    description: |
      When this bit is set to 1, the 500 nA current sources in the signal
      path are enabled. When BURN = 0, the burnout currents are disabled.
      The burnout currents can be enabled only when the buffer is active
      and when chop is disabled.
    type: boolean

  bipolar:
    description: see Documentation/devicetree/bindings/iio/adc/adc.yaml
    type: boolean

required:
  - compatible
  - reg
  - clocks
  - clock-names
  - interrupts
  - dvdd-supply
  - avdd-supply
  - spi-cpol
  - spi-cpha

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

unevaluatedProperties: false

examples:
  - |
    spi0 {
      #address-cells = <1>;
      #size-cells = <0>;

      adc@0 {
        compatible = "adi,ad7192";
        reg = <0>;
        spi-max-frequency = <1000000>;
        spi-cpol;
        spi-cpha;
        clocks = <&ad7192_mclk>;
        clock-names = "mclk";
        interrupts = <25 0x2>;
        interrupt-parent = <&gpio>;
        dvdd-supply = <&dvdd>;
        avdd-supply = <&avdd>;

        adi,refin2-pins-enable;
        adi,rejection-60-Hz-enable;
        adi,buffer-enable;
        adi,burnout-currents-enable;
        };
    };
