# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/fsl,imx7d-adc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Freescale ADC found on the imx7d SoC

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    const: fsl,imx7d-adc

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks:
    maxItems: 1

  clock-names:
    const: adc

  vref-supply: true

  "#io-channel-cells":
    const: 1

required:
  - compatible
  - reg
  - interrupts
  - clocks
  - clock-names
  - vref-supply
  - "#io-channel-cells"

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    #include <dt-bindings/clock/imx7d-clock.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    soc {
        #address-cells = <1>;
        #size-cells = <1>;
        adc@30610000 {
            compatible = "fsl,imx7d-adc";
            reg = <0x30610000 0x10000>;
            interrupts = <GIC_SPI 98 IRQ_TYPE_LEVEL_HIGH>;
            clocks = <&clks IMX7D_ADC_ROOT_CLK>;
            clock-names = "adc";
            vref-supply = <&reg_vcc_3v3_mcu>;
            #io-channel-cells = <1>;
        };
    };
...
