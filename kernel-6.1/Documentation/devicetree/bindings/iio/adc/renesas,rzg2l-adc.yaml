# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/renesas,rzg2l-adc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Renesas RZ/G2L ADC

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description: |
  A/D Converter block is a successive approximation analog-to-digital converter
  with a 12-bit accuracy. Up to eight analog input channels can be selected.
  Conversions can be performed in single or repeat mode. Result of the ADC is
  stored in a 32-bit data register corresponding to each channel.

properties:
  compatible:
    items:
      - enum:
          - renesas,r9a07g043-adc   # RZ/G2UL
          - renesas,r9a07g044-adc   # RZ/G2L
          - renesas,r9a07g054-adc   # RZ/V2L
      - const: renesas,rzg2l-adc

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks:
    items:
      - description: converter clock
      - description: peripheral clock

  clock-names:
    items:
      - const: adclk
      - const: pclk

  power-domains:
    maxItems: 1

  resets:
    maxItems: 2

  reset-names:
    items:
      - const: presetn
      - const: adrst-n

  '#address-cells':
    const: 1

  '#size-cells':
    const: 0

required:
  - compatible
  - reg
  - interrupts
  - clocks
  - clock-names
  - power-domains
  - resets
  - reset-names

patternProperties:
  "^channel@[0-7]$":
    $ref: "adc.yaml"
    type: object
    description: |
      Represents the external channels which are connected to the ADC.

    properties:
      reg:
        description: |
          The channel number.

    required:
      - reg

    additionalProperties: false

allOf:
  - if:
      properties:
        compatible:
          contains:
            const: renesas,r9a07g043-adc
    then:
      patternProperties:
        "^channel@[2-7]$": false
        "^channel@[0-1]$":
          properties:
            reg:
              minimum: 0
              maximum: 1
    else:
      patternProperties:
        "^channel@[0-7]$":
          properties:
            reg:
              minimum: 0
              maximum: 7

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/r9a07g044-cpg.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>

    adc: adc@10059000 {
      compatible = "renesas,r9a07g044-adc", "renesas,rzg2l-adc";
      reg = <0x10059000 0x400>;
      interrupts = <GIC_SPI 347 IRQ_TYPE_EDGE_RISING>;
      clocks = <&cpg CPG_MOD R9A07G044_ADC_ADCLK>,
               <&cpg CPG_MOD R9A07G044_ADC_PCLK>;
      clock-names = "adclk", "pclk";
      power-domains = <&cpg>;
      resets = <&cpg R9A07G044_ADC_PRESETN>,
               <&cpg R9A07G044_ADC_ADRST_N>;
      reset-names = "presetn", "adrst-n";

      #address-cells = <1>;
      #size-cells = <0>;

      channel@0 {
        reg = <0>;
      };
      channel@1 {
        reg = <1>;
      };
      channel@2 {
        reg = <2>;
      };
      channel@3 {
        reg = <3>;
      };
      channel@4 {
        reg = <4>;
      };
      channel@5 {
        reg = <5>;
      };
      channel@6 {
        reg = <6>;
      };
      channel@7 {
        reg = <7>;
      };
    };
