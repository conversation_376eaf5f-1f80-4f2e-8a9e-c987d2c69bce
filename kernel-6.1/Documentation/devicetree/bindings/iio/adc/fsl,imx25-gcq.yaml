# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/fsl,imx25-gcq.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Freescale ADC GCQ device

description:
  This is a generic conversion queue device that can convert any of the
  analog inputs using the ADC unit of the i.MX25.

maintainers:
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    const: fsl,imx25-gcq

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  vref-ext-supply:
    description:
      The regulator supplying the ADC reference voltage.
      Required when at least one subnode uses the this reference.

  vref-xp-supply:
    description:
      The regulator supplying the ADC reference voltage on pin XP.
      Required when at least one subnode uses this reference.

  vref-yp-supply:
    description:
      The regulator supplying the ADC reference voltage on pin YP.
      Required when at least one subnode uses this reference.

  "#io-channel-cells":
    const: 1

  "#address-cells":
    const: 1

  "#size-cells":
    const: 0

required:
  - compatible
  - reg
  - interrupts
  - "#address-cells"
  - "#size-cells"

patternProperties:
  "[a-z][a-z0-9]+@[0-9a-f]+$":
    type: object
    description:
      Child nodes used to define the reference voltages used for each channel

    properties:
      reg:
        description: |
          Number of the analog input.
          0: xp
          1: yp
          2: xn
          3: yn
          4: wiper
          5: inaux0
          6: inaux1
          7: inaux2
        items:
          - minimum: 0
            maximum: 7

      fsl,adc-refp:
        $ref: /schemas/types.yaml#/definitions/uint32
        description: |
          Specifies the positive reference input as defined in
          <dt-bindings/iio/adc/fsl-imx25-gcq.h>
          0: YP voltage reference
          1: XP voltage reference
          2: External voltage reference
          3: Internal voltage reference (default)
        minimum: 0
        maximum: 3

      fsl,adc-refn:
        $ref: /schemas/types.yaml#/definitions/uint32
        description: |
          Specifies the negative reference input as defined in
          <dt-bindings/iio/adc/fsl-imx25-gcq.h>
          0: XN ground reference
          1: YN ground reference
          2: Internal ground reference
          3: External ground reference (default)
        minimum: 0
        maximum: 3

    required:
      - reg

    additionalProperties: false

additionalProperties: false

examples:
  - |
    #include <dt-bindings/iio/adc/fsl-imx25-gcq.h>
    soc {
        #address-cells = <1>;
        #size-cells = <1>;
        adc@50030800 {
            compatible = "fsl,imx25-gcq";
            reg = <0x50030800 0x60>;
            interrupt-parent = <&tscadc>;
            interrupts = <1>;
            #address-cells = <1>;
            #size-cells = <0>;

            inaux@5 {
                reg = <5>;
                fsl,adc-refp = <MX25_ADC_REFP_INT>;
                fsl,adc-refn = <MX25_ADC_REFN_NGND>;
            };
        };
    };
...
