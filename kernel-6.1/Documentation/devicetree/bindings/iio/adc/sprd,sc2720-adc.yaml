# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/sprd,sc2720-adc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Spreadtrum SC27XX series PMICs ADC binding

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description:
  Supports the ADC found on these PMICs.

properties:
  compatible:
    enum:
      - sprd,sc2720-adc
      - sprd,sc2721-adc
      - sprd,sc2723-adc
      - sprd,sc2730-adc
      - sprd,sc2731-adc
      - sprd,ump9620-adc

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  "#io-channel-cells":
    const: 1

  hwlocks:
    maxItems: 1

  nvmem-cells: true

  nvmem-cell-names: true

allOf:
  - if:
      not:
        properties:
          compatible:
            contains:
              enum:
                - sprd,ump9620-adc
    then:
      properties:
        nvmem-cells:
          maxItems: 2
        nvmem-cell-names:
          items:
            - const: big_scale_calib
            - const: small_scale_calib

    else:
      properties:
        nvmem-cells:
          maxItems: 6
        nvmem-cell-names:
          items:
            - const: big_scale_calib1
            - const: big_scale_calib2
            - const: small_scale_calib1
            - const: small_scale_calib2
            - const: vbat_det_cal1
            - const: vbat_det_cal2

required:
  - compatible
  - reg
  - interrupts
  - "#io-channel-cells"
  - hwlocks
  - nvmem-cells
  - nvmem-cell-names

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    pmic {
        #address-cells = <1>;
        #size-cells = <0>;
        adc@480 {
            compatible = "sprd,sc2731-adc";
            reg = <0x480>;
            interrupt-parent = <&sc2731_pmic>;
            interrupts = <0 IRQ_TYPE_LEVEL_HIGH>;
            #io-channel-cells = <1>;
            hwlocks = <&hwlock 4>;
            nvmem-cells = <&adc_big_scale>, <&adc_small_scale>;
            nvmem-cell-names = "big_scale_calib", "small_scale_calib";
        };
    };

  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    pmic {
        #address-cells = <1>;
        #size-cells = <0>;
        adc@504 {
            compatible = "sprd,ump9620-adc";
            reg = <0x504>;
            interrupt-parent = <&ump9620_pmic>;
            interrupts = <0 IRQ_TYPE_LEVEL_HIGH>;
            #io-channel-cells = <1>;
            hwlocks = <&hwlock 4>;
            nvmem-cells = <&adc_bcal1>, <&adc_bcal2>,
                          <&adc_scal1>, <&adc_scal2>,
                          <&vbat_det_cal1>, <&vbat_det_cal2>;
            nvmem-cell-names = "big_scale_calib1", "big_scale_calib2",
                               "small_scale_calib1", "small_scale_calib2",
                               "vbat_det_cal1", "vbat_det_cal2";
        };
    };
...
