# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/brcm,iproc-static-adc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Broadcom's IPROC Static ADC controller

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description: |
  Broadcom iProc ADC controller has 8 10bit channels

properties:
  compatible:
    const: brcm,iproc-static-adc

  adc-syscon:
    $ref: /schemas/types.yaml#/definitions/phandle
    description:
      syscon node defining physical base address of the controller and length
      of memory mapped region.

  "#io-channel-cells":
    const: 1

  clocks:
    maxItems: 1

  clock-names:
    const: tsc_clk

  interrupts:
    maxItems: 1

additionalProperties: false

required:
  - compatible
  - adc-syscon
  - "#io-channel-cells"
  - clocks
  - clock-names
  - interrupts

examples:
  - |
    #include <dt-bindings/clock/bcm-cygnus.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/interrupt-controller/irq.h>
    soc {
        #address-cells = <1>;
        #size-cells = <1>;

        adc {
            compatible = "brcm,iproc-static-adc";
            adc-syscon = <&ts_adc_syscon>;
            #io-channel-cells = <1>;
            clocks = <&asiu_clks BCM_CYGNUS_ASIU_ADC_CLK>;
            clock-names = "tsc_clk";
            interrupts = <GIC_SPI 164 IRQ_TYPE_LEVEL_HIGH>;
        };
    };
...
