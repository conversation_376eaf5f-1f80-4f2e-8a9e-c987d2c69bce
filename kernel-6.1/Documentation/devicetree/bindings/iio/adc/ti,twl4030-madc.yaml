# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/ti,twl4030-madc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: MADC subsystem in the TWL4030 power module

maintainers:
  - <PERSON> <<EMAIL>>

description:
  The MADC subsystem in the TWL4030 consists of a 10-bit ADC
  combined with a 16-input analog multiplexer.

properties:
  compatible:
    const: ti,twl4030-madc

  interrupts:
    maxItems: 1

  ti,system-uses-second-madc-irq:
    type: boolean
    description:
      Set if the second madc irq register should be used, which is intended
      to be used  by Co-Processors (e.g. a modem).

  "#io-channel-cells":
    const: 1

required:
  - compatible
  - interrupts
  - "#io-channel-cells"

additionalProperties: false

examples:
  - |
    twl {
        madc {
            compatible = "ti,twl4030-madc";
            interrupts = <3>;
            #io-channel-cells = <1>;
        };
    };
...
