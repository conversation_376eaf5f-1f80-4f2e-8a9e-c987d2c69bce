# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
# Copyright 2019 Analog Devices Inc.
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/adi,ad7298.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Analog Devices AD7298 ADC

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Bindings for the Analog Devices AD7298 ADC device. Datasheet can be
  found here:
    https://www.analog.com/en/products/ad7298.html

properties:
  compatible:
    const: adi,ad7298

  reg:
    maxItems: 1

  vref-supply: true
  vdd-supply: true

required:
  - compatible
  - reg

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

unevaluatedProperties: false

examples:
  - |
    spi {
      #address-cells = <1>;
      #size-cells = <0>;

      adc@0 {
        compatible = "adi,ad7298";
        reg = <0>;
        spi-max-frequency = <5000000>;
        vref-supply = <&adc_vref>;
      };
    };
...
