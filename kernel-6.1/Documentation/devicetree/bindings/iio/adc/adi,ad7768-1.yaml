# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/adi,ad7768-1.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Analog Devices AD7768-1 ADC device driver

maintainers:
  - <PERSON> <michael.henner<PERSON>@analog.com>

description: |
  Datasheet at:
    https://www.analog.com/media/en/technical-documentation/data-sheets/ad7768-1.pdf

properties:
  compatible:
    const: adi,ad7768-1

  reg:
    maxItems: 1

  clocks:
    maxItems: 1

  clock-names:
    const: mclk

  interrupts:
    maxItems: 1

  '#address-cells':
    const: 1

  '#size-cells':
    const: 0

  vref-supply:
    description:
      ADC reference voltage supply

  adi,sync-in-gpios:
    maxItems: 1
    description:
      Enables synchronization of multiple devices that require simultaneous
      sampling. A pulse is always required if the configuration is changed
      in any way, for example if the filter decimation rate changes.
      As the line is active low, it should be marked GPIO_ACTIVE_LOW.

  reset-gpios:
    maxItems: 1

  spi-cpol: true

  spi-cpha: true

  "#io-channel-cells":
    const: 1

required:
  - compatible
  - reg
  - clocks
  - clock-names
  - vref-supply
  - spi-cpol
  - spi-cpha
  - adi,sync-in-gpios

patternProperties:
  "^channel@([0-9]|1[0-5])$":
    type: object
    description: |
      Represents the external channels which are connected to the device.

    properties:
      reg:
        maxItems: 1
        description: |
          The channel number.

      label:
        description: |
          Unique name to identify which channel this is.
    required:
      - reg
    additionalProperties: false

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    #include <dt-bindings/gpio/gpio.h>
    spi {
        #address-cells = <1>;
        #size-cells = <0>;

        adc@0 {
            compatible = "adi,ad7768-1";
            reg = <0>;
            spi-max-frequency = <2000000>;
            spi-cpol;
            spi-cpha;
            vref-supply = <&adc_vref>;
            interrupts = <25 IRQ_TYPE_EDGE_RISING>;
            interrupt-parent = <&gpio>;
            adi,sync-in-gpios = <&gpio 22 GPIO_ACTIVE_LOW>;
            reset-gpios = <&gpio 27 GPIO_ACTIVE_LOW>;
            clocks = <&ad7768_mclk>;
            clock-names = "mclk";

            #address-cells = <1>;
            #size-cells = <0>;

            channel@0 {
                reg = <0>;
                label = "channel_0";
            };
        };
    };
...
