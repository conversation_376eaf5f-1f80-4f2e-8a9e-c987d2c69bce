# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/qcom,pm8018-adc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm's PM8xxx voltage XOADC

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description: |
  The Qualcomm PM8xxx PMICs contain a HK/XO ADC (Housekeeping/Crystal
  oscillator ADC) encompassing PM8018, PM8038, PM8058 and PM8921.

properties:
  compatible:
    enum:
      - qcom,pm8018-adc
      - qcom,pm8038-adc
      - qcom,pm8058-adc
      - qcom,pm8921-adc

  reg:
    maxItems: 1
    description:
      ADC base address in the PMIC, typically 0x197.

  xoadc-ref-supply:
    description:
      The reference voltage may vary with PMIC variant but is typically
      something like 2.2 or 1.8V.

  interrupts:
    maxItems: 1

  "#address-cells":
    const: 2
    description:
      The first cell is the prescaler (on PM8058) or premux (on PM8921)
      with two valid bits  so legal values are 0x00, 0x01 or 0x02.
      The second cell is the main analog mux setting (0x00..0x0f).
      The combination of prescaler/premux and analog mux uniquely addresses
      a hardware channel on all systems.

  "#size-cells":
    const: 0

  "#io-channel-cells":
    const: 2
    description:
      The cells are precaler or premux followed by the analog muxing line.

additionalProperties: false

required:
  - compatible
  - reg
  - "#io-channel-cells"
  - "#address-cells"
  - "#size-cells"
  - adc-channel@c
  - adc-channel@d
  - adc-channel@f

patternProperties:
  "^(adc-channel@)[0-9a-f]$":
    type: object
    description: |
      ADC channel specific configuration.
      Note that channels c, d and f must be present for calibration.
      These three nodes are used for absolute and ratiometric calibration
      and only need to have these reg values: they are by hardware definition
      1:1 ratio converters that sample 625, 1250 and 0 milliV and create
      an interpolation calibration for all other ADCs.

    properties:
      reg:
        maxItems: 1

      qcom,decimation:
        $ref: /schemas/types.yaml#/definitions/uint32
        description: |
          This parameter is used to decrease the ADC sampling rate.
          Quicker measurements can be made by reducing the decimation ratio.
          Valid values are 512, 1024, 2048, 4096.
          If the property is not found, a default value of 512 will be used.

      qcom,ratiometric:
        $ref: /schemas/types.yaml#/definitions/uint32
        description: |
          Channel calibration type. If this property is specified
          VADC will use a special voltage references for channel
          calibration. The available references are specified in the
          as a u32 value setting (see below) and it is compulsory
          to also specify this reference if ratiometric calibration
          is selected.

          If the property is not found, the channel will be
          calibrated with the 0.625V and 1.25V reference channels, also
          known as an absolute calibration.

          The reference voltage pairs when using ratiometric calibration:
          0 = XO_IN/XOADC_GND
          1 = PMIC_IN/XOADC_GND
          2 = PMIC_IN/BMS_CSP
          3 (invalid)
          4 = XOADC_GND/XOADC_GND
          5 = XOADC_VREF/XOADC_GND

    additionalProperties: false

    required:
      - reg

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    pmic {
        #address-cells = <1>;
        #size-cells = <0>;

        adc@197 {
            compatible = "qcom,pm8058-adc";
            reg = <0x197>;
            interrupts-extended = <&pm8058 76 IRQ_TYPE_EDGE_RISING>;
            #address-cells = <2>;
            #size-cells = <0>;
            #io-channel-cells = <2>;

            vcoin: adc-channel@0 {
                reg = <0x00 0x00>;
            };
            vbat: adc-channel@1 {
                reg = <0x00 0x01>;
            };
            dcin: adc-channel@2 {
                reg = <0x00 0x02>;
            };
            ichg: adc-channel@3 {
                reg = <0x00 0x03>;
            };
            vph_pwr: adc-channel@4 {
                reg = <0x00 0x04>;
            };
            usb_vbus: adc-channel@a {
                reg = <0x00 0x0a>;
            };
            die_temp: adc-channel@b {
                reg = <0x00 0x0b>;
            };
            ref_625mv: adc-channel@c {
                reg = <0x00 0x0c>;
            };
            ref_1250mv: adc-channel@d {
                reg = <0x00 0x0d>;
            };
            ref_325mv: adc-channel@e {
                reg = <0x00 0x0e>;
            };
            ref_muxoff: adc-channel@f {
                reg = <0x00 0x0f>;
           };
        };
    };
...
