# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/amlogic,meson-saradc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Amlogic Meson SAR (Successive Approximation Register) A/D converter

maintainers:
  - <PERSON> <<EMAIL>>

description:
  Binding covers a range of ADCs found on Amlogic Meson SoCs.

properties:
  compatible:
    oneOf:
      - const: amlogic,meson-saradc
      - items:
          - enum:
              - amlogic,meson8-saradc
              - amlogic,meson8b-saradc
              - amlogic,meson8m2-saradc
              - amlogic,meson-gxbb-saradc
              - amlogic,meson-gxl-saradc
              - amlogic,meson-gxm-saradc
              - amlogic,meson-axg-saradc
              - amlogic,meson-g12a-saradc
          - const: amlogic,meson-saradc

  reg:
    maxItems: 1

  interrupts:
    description: Interrupt indicates end of sampling.
    maxItems: 1

  clocks:
    minItems: 2
    maxItems: 4

  clock-names:
    minItems: 2
    items:
      - const: clkin
      - const: core
      - const: adc_clk
      - const: adc_sel

  vref-supply: true

  "#io-channel-cells":
    const: 1

  amlogic,hhi-sysctrl:
    $ref: /schemas/types.yaml#/definitions/phandle
    description:
      Syscon which contains the 5th bit of the TSC (temperature sensor
      coefficient) on Meson8b and Meson8m2 (which used to calibrate the
      temperature sensor)

  nvmem-cells:
    description: phandle to the temperature_calib eFuse cells
    maxItems: 1

  nvmem-cell-names:
    const: temperature_calib

allOf:
  - if:
      properties:
        compatible:
          contains:
            enum:
              - amlogic,meson8-saradc
              - amlogic,meson8b-saradc
              - amlogic,meson8m2-saradc
    then:
      properties:
        clocks:
          maxItems: 2
        clock-names:
          maxItems: 2
    else:
      properties:
        nvmem-cells: false
        mvmem-cel-names: false
        clocks:
          minItems: 4
        clock-names:
          minItems: 4

  - if:
      properties:
        compatible:
          contains:
            enum:
              - amlogic,meson8b-saradc
              - amlogic,meson8m2-saradc
    then:
      properties:
        amlogic,hhi-sysctrl: true
    else:
      properties:
        amlogic,hhi-sysctrl: false

required:
  - compatible
  - reg
  - interrupts
  - clocks
  - clock-names
  - "#io-channel-cells"

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    #include <dt-bindings/clock/gxbb-clkc.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    soc {
        #address-cells = <2>;
        #size-cells = <2>;
        adc@8680 {
            compatible = "amlogic,meson-gxl-saradc", "amlogic,meson-saradc";
            #io-channel-cells = <1>;
            reg = <0x0 0x8680 0x0 0x34>;
            interrupts = <GIC_SPI 73 IRQ_TYPE_EDGE_RISING>;
            clocks = <&xtal>,
                <&clkc CLKID_SAR_ADC>,
                <&clkc CLKID_SAR_ADC_CLK>,
                <&clkc CLKID_SAR_ADC_SEL>;
            clock-names = "clkin", "core", "adc_clk", "adc_sel";
        };
        adc@9680 {
            compatible = "amlogic,meson8b-saradc", "amlogic,meson-saradc";
            #io-channel-cells = <1>;
            reg = <0x0 0x9680 0x0 0x34>;
            interrupts = <GIC_SPI 73 IRQ_TYPE_EDGE_RISING>;
            clocks = <&xtal>, <&clkc CLKID_SAR_ADC>;
            clock-names = "clkin", "core";
            nvmem-cells = <&tsens_caldata>;
            nvmem-cell-names = "temperature_calib";
            amlogic,hhi-sysctrl = <&hhi>;
        };
    };
...
