# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/adi,ad7780.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Analog Devices AD7170/AD7171/AD7780/AD7781 analog to digital converters

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  The ad7780 is a sigma-delta analog to digital converter. This driver provides
  reading voltage values and status bits from both the ad778x and ad717x series.
  Its interface also allows writing on the FILTER and GAIN GPIO pins on the
  ad778x.

  Specifications on the converters can be found at:
    AD7170:
      https://www.analog.com/media/en/technical-documentation/data-sheets/AD7170.pdf
    AD7171:
      https://www.analog.com/media/en/technical-documentation/data-sheets/AD7171.pdf
    AD7780:
      https://www.analog.com/media/en/technical-documentation/data-sheets/ad7780.pdf
    AD7781:
      https://www.analog.com/media/en/technical-documentation/data-sheets/AD7781.pdf

properties:
  compatible:
    enum:
      - adi,ad7170
      - adi,ad7171
      - adi,ad7780
      - adi,ad7781

  reg:
    maxItems: 1

  avdd-supply:
    description:
      The regulator supply for the ADC reference voltage.

  powerdown-gpios:
    description:
      Must be the device tree identifier of the PDRST pin. If
      specified, it will be asserted during driver probe. As the
      line is active high, it should be marked GPIO_ACTIVE_HIGH.
    maxItems: 1

  adi,gain-gpios:
    description:
      Must be the device tree identifier of the GAIN pin. Only for
      the ad778x chips. If specified, it will be asserted during
      driver probe. As the line is active low, it should be marked
      GPIO_ACTIVE_LOW.
    maxItems: 1

  adi,filter-gpios:
    description:
      Must be the device tree identifier of the FILTER pin. Only
      for the ad778x chips. If specified, it will be asserted
      during driver probe. As the line is active low, it should be
      marked GPIO_ACTIVE_LOW.
    maxItems: 1

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>
    spi0 {
        #address-cells = <1>;
        #size-cells = <0>;

        adc@0 {
            compatible = "adi,ad7780";
            reg = <0>;

            avdd-supply      = <&vdd_supply>;
            powerdown-gpios  = <&gpio0 12 GPIO_ACTIVE_HIGH>;
            adi,gain-gpios   = <&gpio1  5 GPIO_ACTIVE_LOW>;
            adi,filter-gpios = <&gpio2 15 GPIO_ACTIVE_LOW>;
        };
    };
