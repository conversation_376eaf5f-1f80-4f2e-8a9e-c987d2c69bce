# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/nxp,lpc1850-adc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: NXP LPC1850 ADC bindings

maintainers:
  - <PERSON> <<EMAIL>>

description:
  Supports the ADC found on the LPC1850 SoC.

properties:
  compatible:
    const: nxp,lpc1850-adc

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks:
    maxItems: 1

  vref-supply: true

  resets:
    maxItems: 1

  "#io-channel-cells":
    const: 1

required:
  - compatible
  - reg
  - interrupts
  - clocks
  - vref-supply
  - resets

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/lpc18xx-ccu.h>
    soc {
        #address-cells = <1>;
        #size-cells = <1>;
        adc@400e3000 {
            compatible = "nxp,lpc1850-adc";
            reg = <0x400e3000 0x1000>;
            interrupts = <17>;
            clocks = <&ccu1 CLK_APB3_ADC0>;
            vref-supply = <&reg_vdda>;
            resets = <&rgu 40>;
         };
    };
...
