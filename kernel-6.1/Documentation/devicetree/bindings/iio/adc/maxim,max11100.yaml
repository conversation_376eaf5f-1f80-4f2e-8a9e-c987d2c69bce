# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/maxim,max11100.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Maxim MAX11100 ADC

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description: |
    Single channel 16 bit ADC with SPI interface.

properties:
  compatible:
    const: maxim,max11100

  reg:
    maxItems: 1

  vref-supply:
    description: External reference, needed to establish input scaling.

  spi-max-frequency:
    minimum: 100000
    maximum: 4800000

required:
  - compatible
  - reg
  - vref-supply

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

unevaluatedProperties: false

examples:
  - |
    spi {
        #address-cells = <1>;
        #size-cells = <0>;

        adc@0 {
            compatible = "maxim,max11100";
            reg = <0>;
            vref-supply = <&adc_vref>;
            spi-max-frequency = <240000>;
        };
    };
...
