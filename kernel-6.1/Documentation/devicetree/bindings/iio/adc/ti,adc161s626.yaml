# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/ti,adc161s626.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Texas Instruments ADC141S626 and ADC161S626 ADCs

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Single channel 14/16bit differential ADCs

properties:
  compatible:
    enum:
      - ti,adc141s626
      - ti,adc161s626

  reg:
    maxItems: 1

  vdda-supply: true

  "#io-channel-cells":
    const: 1

required:
  - compatible
  - reg

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

unevaluatedProperties: false

examples:
  - |
    spi {
        #address-cells = <1>;
        #size-cells = <0>;

        adc@0 {
            compatible = "ti,adc161s626";
            vdda-supply = <&vdda_fixed>;
            reg = <0>;
            spi-max-frequency = <4300000>;
            #io-channel-cells = <1>;
        };
    };
...
