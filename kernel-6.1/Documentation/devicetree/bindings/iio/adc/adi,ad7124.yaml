# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
# Copyright 2019 Analog Devices Inc.
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/adi,ad7124.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Analog Devices AD7124 ADC device driver

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Bindings for the Analog Devices AD7124 ADC device. Datasheet can be
  found here:
    https://www.analog.com/media/en/technical-documentation/data-sheets/AD7124-8.pdf

properties:
  compatible:
    enum:
      - adi,ad7124-4
      - adi,ad7124-8

  reg:
    description: SPI chip select number for the device
    maxItems: 1

  clocks:
    maxItems: 1
    description: phandle to the master clock (mclk)

  clock-names:
    items:
      - const: mclk

  interrupts:
    description: IRQ line for the ADC
    maxItems: 1

  '#address-cells':
    const: 1

  '#size-cells':
    const: 0

  refin1-supply:
    description: refin1 supply can be used as reference for conversion.

  refin2-supply:
    description: refin2 supply can be used as reference for conversion.

  avdd-supply:
    description: avdd supply can be used as reference for conversion.

required:
  - compatible
  - reg
  - clocks
  - clock-names
  - interrupts

patternProperties:
  "^channel@([0-9]|1[0-5])$":
    $ref: "adc.yaml"
    type: object
    description: |
      Represents the external channels which are connected to the ADC.

    properties:
      reg:
        description: |
          The channel number. It can have up to 8 channels on ad7124-4
          and 16 channels on ad7124-8, numbered from 0 to 15.
        items:
          minimum: 0
          maximum: 15

      adi,reference-select:
        description: |
          Select the reference source to use when converting on
          the specific channel. Valid values are:
          0: REFIN1(+)/REFIN1(−).
          1: REFIN2(+)/REFIN2(−).
          3: AVDD
          If this field is left empty, internal reference is selected.
        $ref: /schemas/types.yaml#/definitions/uint32
        enum: [0, 1, 3]

      diff-channels: true

      bipolar: true

      adi,buffered-positive:
        description: Enable buffered mode for positive input.
        type: boolean

      adi,buffered-negative:
        description: Enable buffered mode for negative input.
        type: boolean

    required:
      - reg
      - diff-channels

    additionalProperties: false

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

unevaluatedProperties: false

examples:
  - |
    spi {
      #address-cells = <1>;
      #size-cells = <0>;

      adc@0 {
        compatible = "adi,ad7124-4";
        reg = <0>;
        spi-max-frequency = <5000000>;
        interrupts = <25 2>;
        interrupt-parent = <&gpio>;
        refin1-supply = <&adc_vref>;
        clocks = <&ad7124_mclk>;
        clock-names = "mclk";

        #address-cells = <1>;
        #size-cells = <0>;

        channel@0 {
          reg = <0>;
          diff-channels = <0 1>;
          adi,reference-select = <0>;
          adi,buffered-positive;
        };

        channel@1 {
          reg = <1>;
          bipolar;
          diff-channels = <2 3>;
          adi,reference-select = <0>;
          adi,buffered-positive;
          adi,buffered-negative;
        };

        channel@2 {
          reg = <2>;
          diff-channels = <4 5>;
        };

        channel@3 {
          reg = <3>;
          diff-channels = <6 7>;
        };
      };
    };
