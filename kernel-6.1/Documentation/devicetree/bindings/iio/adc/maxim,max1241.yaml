# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
# Copyright 2020 <PERSON><PERSON><PERSON>
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/maxim,max1241.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Maxim MAX1241 12-bit, single-channel analog to digital converter

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description: |
  Bindings for the max1241 12-bit, single-channel ADC device. Datasheet
  can be found at:
    https://datasheets.maximintegrated.com/en/ds/MAX1240-MAX1241.pdf

properties:
  compatible:
    enum:
      - maxim,max1241

  reg:
    maxItems: 1

  vdd-supply:
    description:
      Device tree identifier of the regulator that powers the ADC.

  vref-supply:
    description:
      Device tree identifier of the regulator that provides the external
      reference voltage.

  shutdown-gpios:
    description:
      GPIO spec for the GPIO pin connected to the ADC's /SHDN pin. If
      specified, the /SHDN pin will be asserted between conversions,
      thus enabling power-down mode.
    maxItems: 1

required:
  - compatible
  - reg
  - vdd-supply
  - vref-supply

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>
    spi {
      #address-cells = <1>;
      #size-cells = <0>;

        adc@0 {
            compatible = "maxim,max1241";
            reg = <0>;
            vdd-supply = <&adc_vdd>;
            vref-supply = <&adc_vref>;
            spi-max-frequency = <1000000>;
            shutdown-gpios = <&gpio 26 1>;
        };
    };
