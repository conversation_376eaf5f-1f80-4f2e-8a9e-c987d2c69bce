# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/ti,adc12138.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Texas Instruments ADC12138 and similar self-calibrating ADCs

maintainers:
  - <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>

description: |
  13 bit ADCs with 1, 2 or 8 inputs and self calibrating circuitry to
  correct for linearity, zero and full scale errors.

properties:
  compatible:
    enum:
      - ti,adc12130
      - ti,adc12132
      - ti,adc12138

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1
    description: End of Conversion (EOC) interrupt

  clocks:
    maxItems: 1
    description: Conversion clock input.

  vref-p-supply:
    description: The regulator supply for positive analog voltage reference

  vref-n-supply:
    description: |
      The regulator supply for negative analog voltage reference
      (Note that this must not go below GND or exceed vref-p)
      If not specified, this is assumed to be analog ground.

  ti,acquisition-time:
    $ref: /schemas/types.yaml#/definitions/uint32
    enum: [ 6, 10, 18, 34 ]
    description: |
      The number of conversion clock periods for the S/H's acquisition time.
      For high source impedances, this value can be increased to 18 or 34.
      For less ADC accuracy and/or slower CCLK frequencies this value may be
      decreased to 6.  See section 6.0 INPUT SOURCE RESISTANCE in the
      datasheet for details.

  "#io-channel-cells":
    const: 1

required:
  - compatible
  - reg
  - interrupts
  - clocks
  - vref-p-supply

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    spi {
        #address-cells = <1>;
        #size-cells = <0>;

        adc@0 {
            compatible = "ti,adc12138";
            reg = <0>;
            interrupts = <28 IRQ_TYPE_EDGE_RISING>;
            interrupt-parent = <&gpio1>;
            clocks = <&cclk>;
            vref-p-supply = <&ldo4_reg>;
            spi-max-frequency = <5000000>;
            ti,acquisition-time = <6>;
            #io-channel-cells = <1>;
        };
    };
...
