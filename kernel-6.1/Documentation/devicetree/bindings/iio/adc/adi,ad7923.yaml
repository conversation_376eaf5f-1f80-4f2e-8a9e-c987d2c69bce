# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/adi,ad7923.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Analog Devices AD7923 and similars with 4 and 8 Channel ADCs.

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Analog Devices AD7904, AD7914, AD7923, AD7924 4 Channel ADCs, and AD7908,
   AD7918, AD7928 8 Channels ADCs.

  Specifications about the part can be found at:
    https://www.analog.com/media/en/technical-documentation/data-sheets/AD7923.pdf
    https://www.analog.com/media/en/technical-documentation/data-sheets/AD7904_7914_7924.pdf
    https://www.analog.com/media/en/technical-documentation/data-sheets/AD7908_7918_7928.pdf

properties:
  compatible:
    enum:
      - adi,ad7904
      - adi,ad7914
      - adi,ad7923
      - adi,ad7924
      - adi,ad7908
      - adi,ad7918
      - adi,ad7928

  reg:
    maxItems: 1

  refin-supply:
    description: |
      The regulator supply for ADC reference voltage.

  adi,range-double:
    description: Sets the analog input range from 0 to 2xVREF.
    type: boolean

  '#address-cells':
    const: 1

  '#size-cells':
    const: 0

required:
  - compatible
  - reg

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

unevaluatedProperties: false

examples:
  - |
    spi {
      #address-cells = <1>;
      #size-cells = <0>;

      ad7928: adc@0 {
        compatible = "adi,ad7928";
        reg = <0>;
        spi-max-frequency = <25000000>;
        refin-supply = <&adc_vref>;

        #address-cells = <1>;
        #size-cells = <0>;
      };
    };
