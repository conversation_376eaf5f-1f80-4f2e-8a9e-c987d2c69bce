# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
# Copyright 2019-2020 <PERSON><PERSON>
%YAML 1.2
---
$id: "http://devicetree.org/schemas/iio/adc/ingenic,adc.yaml#"
$schema: "http://devicetree.org/meta-schemas/core.yaml#"

title: Ingenic JZ47xx ADC controller IIO bindings

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description: >
  Industrial I/O subsystem bindings for ADC controller found in
  Ingenic JZ47xx SoCs.

  ADC clients must use the format described in
  https://github.com/devicetree-org/dt-schema/blob/master/schemas/iio/iio-consumer.yaml,
  giving a phandle and IIO specifier pair ("io-channels") to the ADC controller.

properties:
  compatible:
    enum:
      - ingenic,jz4725b-adc
      - ingenic,jz4740-adc
      - ingenic,jz4760-adc
      - ingenic,jz4760b-adc
      - ingenic,jz4770-adc

  '#io-channel-cells':
    const: 1
    description:
      Must be set to <1> to indicate channels are selected by index.

  reg:
    maxItems: 1

  clocks:
    maxItems: 1

  clock-names:
    items:
      - const: adc

  interrupts:
    maxItems: 1

  ingenic,use-internal-divider:
    description:
      If present, battery voltage is read from the VBAT_IR pin, which has an
      internal 1/4 divider. If absent, it is read through the VBAT_ER pin,
      which does not have such a divider.
    type: boolean

if:
  not:
    properties:
      compatible:
        contains:
          const: ingenic,jz4760b-adc
then:
  properties:
    ingenic,use-internal-divider: false

required:
  - compatible
  - '#io-channel-cells'
  - reg
  - clocks
  - clock-names
  - interrupts

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/ingenic,jz4740-cgu.h>
    #include <dt-bindings/iio/adc/ingenic,adc.h>

    adc@10070000 {
            compatible = "ingenic,jz4740-adc";
            #io-channel-cells = <1>;

            reg = <0x10070000 0x30>;

            clocks = <&cgu JZ4740_CLK_ADC>;
            clock-names = "adc";

            interrupt-parent = <&intc>;
            interrupts = <18>;
    };
