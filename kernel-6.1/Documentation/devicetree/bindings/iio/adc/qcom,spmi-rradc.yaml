# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/qcom,spmi-rradc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm's SPMI PMIC Round Robin ADC

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  The Qualcomm SPMI Round Robin ADC (RRADC) provides interface to clients to
  read the voltage, current and temperature for supported peripherals such as
  the battery thermistor die temperature, charger temperature, USB and DC input
  voltage / current and battery ID resistor.

properties:
  compatible:
    enum:
      - qcom,pmi8998-rradc
      - qcom,pm660-rradc

  reg:
    maxItems: 1

  qcom,batt-id-delay-ms:
    description: Sets the hardware settling time for the battery ID resistor.
    enum: [0, 1, 4, 12, 20, 40, 60, 80]

  "#io-channel-cells":
    const: 1

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    pmic {
      #address-cells = <1>;
      #size-cells = <0>;

      pmic_rradc: adc@4500 {
          compatible = "qcom,pmi8998-rradc";
          reg = <0x4500>;
          #io-channel-cells  = <1>;
      };
    };
