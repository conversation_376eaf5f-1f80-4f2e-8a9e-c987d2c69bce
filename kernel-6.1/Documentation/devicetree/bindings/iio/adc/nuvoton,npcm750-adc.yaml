# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/nuvoton,npcm750-adc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Nuvoton NPCM BMC Analog to Digital Converter (ADC)

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description:
  The NPCM7XX ADC is a 10-bit converter and NPCM8XX ADC is a 12-bit converter,
  both have eight channel inputs.

properties:
  compatible:
    enum:
      - nuvoton,npcm750-adc
      - nuvoton,npcm845-adc

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1
    description: ADC interrupt, should be set for falling edge.

  resets:
    maxItems: 1

  clocks:
    maxItems: 1
    description: If not provided the defulat ADC sample rate will be used.

  vref-supply:
    description: If not supplied, the internal voltage reference will be used.

  "#io-channel-cells":
    const: 1

required:
  - compatible
  - reg
  - interrupts
  - resets

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/clock/nuvoton,npcm7xx-clock.h>
    #include <dt-bindings/reset/nuvoton,npcm7xx-reset.h>
    soc {
        #address-cells = <1>;
        #size-cells = <1>;
        adc@f000c000 {
            compatible = "nuvoton,npcm750-adc";
            reg = <0xf000c000 0x8>;
            interrupts = <GIC_SPI 0 IRQ_TYPE_LEVEL_HIGH>;
            clocks = <&clk NPCM7XX_CLK_ADC>;
            resets = <&rstc NPCM7XX_RESET_IPSRST1 NPCM7XX_RESET_ADC>;
        };
    };
...
