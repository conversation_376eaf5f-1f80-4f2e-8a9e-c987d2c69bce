# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/adi,ad7291.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: AD7291 8-Channel, I2C, 12-Bit SAR ADC with Temperature Sensor

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Analog Devices AD7291 8-Channel I2C 12-Bit SAR ADC with Temperature Sensor
  https://www.analog.com/media/en/technical-documentation/data-sheets/ad7291.pdf

properties:
  compatible:
    enum:
      - adi,ad7291

  reg:
    maxItems: 1

  vref-supply:
    description: |
      The regulator supply for ADC reference voltage.

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    i2c {
      #address-cells = <1>;
      #size-cells = <0>;

      ad7291: adc@0 {
        compatible = "adi,ad7291";
        reg = <0>;
        vref-supply = <&adc_vref>;
      };
    };
...
