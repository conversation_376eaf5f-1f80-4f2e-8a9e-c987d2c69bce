# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/ti,adc128s052.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Texas Instruments ADC128S052 and similar ADCs

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Family of 12 bit SPI ADCs with 2 to 8 channels with a range of different
  target sample rates.

properties:
  compatible:
    enum:
      - ti,adc122s021
      - ti,adc122s051
      - ti,adc122s101
      - ti,adc124s021
      - ti,adc124s051
      - ti,adc124s101
      - ti,adc128s052

  reg:
    maxItems: 1

  vref-supply: true

  "#io-channel-cells":
    const: 1

required:
  - compatible
  - reg
  - vref-supply

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    spi {
        #address-cells = <1>;
        #size-cells = <0>;

        adc@0 {
            compatible = "ti,adc128s052";
            reg = <0>;
            vref-supply = <&vdd_supply>;
            spi-max-frequency = <1000000>;
            #io-channel-cells = <1>;
        };
    };
...
