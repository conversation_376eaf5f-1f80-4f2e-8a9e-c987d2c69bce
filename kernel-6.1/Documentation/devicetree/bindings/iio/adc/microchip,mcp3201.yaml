# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/microchip,mcp3201.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Microchip mcp3201 and similar ADCs

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description: |
   Family of simple ADCs with a SPI interface.

properties:
  compatible:
    enum:
      - microchip,mcp3001
      - microchip,mcp3002
      - microchip,mcp3004
      - microchip,mcp3008
      - microchip,mcp3201
      - microchip,mcp3202
      - microchip,mcp3204
      - microchip,mcp3208
      - microchip,mcp3301
      - microchip,mcp3550-50
      - microchip,mcp3550-60
      - microchip,mcp3551
      - microchip,mcp3553

  reg:
    maxItems: 1

  spi-cpha: true
  spi-cpol: true

  vref-supply:
    description: External reference.

  "#io-channel-cells":
    const: 1

dependencies:
  spi-cpol: [ spi-cpha ]
  spi-cpha: [ spi-cpol ]

required:
  - compatible
  - reg
  - vref-supply

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

unevaluatedProperties: false

examples:
  - |
    spi {
        #address-cells = <1>;
        #size-cells = <0>;

        adc@0 {
            compatible = "microchip,mcp3002";
            reg = <0>;
            vref-supply = <&vref_reg>;
            spi-cpha;
            spi-cpol;
            #io-channel-cells = <1>;
        };
        adc@1 {
            compatible = "microchip,mcp3002";
            reg = <1>;
            vref-supply = <&vref_reg>;
            spi-max-frequency = <1500000>;
        };
    };
...
