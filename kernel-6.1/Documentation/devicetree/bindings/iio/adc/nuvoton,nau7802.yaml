# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/nuvoton,nau7802.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Nuvoton NAU7802 I2c Analog to Digital Converter (ADC)

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    const: nuvoton,nau7802

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  nuvoton,vldo:
    $ref: /schemas/types.yaml#/definitions/uint32
    description:
      Internal reference voltage in millivolts to be configured.
    minimum: 2400
    maximum: 4500

  "#io-channel-cells":
    const: 1

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    i2c {
        #address-cells = <1>;
        #size-cells = <0>;
        nau7802@2a {
            compatible = "nuvoton,nau7802";
            reg = <0x2a>;
            nuvoton,vldo = <3000>;
        };
    };
...
