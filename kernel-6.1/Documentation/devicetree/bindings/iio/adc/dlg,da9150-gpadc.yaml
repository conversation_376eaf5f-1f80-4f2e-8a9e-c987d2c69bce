# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/dlg,da9150-gpadc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Dialog Semiconductor DA9150 IIO GPADC

maintainers:
  - <PERSON> <<EMAIL>>

description:
  This patch adds support for general purpose ADC within the
  DA9150 Charger & Fuel-Gauge IC.

properties:
  compatible:
    const: dlg,da9150-gpadc

  "#io-channel-cells":
    const: 1

required:
  - compatible
  - "#io-channel-cells"

additionalProperties: false

examples:
  - |
    adc {
        compatible = "dlg,da9150-gpadc";
        #io-channel-cells = <1>;
    };
...
