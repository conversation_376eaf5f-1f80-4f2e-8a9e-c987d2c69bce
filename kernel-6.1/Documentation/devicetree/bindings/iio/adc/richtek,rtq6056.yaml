# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/richtek,rtq6056.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: RTQ6056 Bi-Directional Current and Power Monitor with 16-bit ADC

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description: |
  The RTQ6056 is a high accuracy current-sense monitor with I2C and SMBus
  interface, and the device provides full information for system by reading
  out the loading current and power.

  The device monitors both of the drops across sense resistor and the BUS
  voltage, converts into the current in amperes, and power in watts through
  internal analog-to-digital converter ADC. The programmable calibration,
  adjustable conversion time, and averaging function are also built in for
  more design flexibility.

  Datasheet is available at
  https://www.richtek.com/assets/product_file/RTQ6056/DSQ6056-00.pdf

properties:
  compatible:
    const: richtek,rtq6056

  reg:
    maxItems: 1

  "#io-channel-cells":
    const: 1

  shunt-resistor-micro-ohms:
    description: Shunt IN+/IN- sensing node resistor

required:
  - compatible
  - reg
  - "#io-channel-cells"

additionalProperties: false

examples:
  - |
    i2c {
      #address-cells = <1>;
      #size-cells = <0>;
      adc@40 {
        compatible = "richtek,rtq6056";
        reg = <0x40>;
        #io-channel-cells = <1>;
      };
    };
