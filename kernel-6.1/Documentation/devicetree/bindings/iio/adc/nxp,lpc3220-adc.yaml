# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/nxp,lpc3220-adc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: NXP LPC3220 SoC ADC controller

maintainers:
  - <PERSON> <<EMAIL>>

description:
  This hardware block has been used on several LPC32XX SoCs.

properties:
  compatible:
    const: nxp,lpc3220-adc

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  vref-supply: true

  "#io-channel-cells":
    const: 1

required:
  - compatible
  - reg
  - interrupts

additionalProperties: false

examples:
  - |
    soc {
        #address-cells = <1>;
        #size-cells = <1>;
        adc@40048000 {
            compatible = "nxp,lpc3220-adc";
            reg = <0x40048000 0x1000>;
            interrupt-parent = <&mic>;
            interrupts = <39 0>;
            vref-supply = <&vcc>;
        };
    };
...
