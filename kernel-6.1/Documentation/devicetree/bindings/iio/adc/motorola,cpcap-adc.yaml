# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/motorola,cpcap-adc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Motorola CPCAP PMIC ADC binding

maintainers:
  - <PERSON> <<EMAIL>>

description:
  On Motorola phones like droid 4 there is a custom CPCAP PMIC. This PMIC
  has ADCs that are used for battery charging and USB PHY VBUS and ID pin
  detection.

properties:
  compatible:
    enum:
      - motorola,cpcap-adc
      - motorola,mapphone-cpcap-adc

  interrupts:
    maxItems: 1

  interrupt-names:
    const: adcdone

  "#io-channel-cells":
    const: 1

required:
  - compatible
  - interrupts
  - "#io-channel-cells"

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    pmic {
        #address-cells = <1>;
        #size-cells = <0>;
        adc {
            compatible = "motorola,mapphone-cpcap-adc";
            interrupt-parent = <&cpcap>;
            interrupts = <8 IRQ_TYPE_NONE>;
            interrupt-names = "adcdone";
            #io-channel-cells = <1>;
        };
    };
...
