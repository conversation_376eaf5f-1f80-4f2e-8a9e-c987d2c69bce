# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/ti,ads124s08.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Texas Instruments' ads124s08 and ads124s06 ADC chip

maintainers:
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - ti,ads124s06
      - ti,ads124s08

  reg:
    maxItems: 1

  spi-cpha: true

  reset-gpios:
    maxItems: 1

  "#io-channel-cells":
    const: 1

required:
  - compatible
  - reg

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>
    spi {
        #address-cells = <1>;
        #size-cells = <0>;

        adc@0 {
            compatible = "ti,ads124s08";
            reg = <0>;
            spi-max-frequency = <1000000>;
            spi-cpha;
            reset-gpios = <&gpio1 16 GPIO_ACTIVE_LOW>;
        };
    };
...
