# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/adi,ad7292.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Analog Devices AD7292 10-Bit Monitor and Control System

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description: |
  Analog Devices AD7292 10-Bit Monitor and Control System with ADC, DACs,
  Temperature Sensor, and GPIOs

  Specifications about the part can be found at:
    https://www.analog.com/media/en/technical-documentation/data-sheets/ad7292.pdf

properties:
  compatible:
    enum:
      - adi,ad7292

  reg:
    maxItems: 1

  vref-supply:
    description: |
      The regulator supply for ADC and DAC reference voltage.

  spi-cpha: true

  '#address-cells':
    const: 1

  '#size-cells':
    const: 0

required:
  - compatible
  - reg
  - spi-cpha

patternProperties:
  "^channel@[0-7]$":
    $ref: "adc.yaml"
    type: object
    description: |
      Represents the external channels which are connected to the ADC.

    properties:
      reg:
        description: |
          The channel number. It can have up to 8 channels numbered from 0 to 7.
        items:
          - minimum: 0
            maximum: 7

      diff-channels: true

    required:
      - reg

    additionalProperties: true

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

unevaluatedProperties: false

examples:
  - |
    spi {
      #address-cells = <1>;
      #size-cells = <0>;

      ad7292: adc@0 {
        compatible = "adi,ad7292";
        reg = <0>;
        spi-max-frequency = <25000000>;
        vref-supply = <&adc_vref>;
        spi-cpha;

        #address-cells = <1>;
        #size-cells = <0>;

        channel@0 {
          reg = <0>;
          diff-channels = <0 1>;
        };
        channel@2 {
          reg = <2>;
        };
        channel@3 {
          reg = <3>;
        };
        channel@4 {
          reg = <4>;
        };
        channel@5 {
          reg = <5>;
        };
        channel@6 {
          reg = <6>;
        };
        channel@7 {
          reg = <7>;
        };
      };
    };
