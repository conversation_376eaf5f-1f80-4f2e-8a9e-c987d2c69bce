# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/marvell,berlin2-adc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Berlin 2 Analog to Digital Converter (ADC)

maintainers:
  - <PERSON> <<EMAIL>>

description:
  The Berlin ADC has 8 channels, with one connected to a temperature sensor.
  It is part of the system controller register set. The ADC node should be a
  sub-node of the system controller node.

properties:
  compatible:
    const: marvell,berlin2-adc

  interrupts:
    minItems: 2
    maxItems: 2

  interrupt-names:
    items:
      - const: adc
      - const: tsen

  "#io-channel-cells":
    const: 1

required:
  - compatible
  - interrupts
  - interrupt-names

additionalProperties: false

examples:
  - |
    sysctrl {
        adc {
            compatible = "marvell,berlin2-adc";
            interrupt-parent = <&sic>;
            interrupts = <12>, <14>;
            interrupt-names = "adc", "tsen";
        };
    };
...
