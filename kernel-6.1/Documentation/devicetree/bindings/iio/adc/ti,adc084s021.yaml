# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/ti,adc084s021.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Texas Instruments ADC084S021 ADC

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description: |
  8 bit ADC with 4 channels

properties:
  compatible:
    const: ti,adc084s021

  reg:
    maxItems: 1

  vref-supply:
    description: External reference, needed to establish input scaling

  spi-cpol: true
  spi-cpha: true

  "#io-channel-cells":
    const: 1

required:
  - compatible
  - reg
  - vref-supply
  - spi-cpol
  - spi-cpha

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

unevaluatedProperties: false

examples:
  - |
    spi {
        #address-cells = <1>;
        #size-cells = <0>;

        adc@0 {
            compatible = "ti,adc084s021";
            reg = <0>;
            vref-supply = <&adc_vref>;
            spi-cpol;
            spi-cpha;
            spi-max-frequency = <16000000>;
            #io-channel-cells = <1>;
        };
    };
...
