# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/filter/adi,admv8818.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: ADMV8818 Digitally Tunable, High-Pass and Low-Pass Filter

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description: |
    Fully monolithic microwave integrated circuit (MMIC) that
    features a digitally selectable frequency of operation.
    The device features four independently controlled high-pass
    filters (HPFs) and four independently controlled low-pass filters
    (LPFs) that span the 2 GHz to 18 GHz frequency range.

    https://www.analog.com/en/products/admv8818.html

properties:
  compatible:
    enum:
      - adi,admv8818

  reg:
    maxItems: 1

  spi-max-frequency:
    maximum: 10000000

  clocks:
    description:
      Definition of the external clock.
    minItems: 1

  clock-names:
    items:
      - const: rf_in

  clock-output-names:
    maxItems: 1

  '#clock-cells':
    const: 0

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    spi {
      #address-cells = <1>;
      #size-cells = <0>;
      admv8818@0 {
        compatible = "adi,admv8818";
        reg = <0>;
        spi-max-frequency = <10000000>;
        clocks = <&admv8818_rfin>;
        clock-names = "rf_in";
      };
    };
...
