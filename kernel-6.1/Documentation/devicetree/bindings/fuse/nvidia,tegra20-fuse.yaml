# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/fuse/nvidia,tegra20-fuse.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: NVIDIA Tegra FUSE block

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    oneOf:
      - enum:
          - nvidia,tegra20-efuse
          - nvidia,tegra30-efuse
          - nvidia,tegra114-efuse
          - nvidia,tegra124-efuse
          - nvidia,tegra210-efuse
          - nvidia,tegra186-efuse
          - nvidia,tegra194-efuse
          - nvidia,tegra234-efuse

      - items:
          - const: nvidia,tegra132-efuse
          - const: nvidia,tegra124-efuse

  reg:
    maxItems: 1

  clocks:
    maxItems: 1

  clock-names:
    items:
      - const: fuse

  resets:
    maxItems: 1

  reset-names:
    items:
      - const: fuse

  operating-points-v2:
    $ref: "/schemas/types.yaml#/definitions/phandle"

  power-domains:
    items:
      - description: phandle to the core power domain

additionalProperties: false

required:
  - compatible
  - reg
  - clocks
  - clock-names

if:
  properties:
    compatible:
      contains:
        enum:
          - nvidia,tegra20-efuse
          - nvidia,tegra30-efuse
          - nvidia,tegra114-efuse
          - nvidia,tegra124-efuse
          - nvidia,tegra132-efuse
          - nvidia,tegra210-efuse
then:
  required:
    - resets
    - reset-names

examples:
  - |
    #include <dt-bindings/clock/tegra20-car.h>

    fuse@7000f800 {
        compatible = "nvidia,tegra20-efuse";
        reg = <0x7000f800 0x400>;
        clocks = <&tegra_car TEGRA20_CLK_FUSE>;
        clock-names = "fuse";
        resets = <&tegra_car 39>;
        reset-names = "fuse";
    };
