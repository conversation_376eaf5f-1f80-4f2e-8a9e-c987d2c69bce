# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/hwinfo/samsung,exynos-chipid.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Samsung Exynos SoC series Chipid driver

maintainers:
  - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - samsung,exynos4210-chipid
      - samsung,exynos850-chipid

  reg:
    maxItems: 1

  samsung,asv-bin:
    description:
      Adaptive Supply Voltage bin selection. This can be used
      to determine the ASV bin of an SoC if respective information
      is missing in the CHIPID registers or in the OTP memory.
    $ref: /schemas/types.yaml#/definitions/uint32
    enum: [0, 1, 2, 3]

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    chipid@10000000 {
        compatible = "samsung,exynos4210-chipid";
        reg = <0x10000000 0x100>;
        samsung,asv-bin = <2>;
    };
