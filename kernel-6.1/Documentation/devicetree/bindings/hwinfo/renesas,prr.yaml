# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/hwinfo/renesas,prr.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Renesas Product Register

maintainers:
  - <PERSON><PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

description: |
  Most Renesas ARM SoCs have a Product Register or Boundary Scan ID
  Register that allows to retrieve SoC product and revision information.
  If present, a device node for this register should be added.

properties:
  compatible:
    enum:
      - renesas,prr
      - renesas,bsid
  reg:
    maxItems: 1

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    prr: chipid@ff000044 {
        compatible = "renesas,prr";
        reg = <0xff000044 4>;
    };
