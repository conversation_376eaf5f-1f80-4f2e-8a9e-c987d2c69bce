Nokia modem client bindings

The Nokia modem HSI client follows the common HSI client binding
and inherits all required properties. The following additional
properties are needed by the Nokia modem HSI client:

Required properties:
- compatible:		Should be one of
      "nokia,n900-modem"
      "nokia,n950-modem"
      "nokia,n9-modem"
- hsi-channel-names:	Should contain the following strings
      "mcsaab-control"
      "speech-control"
      "speech-data"
      "mcsaab-data"
- gpios:		Should provide a GPIO handler for each GPIO listed in
                        gpio-names
- gpio-names:		Should contain the following strings
      "cmt_apeslpx" (for n900, n950, n9)
      "cmt_rst_rq"  (for n900, n950, n9)
      "cmt_en"      (for n900, n950, n9)
      "cmt_rst"     (for n900)
      "cmt_bsi"     (for n900)
- interrupts:		Should be IRQ handle for modem's reset indication

Example:

&ssi_port {
	modem: hsi-client {
		compatible = "nokia,n900-modem";

		pinctrl-names = "default";
		pinctrl-0 = <&modem_pins>;

		hsi-channel-ids = <0>, <1>, <2>, <3>;
		hsi-channel-names = "mcsaab-control",
				    "speech-control",
				    "speech-data",
				    "mcsaab-data";
		hsi-speed-kbps = <55000>;
		hsi-mode = "frame";
		hsi-flow = "synchronized";
		hsi-arb-mode = "round-robin";

		interrupts-extended = <&gpio3 8 IRQ_TYPE_EDGE_FALLING>; /* 72 */

		gpios = <&gpio3  6 GPIO_ACTIVE_HIGH>, /* 70 */
			<&gpio3  9 GPIO_ACTIVE_HIGH>, /* 73 */
			<&gpio3 10 GPIO_ACTIVE_HIGH>, /* 74 */
			<&gpio3 11 GPIO_ACTIVE_HIGH>, /* 75 */
			<&gpio5 29 GPIO_ACTIVE_HIGH>; /* 157 */
		gpio-names = "cmt_apeslpx",
			     "cmt_rst_rq",
			     "cmt_en",
			     "cmt_rst",
			     "cmt_bsi";
	};
};
