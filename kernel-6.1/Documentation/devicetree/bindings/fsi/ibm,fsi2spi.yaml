# SPDX-License-Identifier: (GPL-2.0-or-later)
%YAML 1.2
---
$id: http://devicetree.org/schemas/fsi/ibm,fsi2spi.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: IBM FSI-attached SPI controllers

maintainers:
  - <PERSON> <eaja<PERSON>@linux.ibm.com>

description: |
  This binding describes an FSI CFAM engine called the FSI2SPI. Therefore this
  node will always be a child of an FSI CFAM node; see fsi.txt for details on
  FSI slave and CFAM nodes. This FSI2SPI engine provides access to a number of
  SPI controllers.

properties:
  compatible:
    enum:
      - ibm,fsi2spi

  reg:
    items:
      - description: FSI slave address

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    fsi2spi@1c00 {
        compatible = "ibm,fsi2spi";
        reg = <0x1c00 0x400>;
    };
