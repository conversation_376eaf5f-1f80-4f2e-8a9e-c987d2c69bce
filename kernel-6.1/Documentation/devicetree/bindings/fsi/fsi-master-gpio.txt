Device-tree bindings for gpio-based FSI master driver
-----------------------------------------------------

Required properties:
 - compatible = "fsi-master-gpio";
 - clock-gpios = <gpio-descriptor>;	: <PERSON><PERSON> for FSI clock
 - data-gpios = <gpio-descriptor>;	: GP<PERSON> for FSI data signal

Optional properties:
 - enable-gpios = <gpio-descriptor>;	: <PERSON><PERSON> for enable signal
 - trans-gpios = <gpio-descriptor>;	: <PERSON><PERSON> for voltage translator enable
 - mux-gpios = <gpio-descriptor>;	: GPIO for pin multiplexing with other
                                          functions (eg, external FSI masters)
 - no-gpio-delays;			: Don't add extra delays between GPIO
                                          accesses. This is useful when the HW
					  GPIO block is running at a low enough
					  frequency.

Examples:

    fsi-master {
        compatible = "fsi-master-gpio", "fsi-master";
        clock-gpios = <&gpio 0>;
        data-gpios = <&gpio 1>;
        enable-gpios = <&gpio 2>;
        trans-gpios = <&gpio 3>;
        mux-gpios = <&gpio 4>;
    }
