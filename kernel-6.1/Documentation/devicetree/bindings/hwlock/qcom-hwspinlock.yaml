# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/hwlock/qcom-hwspinlock.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Hardware Mutex Block

maintainers:
  - <PERSON><PERSON><PERSON> <bjorn.and<PERSON><PERSON>@linaro.org>

description:
  The hardware block provides mutexes utilized between different processors on
  the SoC as part of the communication protocol used by these processors.

properties:
  compatible:
    oneOf:
      - enum:
          - qcom,sfpb-mutex
          - qcom,tcsr-mutex
      - items:
          - enum:
              - qcom,apq8084-tcsr-mutex
              - qcom,ipq6018-tcsr-mutex
              - qcom,msm8226-tcsr-mutex
              - qcom,msm8994-tcsr-mutex
          - const: qcom,tcsr-mutex
      - items:
          - enum:
              - qcom,msm8974-tcsr-mutex
          - const: qcom,tcsr-mutex
          - const: syscon

  reg:
    maxItems: 1

  '#hwlock-cells':
    const: 1

required:
  - compatible
  - reg
  - '#hwlock-cells'

additionalProperties: false

examples:
  - |
    hwlock@1f40000 {
        compatible = "qcom,tcsr-mutex";
        reg = <0x01f40000 0x40000>;
        #hwlock-cells = <1>;
    };
...
