# SPDX-License-Identifier: (GPL-2.0-only or BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/hwlock/allwinner,sun6i-a31-hwspinlock.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: SUN6I hardware spinlock driver for Allwinner sun6i compatible SoCs

maintainers:
  - <PERSON><PERSON><PERSON>t <<EMAIL>>

description:
  The hardware unit provides semaphores between the ARM cores and the embedded
  companion core on the SoC.

properties:
  compatible:
    const: allwinner,sun6i-a31-hwspinlock

  reg:
    maxItems: 1

  clocks:
    maxItems: 1

  resets:
    maxItems: 1

required:
  - compatible
  - reg
  - clocks
  - resets

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/sun8i-a23-a33-ccu.h>
    #include <dt-bindings/reset/sun8i-a23-a33-ccu.h>

    hwlock@1c18000 {
        compatible = "allwinner,sun6i-a31-hwspinlock";
        reg = <0x01c18000 0x1000>;
        clocks = <&ccu CLK_BUS_SPINLOCK>;
        resets = <&ccu RST_BUS_SPINLOCK>;
    };
...
