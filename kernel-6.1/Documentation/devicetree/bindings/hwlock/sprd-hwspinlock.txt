SPRD Hardware Spinlock Device Binding
-------------------------------------

Required properties :
- compatible : should be "sprd,hwspinlock-r3p0".
- reg : the register address of hwspinlock.
- #hwlock-cells : hwlock users only use the hwlock id to represent a specific
	hwlock, so the number of cells should be <1> here.
- clock-names : Must contain "enable".
- clocks : Must contain a phandle entry for the clock in clock-names, see the
	common clock bindings.

Please look at the generic hwlock binding for usage information for consumers,
"Documentation/devicetree/bindings/hwlock/hwlock.txt"

Example of hwlock provider:
	hwspinlock@40500000 {
		compatible  = "sprd,hwspinlock-r3p0";
		reg = <0 0x40500000 0 0x1000>;
		#hwlock-cells = <1>;
		clock-names = "enable";
		clocks = <&clk_aon_apb_gates0 22>;
	};
