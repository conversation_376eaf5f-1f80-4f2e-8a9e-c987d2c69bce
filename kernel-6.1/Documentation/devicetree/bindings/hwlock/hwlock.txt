Generic hwlock bindings
=======================

Generic bindings that are common to all the hwlock platform specific driver
implementations.

Please also look through the individual platform specific hwlock binding
documentations for identifying any additional properties specific to that
platform.

hwlock providers:
=================

Required properties:
- #hwlock-cells:        Specifies the number of cells needed to represent a
                        specific lock.

hwlock users:
=============

Consumers that require specific hwlock(s) should specify them using the
property "hwlocks", and an optional "hwlock-names" property.

Required properties:
- hwlocks:              List of phandle to a hwlock provider node and an
                        associated hwlock args specifier as indicated by
                        #hwlock-cells. The list can have just a single hwlock
                        or multiple hwlocks, with each hwlock represented by
                        a phandle and a corresponding args specifier.

Optional properties:
- hwlock-names:         List of hwlock name strings defined in the same order
                        as the hwlocks, with one name per hwlock. Consumers can
                        use the hwlock-names to match and get a specific hwlock.


1. Example of a node using a single specific hwlock:

The following example has a node requesting a hwlock in the bank defined by
the node hwlock1. hwlock1 is a hwlock provider with an argument specifier
of length 1.

	node {
		...
		hwlocks = <&hwlock1 2>;
		...
	};

2. Example of a node using multiple specific hwlocks:

The following example has a node requesting two hwlocks, a hwlock within
the hwlock device node 'hwlock1' with #hwlock-cells value of 1, and another
hwlock within the hwlock device node 'hwlock2' with #hwlock-cells value of 2.

	node {
		...
		hwlocks = <&hwlock1 2>, <&hwlock2 0 3>;
		...
	};
