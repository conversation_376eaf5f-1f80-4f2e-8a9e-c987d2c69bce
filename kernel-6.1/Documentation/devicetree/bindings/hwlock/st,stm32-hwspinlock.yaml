# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/hwlock/st,stm32-hwspinlock.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: STMicroelectronics STM32 Hardware Spinlock bindings

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

properties:
  "#hwlock-cells":
    const: 1

  compatible:
    const: st,stm32-hwspinlock

  reg:
    maxItems: 1

  clocks:
    maxItems: 1

  clock-names:
    items:
      - const: hsem

required:
  - "#hwlock-cells"
  - compatible
  - reg
  - clocks
  - clock-names

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/stm32mp1-clks.h>
    hwspinlock@4c000000 {
        compatible = "st,stm32-hwspinlock";
        #hwlock-cells = <1>;
        reg = <0x4c000000 0x400>;
        clocks = <&rcc HSEM>;
        clock-names = "hsem";
    };

...
