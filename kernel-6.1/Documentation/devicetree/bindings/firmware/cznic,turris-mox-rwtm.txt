Turris Mox rWTM firmware driver

Required properties:
 - compatible		: Should be "cznic,turris-mox-rwtm"
 - mboxes		: Must contain a reference to associated mailbox

This device tree node should be used on Turris Mox, or potentially another A3700
compatible device running the Mox's rWTM firmware in the secure processor (for
example it is possible to flash this firmware into EspressoBin).

Example:

	firmware {
		turris-mox-rwtm {
			compatible = "cznic,turris-mox-rwtm";
			mboxes = <&rwtm 0>;
			status = "okay";
		};
	};
