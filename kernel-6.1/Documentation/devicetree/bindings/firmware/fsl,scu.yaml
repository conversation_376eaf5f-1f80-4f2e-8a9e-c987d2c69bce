# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/firmware/fsl,scu.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: NXP i.MX System Controller Firmware (SCFW)

maintainers:
  - <PERSON> <<EMAIL>>

description:
  The System Controller Firmware (SCFW) is a low-level system function
  which runs on a dedicated Cortex-M core to provide power, clock, and
  resource management. It exists on some i.MX8 processors. e.g. i.MX8QM
  (QM, QP), and i.MX8QX (QXP, DX).
  The AP communicates with the SC using a multi-ported MU module found
  in the LSIO subsystem. The current definition of this MU module provides
  5 remote AP connections to the SC to support up to 5 execution environments
  (TZ, HV, standard Linux, etc.). The SC side of this MU module interfaces
  with the LSIO DSC IP bus. The SC firmware will communicate with this MU
  using the MSI bus.

properties:
  compatible:
    const: fsl,imx-scu

  clock-controller:
    description:
      Clock controller node that provides the clocks controlled by the SCU
    $ref: /schemas/clock/fsl,scu-clk.yaml

  gpio:
    description:
      Control the GPIO PINs on SCU domain over the firmware APIs
    $ref: /schemas/gpio/fsl,imx8qxp-sc-gpio.yaml

  ocotp:
    description:
      OCOTP controller node provided by the SCU
    $ref: /schemas/nvmem/fsl,scu-ocotp.yaml

  keys:
    description:
      Keys provided by the SCU
    $ref: /schemas/input/fsl,scu-key.yaml

  mboxes:
    description:
      A list of phandles of TX MU channels followed by a list of phandles of
      RX MU channels. The list may include at the end one more optional MU
      channel for general interrupt. The number of expected tx and rx
      channels is 1 TX and 1 RX channels if MU instance is "fsl,imx8-mu-scu"
      compatible, 4 TX and 4 RX channels otherwise. All MU channels must be
      within the same MU instance. Cross instances are not allowed. The MU
      instance can only be one of LSIO MU0~M4 for imx8qxp and imx8qm. Users
      need to ensure that one is used that does not conflict with other
      execution environments such as ATF.
    oneOf:
      - items:
          - description: TX0 MU channel
          - description: RX0 MU channel
      - items:
          - description: TX0 MU channel
          - description: RX0 MU channel
          - description: optional MU channel for general interrupt
      - items:
          - description: TX0 MU channel
          - description: TX1 MU channel
          - description: TX2 MU channel
          - description: TX3 MU channel
          - description: RX0 MU channel
          - description: RX1 MU channel
          - description: RX2 MU channel
          - description: RX3 MU channel
      - items:
          - description: TX0 MU channel
          - description: TX1 MU channel
          - description: TX2 MU channel
          - description: TX3 MU channel
          - description: RX0 MU channel
          - description: RX1 MU channel
          - description: RX2 MU channel
          - description: RX3 MU channel
          - description: optional MU channel for general interrupt

  mbox-names:
    oneOf:
      - items:
          - const: tx0
          - const: rx0
      - items:
          - const: tx0
          - const: rx0
          - const: gip3
      - items:
          - const: tx0
          - const: tx1
          - const: tx2
          - const: tx3
          - const: rx0
          - const: rx1
          - const: rx2
          - const: rx3
      - items:
          - const: tx0
          - const: tx1
          - const: tx2
          - const: tx3
          - const: rx0
          - const: rx1
          - const: rx2
          - const: rx3
          - const: gip3

  pinctrl:
    description:
      Pin controller provided by the SCU
    $ref: /schemas/pinctrl/fsl,scu-pinctrl.yaml

  power-controller:
    description:
      Power domains controller node that provides the power domains
      controlled by the SCU
    $ref: /schemas/power/fsl,scu-pd.yaml

  rtc:
    description:
      RTC controller provided by the SCU
    $ref: /schemas/rtc/fsl,scu-rtc.yaml

  thermal-sensor:
    description:
      Thermal sensor provided by the SCU
    $ref: /schemas/thermal/fsl,scu-thermal.yaml

  watchdog:
    description:
      Watchdog controller provided by the SCU
    $ref: /schemas/watchdog/fsl,scu-wdt.yaml

required:
  - compatible
  - mbox-names
  - mboxes

additionalProperties: false

examples:
  - |
    #include <dt-bindings/firmware/imx/rsrc.h>
    #include <dt-bindings/input/input.h>
    #include <dt-bindings/pinctrl/pads-imx8qxp.h>

    firmware {
        system-controller {
            compatible = "fsl,imx-scu";
            mbox-names = "tx0", "tx1", "tx2", "tx3",
                         "rx0", "rx1", "rx2", "rx3",
                         "gip3";
            mboxes = <&lsio_mu1 0 0 &lsio_mu1 0 1 &lsio_mu1 0 2 &lsio_mu1 0 3
                      &lsio_mu1 1 0 &lsio_mu1 1 1 &lsio_mu1 1 2 &lsio_mu1 1 3
                      &lsio_mu1 3 3>;

            clock-controller {
                compatible = "fsl,imx8qxp-clk", "fsl,scu-clk";
                #clock-cells = <2>;
            };

            pinctrl {
                compatible = "fsl,imx8qxp-iomuxc";

                pinctrl_lpuart0: lpuart0grp {
                    fsl,pins = <
                        IMX8QXP_UART0_RX_ADMA_UART0_RX   0x06000020
                        IMX8QXP_UART0_TX_ADMA_UART0_TX   0x06000020
                    >;
                };
            };

            ocotp {
                compatible = "fsl,imx8qxp-scu-ocotp";
                #address-cells = <1>;
                #size-cells = <1>;

                fec_mac0: mac@2c4 {
                    reg = <0x2c4 6>;
                };
            };

            power-controller {
                compatible = "fsl,imx8qxp-scu-pd", "fsl,scu-pd";
                #power-domain-cells = <1>;
            };

            rtc {
                compatible = "fsl,imx8qxp-sc-rtc";
            };

            keys {
                compatible = "fsl,imx8qxp-sc-key", "fsl,imx-sc-key";
                linux,keycodes = <KEY_POWER>;
            };

            watchdog {
                compatible = "fsl,imx8qxp-sc-wdt", "fsl,imx-sc-wdt";
                timeout-sec = <60>;
            };

            thermal-sensor {
                compatible = "fsl,imx8qxp-sc-thermal", "fsl,imx-sc-thermal";
                #thermal-sensor-cells = <1>;
            };
        };
    };
