Intel Service Layer Driver for Stratix10 SoC
============================================
Intel Stratix10 SoC is composed of a 64 bit quad-core ARM Cortex A53 hard
processor system (HPS) and Secure Device Manager (SDM). When the FPGA is
configured from HPS, there needs to be a way for HPS to notify SDM the
location and size of the configuration data. Then SDM will get the
configuration data from that location and perform the FPGA configuration.

To meet the whole system security needs and support virtual machine requesting
communication with SDM, only the secure world of software (EL3, Exception
Layer 3) can interface with SDM. All software entities running on other
exception layers must channel through the EL3 software whenever it needs
service from SDM.

Intel Stratix10 service layer driver, running at privileged exception level
(EL1, Exception Layer 1), interfaces with the service providers and provides
the services for FPGA configuration, QSPI, Crypto and warm reset. Service layer
driver also manages secure monitor call (SMC) to communicate with secure monitor
code running in EL3.

Required properties:
-------------------
The svc node has the following mandatory properties, must be located under
the firmware node.

- compatible: "intel,stratix10-svc" or "intel,agilex-svc"
- method: smc or hvc
        smc - Secure Monitor Call
        hvc - Hypervisor Call
- memory-region:
	phandle to the reserved memory node. See
	Documentation/devicetree/bindings/reserved-memory/reserved-memory.txt
	for details

Example:
-------

	reserved-memory {
                #address-cells = <2>;
                #size-cells = <2>;
                ranges;

                service_reserved: svcbuffer@0 {
                        compatible = "shared-dma-pool";
                        reg = <0x0 0x0 0x0 0x1000000>;
                        alignment = <0x1000>;
                        no-map;
                };
        };

	firmware {
		svc {
			compatible = "intel,stratix10-svc";
			method = "smc";
			memory-region = <&service_reserved>;
		};
	};
