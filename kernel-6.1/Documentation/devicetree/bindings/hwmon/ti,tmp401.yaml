# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/hwmon/ti,tmp401.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: TMP401, TPM411 and TMP43x temperature sensor

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description: |
  ±1°C Remote and Local temperature sensor

  Datasheets:
  https://www.ti.com/lit/ds/symlink/tmp401.pdf
  https://www.ti.com/lit/ds/symlink/tmp411.pdf
  https://www.ti.com/lit/ds/symlink/tmp431.pdf
  https://www.ti.com/lit/ds/symlink/tmp435.pdf

properties:
  compatible:
    enum:
      - ti,tmp401
      - ti,tmp411
      - ti,tmp431
      - ti,tmp432
      - ti,tmp435

  reg:
    maxItems: 1

  ti,extended-range-enable:
    description:
      When set, this sensor measures over extended temperature range.
    type: boolean

  ti,n-factor:
    description:
      value to be used for converting remote channel measurements to
      temperature.
    $ref: /schemas/types.yaml#/definitions/int32
    minimum: -128
    maximum: 127

  ti,beta-compensation:
    description:
      value to select beta correction range.
    $ref: /schemas/types.yaml#/definitions/uint32
    minimum: 0
    maximum: 15

allOf:
  - if:
      properties:
        compatible:
          contains:
            enum:
              - ti,tmp401
    then:
      properties:
        ti,n-factor: false

  - if:
      properties:
        compatible:
          contains:
            enum:
              - ti,tmp401
              - ti,tmp411
    then:
      properties:
        ti,beta-compensation: false

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    i2c {
      #address-cells = <1>;
      #size-cells = <0>;

      sensor@4c {
        compatible = "ti,tmp401";
        reg = <0x4c>;
      };
    };
  - |
    i2c {
      #address-cells = <1>;
      #size-cells = <0>;

      sensor@4c {
        compatible = "ti,tmp431";
        reg = <0x4c>;
        ti,extended-range-enable;
        ti,n-factor = <0x3b>;
        ti,beta-compensation = <0x7>;
      };
    };
