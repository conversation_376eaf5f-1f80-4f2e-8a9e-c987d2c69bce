# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/hwmon/microchip,lan966x.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Microchip LAN966x Hardware Monitor

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Microchip LAN966x temperature monitor and fan controller

properties:
  compatible:
    enum:
      - microchip,lan9668-hwmon

  reg:
    items:
      - description: PVT registers
      - description: FAN registers

  reg-names:
    items:
      - const: pvt
      - const: fan

  clocks:
    maxItems: 1

  '#thermal-sensor-cells':
    const: 0

required:
  - compatible
  - reg
  - reg-names
  - clocks

additionalProperties: false

examples:
  - |
    hwmon: hwmon@e2010180 {
        compatible = "microchip,lan9668-hwmon";
        reg = <0xe2010180 0xc>,
              <0xe20042a8 0xc>;
        reg-names = "pvt", "fan";
        clocks = <&sys_clk>;
        #thermal-sensor-cells = <0>;
    };
