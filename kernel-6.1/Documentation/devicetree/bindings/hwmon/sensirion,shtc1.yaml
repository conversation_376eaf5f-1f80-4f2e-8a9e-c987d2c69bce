# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/hwmon/sensirion,shtc1.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Sensirion SHTC1 Humidity and Temperature Sensor IC

maintainers:
  - <PERSON> <EMAIL>

description: |
  The SHTC1, SHTW1 and SHTC3 are digital humidity and temperature sensors
  designed especially for battery-driven high-volume consumer electronics
  applications.
  For further information refere to Documentation/hwmon/shtc1.rst

  This binding document describes the binding for the hardware monitor
  portion of the driver.

properties:
  compatible:
    enum:
      - sensirion,shtc1
      - sensirion,shtw1
      - sensirion,shtc3

  reg:
    const: 0x70

  sensirion,blocking-io:
    $ref: /schemas/types.yaml#/definitions/flag
    description:
      If set, the driver holds the i2c bus until the measurement is finished.

  sensirion,low-precision:
    $ref: /schemas/types.yaml#/definitions/flag
    description:
      If set, the sensor acquires data with low precision (not recommended).
      The driver acquires data with high precision by default.

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    i2c {
      #address-cells = <1>;
      #size-cells = <0>;
      clock-frequency = <400000>;

      shtc3@70 {
        compatible = "sensirion,shtc3";
        reg = <0x70>;
        sensirion,blocking-io;
      };
    };
...
