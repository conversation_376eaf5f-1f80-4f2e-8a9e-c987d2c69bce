# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---

$id: http://devicetree.org/schemas/hwmon/winbond,w83781d.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Winbond W83781 and compatible hardware monitor IC

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - winbond,w83781d
      - winbond,w83781g
      - winbond,w83782d
      - winbond,w83783s
      - asus,as99127f

  reg:
    maxItems: 1

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        temperature-sensor@28 {
            compatible = "winbond,w83781d";
            reg = <0x28>;
        };
    };
