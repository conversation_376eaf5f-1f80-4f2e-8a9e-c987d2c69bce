ltc2990: Linear Technology LTC2990 power monitor

Required properties:
- compatible: Must be "lltc,ltc2990"
- reg: I2C slave address
- lltc,meas-mode:
	An array of two integers for configuring the chip measurement mode.

	The first integer defines the bits 2..0 in the control register. In all
	cases the internal temperature and supply voltage are measured. In
	addition the following input measurements are enabled per mode:

		0: V1, V2, TR2
		1: V1-V2, TR2
		2: V1-V2, V3, V4
		3: TR1, V3, V4
		4: TR1, V3-V4
		5: TR1, TR2
		6: V1-V2, V3-V4
		7: V1, V2, V3, V4

	The second integer defines the bits 4..3 in the control register. This
	allows a subset of the measurements to be enabled:

		0: Internal temperature and supply voltage only
		1: TR1, V1 or V1-V2 only per mode
		2: TR2, V3 or V3-V4 only per mode
		3: All measurements per mode

Example:

ltc2990@4c {
	compatible = "lltc,ltc2990";
	reg = <0x4c>;
	lltc,meas-mode = <7 3>;	/* V1, V2, V3, V4 */
};
