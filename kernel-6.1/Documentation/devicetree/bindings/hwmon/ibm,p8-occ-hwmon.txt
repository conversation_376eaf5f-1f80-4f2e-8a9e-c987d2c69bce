Device-tree bindings for I2C-based On-Chip Controller hwmon device
------------------------------------------------------------------

Required properties:
 - compatible = "ibm,p8-occ-hwmon";
 - reg = <I2C address>;			: I2C bus address

Examples:

    i2c-bus@100 {
        #address-cells = <1>;
        #size-cells = <0>;
        clock-frequency = <100000>;
        < more properties >

        occ-hwmon@1 {
            compatible = "ibm,p8-occ-hwmon";
            reg = <0x50>;
        };

        occ-hwmon@2 {
            compatible = "ibm,p8-occ-hwmon";
            reg = <0x51>;
        };
    };
