# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/hwmon/amd,sbtsi.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: >
  Sideband interface Temperature Sensor Interface (SB-TSI) compliant
  AMD SoC temperature device

maintainers:
  - <PERSON><PERSON> <<EMAIL>>
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description: |
  SB Temperature Sensor Interface (SB-TSI) is an SMBus compatible
  interface that reports AMD SoC's Ttcl (normalized temperature),
  and resembles a typical 8-pin remote temperature sensor's I2C interface
  to BMC. The emulated thermal sensor can report temperatures in increments
  of 0.125 degrees, ranging from 0 to 255.875.

properties:
  compatible:
    enum:
      - amd,sbtsi

  reg:
    maxItems: 1
    description: |
      I2C bus address of the device as specified in Section 6.3.1 of the
      SoC register reference. The SB-TSI address is normally 98h for socket
      0 and 90h for socket 1, but it could vary based on hardware address
      select pins.
      \[open source SoC register reference\]
        https://www.amd.com/system/files/TechDocs/56255_OSRR.pdf

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    i2c0 {
        #address-cells = <1>;
        #size-cells = <0>;

        sbtsi@4c {
                compatible = "amd,sbtsi";
                reg = <0x4c>;
        };
    };
...
