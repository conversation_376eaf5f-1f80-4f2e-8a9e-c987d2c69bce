# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/hwmon/adi,adm1266.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Analog Devices ADM1266 Cascadable Super Sequencer with Margin
  Control and Fault Recording

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description: |
  Analog Devices ADM1266 Cascadable Super Sequencer with Margin
  Control and Fault Recording.
  https://www.analog.com/media/en/technical-documentation/data-sheets/ADM1266.pdf

properties:
  compatible:
    enum:
      - adi,adm1266

  reg:
    description: |
      I2C address of slave device.
    items:
      minimum: 0x40
      maximum: 0x4F

  avcc-supply:
    description: |
      Phandle to the Avcc power supply.

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    i2c0 {
        #address-cells = <1>;
        #size-cells = <0>;

        adm1266@40 {
                compatible = "adi,adm1266";
                reg = <0x40>;
        };
    };
...
