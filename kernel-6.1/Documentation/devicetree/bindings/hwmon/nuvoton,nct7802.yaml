# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---

$id: http://devicetree.org/schemas/hwmon/nuvoton,nct7802.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Nuvoton NCT7802Y Hardware Monitoring IC

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description: |
  The NCT7802Y is a hardware monitor IC which supports one on-die and up to
  5 remote temperature sensors with SMBus interface.

  Datasheets:
    https://www.nuvoton.com/export/resource-files/Nuvoton_NCT7802Y_Datasheet_V12.pdf

additionalProperties: false

properties:
  compatible:
    enum:
      - nuvoton,nct7802

  reg:
    maxItems: 1

  "#address-cells":
    const: 1

  "#size-cells":
    const: 0

patternProperties:
  "^channel@[0-3]$":
    type: object

    additionalProperties: false

    properties:
      reg:
        items:
          - enum:
              - 0    # Local Temperature Sensor ("LTD")
              - 1    # Remote Temperature Sensor or Voltage Sensor 1 ("RTD1")
              - 2    # Remote Temperature Sensor or Voltage Sensor 2 ("RTD2")
              - 3    # Remote Temperature Sensor or Voltage Sensor 3 ("RTD3")

      sensor-type:
        items:
          - enum:
              - temperature
              - voltage

      temperature-mode:
        items:
          - enum:
              - thermistor
              - thermal-diode

    required:
      - reg

    allOf:
      # For channels RTD1, RTD2 and RTD3, require sensor-type to be set.
      # Otherwise (for all other channels), do not allow temperature-mode to be
      # set.
      - if:
          properties:
            reg:
              items:
                - enum:
                    - 1
                    - 2
                    - 3
        then:
          required:
            - sensor-type
        else:
          not:
            required:
              - sensor-type

      # For channels RTD1 and RTD2 and if sensor-type is "temperature", require
      # temperature-mode to be set. Otherwise (for all other channels or
      # sensor-type settings), do not allow temperature-mode to be set
      - if:
          properties:
            reg:
              items:
                - enum:
                    - 1
                    - 2
            sensor-type:
              items:
                - enum:
                    - temperature
        then:
          required:
            - temperature-mode
        else:
          not:
            required:
              - temperature-mode

required:
  - compatible
  - reg

examples:
  - |
    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        nct7802@28 {
            compatible = "nuvoton,nct7802";
            reg = <0x28>;

            #address-cells = <1>;
            #size-cells = <0>;

            channel@0 { /* LTD */
              reg = <0>;
            };

            channel@1 { /* RTD1 */
              reg = <1>;
              sensor-type = "voltage";
            };

            channel@2 { /* RTD2 */
              reg = <2>;
              sensor-type = "temperature";
              temperature-mode = "thermal-diode";
            };

            channel@3 { /* RTD3 */
              reg = <3>;
              sensor-type = "temperature";
            };
        };
    };
