# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---

$id: http://devicetree.org/schemas/hwmon/pmbus/ti,lm25066.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: National Semiconductor/Texas Instruments LM250x6/LM506x power-management ICs

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description: |
  The LM25066 family of power-management ICs (a.k.a. hot-swap
  controllers or eFuses in various contexts) are PMBus devices that
  offer temperature, current, voltage, and power monitoring.

  Datasheet: https://www.ti.com/lit/ds/symlink/lm25066.pdf

properties:
  compatible:
    enum:
      - ti,lm25056
      - ti,lm25066
      - ti,lm5064
      - ti,lm5066
      - ti,lm5066i

  reg:
    maxItems: 1

  shunt-resistor-micro-ohms:
    description:
      Shunt (sense) resistor value in micro-Ohms
    default: 1000

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        pmic@40 {
            compatible = "ti,lm25066";
            reg = <0x40>;
            shunt-resistor-micro-ohms = <675>;
        };
    };
