# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/hwmon/adi,max31760.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Analog Devices MAX31760 Fan-Speed Controller

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Analog Devices MAX31760 Fan-Speed Controller
  https://datasheets.maximintegrated.com/en/ds/MAX31760.pdf

properties:
  compatible:
    enum:
      - adi,max31760

  reg:
    description: I2C address of slave device.
    minimum: 0x50
    maximum: 0x57

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        fan-controller@50 {
            reg = <0x50>;
            compatible = "adi,max31760";
        };
    };
