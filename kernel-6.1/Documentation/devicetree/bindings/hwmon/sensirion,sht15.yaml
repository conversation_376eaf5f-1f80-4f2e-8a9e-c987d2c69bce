# SPDX-License-Identifier: GPL-2.0-only or BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/hwmon/sensirion,sht15.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Sensirion SHT15 humidity and temperature sensor

maintainers:
  - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    const: sensirion,sht15

  clk-gpios:
    maxItems: 1

  data-gpios:
    maxItems: 1

  vcc-supply:
    description: regulator that drives the VCC pin

required:
  - compatible
  - clk-gpios
  - data-gpios
  - vcc-supply

additionalProperties: false

examples:
  - |
    sensor {
        compatible = "sensirion,sht15";
        clk-gpios = <&gpio4 12 0>;
        data-gpios = <&gpio4 13 0>;
        vcc-supply = <&reg_sht15>;

        pinctrl-names = "default";
        pinctrl-0 = <&pinctrl_sensor>;
    };
