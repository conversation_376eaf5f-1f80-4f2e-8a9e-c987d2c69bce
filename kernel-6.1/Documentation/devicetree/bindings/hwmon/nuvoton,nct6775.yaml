# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---

$id: http://devicetree.org/schemas/hwmon/nuvoton,nct6775.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Nuvoton NCT6775 and compatible Super I/O chips

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - nuvoton,nct6106
      - nuvoton,nct6116
      - nuvoton,nct6775
      - nuvoton,nct6776
      - nuvoton,nct6779
      - nuvoton,nct6791
      - nuvoton,nct6792
      - nuvoton,nct6793
      - nuvoton,nct6795
      - nuvoton,nct6796
      - nuvoton,nct6797
      - nuvoton,nct6798

  reg:
    maxItems: 1

  nuvoton,tsi-channel-mask:
    description:
      Bitmask indicating which TSI temperature sensor channels are
      active.  LSB is TSI0, bit 1 is TSI1, etc.
    $ref: /schemas/types.yaml#/definitions/uint32
    maximum: 0xff
    default: 0

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        superio@4d {
            compatible = "nuvoton,nct6779";
            reg = <0x4d>;
            nuvoton,tsi-channel-mask = <0x03>;
        };
    };
