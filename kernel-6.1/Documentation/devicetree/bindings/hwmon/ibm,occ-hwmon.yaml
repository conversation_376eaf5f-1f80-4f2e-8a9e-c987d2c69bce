# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/hwmon/ibm,occ-hwmon.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: IBM On-Chip Controller (OCC) accessed from a service processor

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  The POWER processor On-Chip Controller (OCC) helps manage power and
  thermals for the system. A service processor or baseboard management
  controller can query the OCC for it's power and thermal data to report
  through hwmon.

properties:
  compatible:
    enum:
      - ibm,p9-occ-hwmon
      - ibm,p10-occ-hwmon

  ibm,no-poll-on-init:
    description: This property describes whether or not the OCC should
      be polled during driver initialization.
    type: boolean

required:
  - compatible

additionalProperties: false

examples:
  - |
    hwmon {
        compatible = "ibm,p10-occ-hwmon";
        ibm,no-poll-on-init;
    };
