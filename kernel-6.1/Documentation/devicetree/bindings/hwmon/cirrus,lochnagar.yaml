# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/hwmon/cirrus,lochnagar.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Cirrus Logic Lochnagar Audio Development Board

maintainers:
  - <EMAIL>

description: |
  Lochnagar is an evaluation and development board for Cirrus Logic
  Smart CODEC and Amp devices. It allows the connection of most Cirrus
  Logic devices on mini-cards, as well as allowing connection of various
  application processor systems to provide a full evaluation platform.
  Audio system topology, clocking and power can all be controlled through
  the Lochnagar, allowing the device under test to be used in a variety of
  possible use cases.

  This binding document describes the binding for the hardware monitor
  portion of the driver.

  This binding must be part of the Lochnagar MFD binding:
    [1] ../mfd/cirrus,lochnagar.yaml

properties:
  compatible:
    enum:
      - cirrus,lochnagar2-hwmon

required:
  - compatible

additionalProperties: false
