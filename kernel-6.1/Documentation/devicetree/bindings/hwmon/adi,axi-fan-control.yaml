# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
# Copyright 2019 Analog Devices Inc.
%YAML 1.2
---
$id: http://devicetree.org/schemas/hwmon/adi,axi-fan-control.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Analog Devices AXI FAN Control

maintainers:
  - Nuno Sá <<EMAIL>>

description: |+
  Bindings for the Analog Devices AXI FAN Control driver. Spefications of the
  core can be found in:

  https://wiki.analog.com/resources/fpga/docs/axi_fan_control

properties:
  compatible:
    enum:
      - adi,axi-fan-control-1.00.a

  reg:
    maxItems: 1

  clocks:
    maxItems: 1

  interrupts:
    maxItems: 1

  pulses-per-revolution:
    description:
      Value specifying the number of pulses per revolution of the controlled
      FAN.
    $ref: /schemas/types.yaml#/definitions/uint32
    enum: [1, 2, 4]

required:
  - compatible
  - reg
  - clocks
  - interrupts
  - pulses-per-revolution

additionalProperties: false

examples:
  - |
    fpga_axi: fpga-axi {
            #address-cells = <0x2>;
            #size-cells = <0x1>;

            axi_fan_control: axi-fan-control@80000000 {
                    compatible = "adi,axi-fan-control-1.00.a";
                    reg = <0x0 0x80000000 0x10000>;
                    clocks = <&clk 71>;
                    interrupts = <0 110 0>;
                    pulses-per-revolution = <2>;
            };
    };
...
