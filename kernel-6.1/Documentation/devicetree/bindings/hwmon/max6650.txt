Bindings for MAX6651 and MAX6650 I2C fan controllers

Reference:
[1]	https://datasheets.maximintegrated.com/en/ds/MAX6650-MAX6651.pdf

Required properties:
- compatible : One of "maxim,max6650" or "maxim,max6651"
- reg        : I2C address, one of 0x1b, 0x1f, 0x4b, 0x48.

Optional properties, default is to retain the chip's current setting:
- maxim,fan-microvolt : The supply voltage of the fan, either 5000000 uV or
			12000000 uV.
- maxim,fan-prescale  : Pre-scaling value, as per datasheet [1]. Lower values
			allow more fine-grained control of slower fans.
			Valid: 1, 2, 4, 8, 16.
- maxim,fan-target-rpm: Initial requested fan rotation speed. If specified, the
			driver selects closed-loop mode and the requested speed.
			This ensures the fan is already running before userspace
			takes over.

Example:
	fan-max6650: max6650@1b {
		reg = <0x1b>;
		compatible = "maxim,max6650";
		maxim,fan-microvolt = <12000000>;
		maxim,fan-prescale = <4>;
		maxim,fan-target-rpm = <1200>;
	};
