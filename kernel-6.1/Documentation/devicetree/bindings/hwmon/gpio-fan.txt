Bindings for fan connected to GPIO lines

Required properties:
- compatible : "gpio-fan"

Optional properties:
- gpios: Specifies the pins that map to bits in the control value,
  ordered MSB-->LSB.
- gpio-fan,speed-map: A mapping of possible fan RPM speeds and the
  control value that should be set to achieve them. This array
  must have the RPM values in ascending order.
- alarm-gpios: This pin going active indicates something is wrong with
  the fan, and a udev event will be fired.
- #cooling-cells: If used as a cooling device, must be <2>
  Also see:
  Documentation/devicetree/bindings/thermal/thermal-cooling-devices.yaml
  min and max states are derived from the speed-map of the fan.

Note: At least one the "gpios" or "alarm-gpios" properties must be set.

Examples:

	gpio_fan {
		compatible = "gpio-fan";
		gpios = <&gpio1 14 1
			 &gpio1 13 1>;
		gpio-fan,speed-map = <0    0
				      3000 1
				      6000 2>;
		alarm-gpios = <&gpio1 15 1>;
	};
	gpio_fan_cool: gpio_fan {
		compatible = "gpio-fan";
		gpios = <&gpio2 14 1
			 &gpio2 13 1>;
		gpio-fan,speed-map =	<0    0>,
					<3000 1>,
					<6000 2>;
		alarm-gpios = <&gpio2 15 1>;
		#cooling-cells = <2>; /* min followed by max */
	};
