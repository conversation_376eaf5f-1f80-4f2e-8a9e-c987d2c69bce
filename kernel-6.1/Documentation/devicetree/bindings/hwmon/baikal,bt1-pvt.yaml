# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
# Copyright (C) 2020 BAIKAL ELECTRONICS, JSC
%YAML 1.2
---
$id: http://devicetree.org/schemas/hwmon/baikal,bt1-pvt.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Baikal-T1 PVT Sensor

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Baikal-T1 SoC provides an embedded process, voltage and temperature
  sensor to monitor an internal SoC environment (chip temperature, supply
  voltage and process monitor) and on time detect critical situations,
  which may cause the system instability and even damages. The IP-block
  is based on the Analog Bits PVT sensor, but is equipped with a dedicated
  control wrapper, which provides a MMIO registers-based access to the
  sensor core functionality (APB3-bus based) and exposes an additional
  functions like thresholds/data ready interrupts, its status and masks,
  measurements timeout. Its internal structure is depicted on the next
  diagram:

     Analog Bits core                     Bakal-T1 PVT control block
  +--------------------+                  +------------------------+
  | Temperature sensor |-+         +------| Sensors control        |
  |--------------------| |<---En---|      |------------------------|
  | Voltage sensor     |-|<--Mode--| +--->| Sampled data           |
  |--------------------| |<--Trim--+ |    |------------------------|
  | Low-Vt sensor      |-|           | +--| Thresholds comparator  |
  |--------------------| |---Data----| |  |------------------------|
  | High-Vt sensor     |-|           | +->| Interrupts status      |
  |--------------------| |--Valid--+-+ |  |------------------------|
  | Standard-Vt sensor |-+         +---+--| Interrupts mask        |
  +--------------------+                  |------------------------|
           ^                              | Interrupts timeout     |
           |                              +------------------------+
           |                                        ^  ^
  Rclk-----+----------------------------------------+  |
  APB3-------------------------------------------------+

  This bindings describes the external Baikal-T1 PVT control interfaces
  like MMIO registers space, interrupt request number and clocks source.
  These are then used by the corresponding hwmon device driver to
  implement the sysfs files-based access to the sensors functionality.

properties:
  compatible:
    const: baikal,bt1-pvt

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks:
    items:
      - description: PVT reference clock
      - description: APB3 interface clock

  clock-names:
    items:
      - const: ref
      - const: pclk

  "#thermal-sensor-cells":
    description: Baikal-T1 can be referenced as the CPU thermal-sensor
    const: 0

  baikal,pvt-temp-offset-millicelsius:
    description: |
      Temperature sensor trimming factor. It can be used to manually adjust the
      temperature measurements within 7.130 degrees Celsius.
    default: 0
    minimum: 0
    maximum: 7130

additionalProperties: false

required:
  - compatible
  - reg
  - interrupts
  - clocks
  - clock-names

examples:
  - |
    #include <dt-bindings/interrupt-controller/mips-gic.h>

    pvt@1f200000 {
      compatible = "baikal,bt1-pvt";
      reg = <0x1f200000 0x1000>;
      #thermal-sensor-cells = <0>;

      interrupts = <GIC_SHARED 31 IRQ_TYPE_LEVEL_HIGH>;

      baikal,pvt-temp-offset-millicelsius = <1000>;

      clocks = <&ccu_sys>, <&ccu_sys>;
      clock-names = "ref", "pclk";
    };
...
