# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/hwmon/amd,sbrmi.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: >
  Sideband Remote Management Interface (SB-RMI) compliant
  AMD SoC power device.

maintainers:
  - <PERSON><PERSON><PERSON> <A<PERSON><PERSON><PERSON>@amd.com>

description: |
  SB Remote Management Interface (SB-RMI) is an SMBus compatible
  interface that reports AMD SoC's Power (normalized Power) using,
  Mailbox Service Request and resembles a typical 8-pin remote power
  sensor's I2C interface to BMC. The power attributes in hwmon
  reports power in microwatts.

properties:
  compatible:
    enum:
      - amd,sbrmi

  reg:
    maxItems: 1
    description: |
      I2C bus address of the device as specified in Section SBI SMBus Address
      of the SoC register reference. The SB-RMI address is normally 78h for
      socket 0 and 70h for socket 1, but it could vary based on hardware
      address select pins.
      \[open source SoC register reference\]
        https://www.amd.com/en/support/tech-docs?keyword=55898

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    i2c0 {
        #address-cells = <1>;
        #size-cells = <0>;

        sbrmi@3c {
                compatible = "amd,sbrmi";
                reg = <0x3c>;
        };
    };
...
