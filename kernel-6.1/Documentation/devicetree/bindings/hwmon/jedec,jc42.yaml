# SPDX-License-Identifier: GPL-2.0-only or BSD-2-<PERSON>e
%YAML 1.2
---
$id: http://devicetree.org/schemas/hwmon/jedec,jc42.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: <PERSON><PERSON> JC-42.4 compatible temperature sensors

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON><PERSON> <<EMAIL>>

select:
  properties:
    compatible:
      const: jedec,jc-42.4-temp

  required:
    - compatible

properties:
  compatible:
    oneOf:
      - const: jedec,jc-42.4-temp
      - items:
          - enum:
              - adi,adt7408
              - atmel,at30ts00
              - atmel,at30tse004
              - idt,tse2002
              - idt,tse2004
              - idt,ts3000
              - idt,ts3001
              - maxim,max6604
              - microchip,mcp9804
              - microchip,mcp9805
              - microchip,mcp9808
              - microchip,mcp98243
              - microchip,mcp98244
              - microchip,mcp9843
              - nxp,se97
              - nxp,se97b
              - nxp,se98
              - onnn,cat6095
              - onnn,cat34ts02
              - st,stts2002
              - st,stts2004
              - st,stts3000
              - st,stts424
              - st,stts424e
          - const: jedec,jc-42.4-temp

  reg:
    maxItems: 1

  smbus-timeout-disable:
    description: |
      When set, the smbus timeout function will be disabled. This is not
      supported on all chips.
    type: boolean

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        temp-sensor@1a {
            compatible = "jedec,jc-42.4-temp";
            reg = <0x1a>;
        };
    };
