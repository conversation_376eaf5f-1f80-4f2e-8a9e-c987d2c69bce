# SPDX-License-Identifier: GPL-2.0-only or BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/hwmon/lm75.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: LM75 hwmon sensor

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - adi,adt75
      - atmel,at30ts74
      - dallas,ds1775
      - dallas,ds75
      - dallas,ds7505
      - gmt,g751
      - national,lm75
      - national,lm75a
      - national,lm75b
      - maxim,max6625
      - maxim,max6626
      - maxim,max31725
      - maxim,max31726
      - maxim,mcp980x
      - nxp,pct2075
      - st,stds75
      - st,stlm75
      - microchip,tcn75
      - ti,tmp1075
      - ti,tmp100
      - ti,tmp101
      - ti,tmp105
      - ti,tmp112
      - ti,tmp175
      - ti,tmp275
      - ti,tmp75
      - ti,tmp75b
      - ti,tmp75c

  reg:
    maxItems: 1

  vs-supply:
    description: phandle to the regulator that provides the +VS supply

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    i2c {
      #address-cells = <1>;
      #size-cells = <0>;

      sensor@48 {
        compatible = "st,stlm75";
        reg = <0x48>;
        vs-supply = <&vs>;
      };
    };
