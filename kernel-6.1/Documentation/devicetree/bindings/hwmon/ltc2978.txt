ltc2978

Required properties:
- compatible: should contain one of:
  * "lltc,ltc2972"
  * "lltc,ltc2974"
  * "lltc,ltc2975"
  * "lltc,ltc2977"
  * "lltc,ltc2978"
  * "lltc,ltc2979"
  * "lltc,ltc2980"
  * "lltc,ltc3880"
  * "lltc,ltc3882"
  * "lltc,ltc3883"
  * "lltc,ltc3884"
  * "lltc,ltc3886"
  * "lltc,ltc3887"
  * "lltc,ltc3889"
  * "lltc,ltc7880"
  * "lltc,ltm2987"
  * "lltc,ltm4664"
  * "lltc,ltm4675"
  * "lltc,ltm4676"
  * "lltc,ltm4677"
  * "lltc,ltm4678"
  * "lltc,ltm4680"
  * "lltc,ltm4686"
  * "lltc,ltm4700"
- reg: I2C slave address

Optional properties:
- regulators: A node that houses a sub-node for each regulator controlled by
  the device. Each sub-node is identified using the node's name, with valid
  values listed below. The content of each sub-node is defined by the
  standard binding for regulators; see regulator.txt.

Valid names of regulators depend on number of supplies supported per device:
  * ltc2972 vout0 - vout1
  * ltc2974, ltc2975 : vout0 - vout3
  * ltc2977, ltc2979, ltc2980, ltm2987 : vout0 - vout7
  * ltc2978 : vout0 - vout7
  * ltc3880, ltc3882, ltc3884, ltc3886, ltc3887, ltc3889 : vout0 - vout1
  * ltc7880 : vout0 - vout1
  * ltc3883 : vout0
  * ltm4664 : vout0 - vout1
  * ltm4675, ltm4676, ltm4677, ltm4678 : vout0 - vout1
  * ltm4680, ltm4686 : vout0 - vout1
  * ltm4700 : vout0 - vout1

Example:
ltc2978@5e {
	compatible = "lltc,ltc2978";
	reg = <0x5e>;
	regulators {
		vout0 {
			regulator-name = "FPGA-2.5V";
		};
		vout2 {
			regulator-name = "FPGA-1.5V";
		};
	};
};
