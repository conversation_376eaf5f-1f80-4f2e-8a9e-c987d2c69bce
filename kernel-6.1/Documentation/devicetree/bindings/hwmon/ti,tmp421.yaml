# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/hwmon/ti,tmp421.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: TMP42x/TMP44x temperature sensor

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description: |
  ±1°C Remote and Local temperature sensor
  https://www.ti.com/lit/ds/symlink/tmp422.pdf

properties:
  compatible:
    enum:
      - ti,tmp421
      - ti,tmp422
      - ti,tmp423
      - ti,tmp441
      - ti,tmp442
  reg:
    maxItems: 1

  '#address-cells':
    const: 1

  '#size-cells':
    const: 0

required:
  - compatible
  - reg

additionalProperties: false

patternProperties:
  "^channel@([0-3])$":
    type: object
    description: |
      Represents channels of the device and their specific configuration.

    properties:
      reg:
        description: |
          The channel number. 0 is local channel, 1-3 are remote channels
        items:
          minimum: 0
          maximum: 3

      label:
        description: |
          A descriptive name for this channel, like "ambient" or "psu".

      ti,n-factor:
        description: |
          The value (two's complement) to be programmed in the channel specific N correction register.
          For remote channels only.
        $ref: /schemas/types.yaml#/definitions/int32
        minimum: -128
        maximum: 127

    required:
      - reg

    additionalProperties: false

examples:
  - |
    i2c {
      #address-cells = <1>;
      #size-cells = <0>;

      sensor@4c {
        compatible = "ti,tmp422";
        reg = <0x4c>;
      };
    };
  - |
    i2c {
      #address-cells = <1>;
      #size-cells = <0>;

      sensor@4c {
        compatible = "ti,tmp422";
        reg = <0x4c>;
        #address-cells = <1>;
        #size-cells = <0>;

        channel@0 {
          reg = <0x0>;
          ti,n-factor = <0x1>;
          label = "local";
        };

        channel@1 {
          reg = <0x1>;
          ti,n-factor = <0x0>;
          label = "somelabel";
        };

        channel@2 {
          reg = <0x2>;
          status = "disabled";
        };
      };
    };
