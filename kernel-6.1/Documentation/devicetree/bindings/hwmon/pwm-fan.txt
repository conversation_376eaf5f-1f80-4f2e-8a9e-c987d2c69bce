Bindings for a fan connected to the PWM lines

Required properties:
- compatible	: "pwm-fan"
- pwms		: the PWM that is used to control the PWM fan
- cooling-levels      : PWM duty cycle values in a range from 0 to 255
			which correspond to thermal cooling states

Optional properties:
- fan-supply		: phandle to the regulator that provides power to the fan
- interrupts		: This contains an interrupt specifier for each fan
			  tachometer output connected to an interrupt source.
			  The output signal must generate a defined number of
			  interrupts per fan revolution, which require that
			  it must be self resetting edge interrupts. See
			  interrupt-controller/interrupts.txt for the format.
- pulses-per-revolution : define the number of pulses per fan revolution for
			  each tachometer input as an integer (default is 2
			  interrupts per revolution). The value must be
			  greater than zero.
- rockchip,temp-trips	: The property is an array of 2-tuples items, and
			  each item consists of temperature in millicelsius and
			  pwm cooling state. This depends on CONFIG_ROCKCHIP_SYSTEM_MONITOR.
			  If add the property the fan cooling state will be changed
			  by system monitor. Otherwise, use the default thermal governor.

Example:
	fan0: pwm-fan {
		compatible = "pwm-fan";
		#cooling-cells = <2>;
		pwms = <&pwm 0 10000 0>;
		cooling-levels = <0 102 170 230>;
	};

	thermal-zones {
		cpu_thermal: cpu-thermal {
			     thermal-sensors = <&tmu 0>;
			     polling-delay-passive = <0>;
			     polling-delay = <0>;
			     trips {
					cpu_alert1: cpu-alert1 {
						    temperature = <100000>; /* millicelsius */
						    hysteresis = <2000>; /* millicelsius */
						    type = "passive";
					};
			     };
			     cooling-maps {
					map0 {
						    trip = <&cpu_alert1>;
						    cooling-device = <&fan0 0 1>;
					};
			     };
		};

Example 2:
	fan0: pwm-fan {
		compatible = "pwm-fan";
		pwms = <&pwm 0 40000 0>;
		fan-supply = <&reg_fan>;
		interrupt-parent = <&gpio5>;
		interrupts = <1 IRQ_TYPE_EDGE_FALLING>;
		pulses-per-revolution = <2>;
	};

Example 3:
	fan0: pwm-fan {
		compatible = "pwm-fan";
		pwms = <&pwm1 0 25000 0>;
		interrupts-extended = <&gpio1 1 IRQ_TYPE_EDGE_FALLING>,
			<&gpio2 5 IRQ_TYPE_EDGE_FALLING>;
		pulses-per-revolution = <2>, <1>;
	};
