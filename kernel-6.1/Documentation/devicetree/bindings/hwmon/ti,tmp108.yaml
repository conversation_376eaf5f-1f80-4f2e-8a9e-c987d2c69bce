# SPDX-License-Identifier: GPL-2.0-only or BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/hwmon/ti,tmp108.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: TMP108 temperature sensor

maintainers:
  - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - ti,tmp108

  interrupts:
    items:
      - description: alert interrupt

  reg:
    maxItems: 1

  "#thermal-sensor-cells":
    const: 0

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>

    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        sensor@48 {
            compatible = "ti,tmp108";
            reg = <0x48>;
            interrupt-parent = <&gpio1>;
            interrupts = <7 IRQ_TYPE_LEVEL_LOW>;
            pinctrl-names = "default";
            pinctrl-0 = <&tmp_alrt>;
            #thermal-sensor-cells = <0>;
        };
    };
