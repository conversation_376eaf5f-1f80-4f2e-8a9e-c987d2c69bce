# SPDX-License-Identifier: GPL-2.0-only or BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/hwmon/national,lm90.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: LM90 series thermometer

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - adi,adm1032
      - adi,adt7461
      - adi,adt7461a
      - adi,adt7481
      - dallas,max6646
      - dallas,max6647
      - dallas,max6649
      - dallas,max6657
      - dallas,max6658
      - dallas,max6659
      - dallas,max6680
      - dallas,max6681
      - dallas,max6695
      - dallas,max6696
      - gmt,g781
      - national,lm86
      - national,lm89
      - national,lm90
      - national,lm99
      - nxp,sa56004
      - onnn,nct1008
      - ti,tmp451
      - ti,tmp461
      - winbond,w83l771


  interrupts:
    items:
      - description: |
          Single interrupt specifier which describes the LM90 "-ALERT" pin
          output.

  reg:
    maxItems: 1

  "#thermal-sensor-cells":
    const: 1

  '#address-cells':
    const: 1

  '#size-cells':
    const: 0

  vcc-supply:
    description: phandle to the regulator that provides the +VCC supply

  ti,extended-range-enable:
    description: Set to enable extended range temperature.
    type: boolean

required:
  - compatible
  - reg

patternProperties:
  "^channel@([0-2])$":
    type: object
    description: Represents channels of the device and their specific configuration.

    properties:
      reg:
        description: The channel number. 0 is local channel, 1-2 are remote channels.
        items:
          minimum: 0
          maximum: 2

      label:
        description: A descriptive name for this channel, like "ambient" or "psu".

      temperature-offset-millicelsius:
        description: Temperature offset to be added to or subtracted from remote temperature measurements.

    required:
      - reg

    additionalProperties: false

allOf:
  - if:
      not:
        properties:
          compatible:
            contains:
              enum:
                - adi,adt7461
                - adi,adt7461a
                - adi,adt7481
                - ti,tmp451
                - ti,tmp461
    then:
      properties:
        ti,extended-range-enable: false

  - if:
      properties:
        compatible:
          contains:
            enum:
              - dallas,max6646
              - dallas,max6647
              - dallas,max6649
              - dallas,max6657
              - dallas,max6658
              - dallas,max6659
              - dallas,max6695
              - dallas,max6696
    then:
      patternProperties:
        "^channel@([0-2])$":
          properties:
            temperature-offset-millicelsius: false

  - if:
      properties:
        compatible:
          contains:
            enum:
              - adi,adt7461
              - adi,adt7461a
              - adi,adt7481
              - onnn,nct1008
    then:
      patternProperties:
        "^channel@([0-2])$":
          properties:
            temperature-offset-millicelsius:
              maximum: 127750

  - if:
      properties:
        compatible:
          contains:
            enum:
              - adi,adm1032
              - dallas,max6680
              - dallas,max6681
              - gmt,g781
              - national,lm86
              - national,lm89
              - national,lm90
              - national,lm99
              - nxp,sa56004
              - winbond,w83l771
    then:
      patternProperties:
        "^channel@([0-2])$":
          properties:
            temperature-offset-millicelsius:
              maximum: 127875

  - if:
      properties:
        compatible:
          contains:
            enum:
              - ti,tmp451
              - ti,tmp461
    then:
      patternProperties:
        "^channel@([0-2])$":
          properties:
            temperature-offset-millicelsius:
              maximum: 127937

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>

    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        sensor@4c {
            compatible = "onnn,nct1008";
            reg = <0x4c>;
            vcc-supply = <&palmas_ldo6_reg>;
            interrupts = <4 IRQ_TYPE_LEVEL_LOW>;
            #thermal-sensor-cells = <1>;
        };
    };
  - |
    i2c {
      #address-cells = <1>;
      #size-cells = <0>;

      sensor@4c {
        compatible = "adi,adt7481";
        reg = <0x4c>;
        #address-cells = <1>;
        #size-cells = <0>;

        channel@0 {
          reg = <0x0>;
          label = "local";
        };

        channel@1 {
          reg = <0x1>;
          label = "front";
          temperature-offset-millicelsius = <4000>;
        };

        channel@2 {
          reg = <0x2>;
          label = "back";
          temperature-offset-millicelsius = <750>;
        };
      };
    };
