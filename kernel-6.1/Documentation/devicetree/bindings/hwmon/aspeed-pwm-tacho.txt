ASPEED AST2400/AST2500 PWM and Fan Tacho controller device driver

The ASPEED PWM controller can support upto 8 PWM outputs. The ASPEED Fan Tacho
controller can support upto 16 Fan tachometer inputs.

There can be upto 8 fans supported. Each fan can have one PWM output and
one/two Fan tach inputs.

Required properties for pwm-tacho node:
- #address-cells : should be 1.

- #size-cells : should be 1.

- #cooling-cells: should be 2.

- reg : address and length of the register set for the device.

- pinctrl-names : a pinctrl state named "default" must be defined.

- pinctrl-0 : phandle referencing pin configuration of the PWM ports.

- compatible : should be "aspeed,ast2400-pwm-tacho" for AST2400 and
	       "aspeed,ast2500-pwm-tacho" for AST2500.

- clocks : phandle to clock provider with the clock number in the second cell

- resets : phandle to reset controller with the reset number in the second cell

fan subnode format:
===================
Under fan subnode there can upto 8 child nodes, with each child node
representing a fan. If there are 8 fans each fan can have one PWM port and
one/two Fan tach inputs.
For PWM port can be configured cooling-levels to create cooling device.
Cooling device could be bound to a thermal zone for the thermal control.

Required properties for each child node:
- reg : should specify PWM source port.
	integer value in the range 0 to 7 with 0 indicating PWM port A and
	7 indicating PWM port H.

- cooling-levels: PWM duty cycle values in a range from 0 to 255
                  which correspond to thermal cooling states.

- aspeed,fan-tach-ch : should specify the Fan tach input channel.
                integer value in the range 0 through 15, with 0 indicating
		Fan tach channel 0 and 15 indicating Fan tach channel 15.
		Atleast one Fan tach input channel is required.

Examples:

pwm_tacho: pwmtachocontroller@1e786000 {
	#address-cells = <1>;
	#size-cells = <1>;
	#cooling-cells = <2>;
	reg = <0x1E786000 0x1000>;
	compatible = "aspeed,ast2500-pwm-tacho";
	clocks = <&syscon ASPEED_CLK_APB>;
	resets = <&syscon ASPEED_RESET_PWM>;
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_pwm0_default &pinctrl_pwm1_default>;

	fan@0 {
		reg = <0x00>;
		cooling-levels = /bits/ 8 <125 151 177 203 229 255>;
		aspeed,fan-tach-ch = /bits/ 8 <0x00>;
	};

	fan@1 {
		reg = <0x01>;
		aspeed,fan-tach-ch = /bits/ 8 <0x01 0x02>;
	};
};
