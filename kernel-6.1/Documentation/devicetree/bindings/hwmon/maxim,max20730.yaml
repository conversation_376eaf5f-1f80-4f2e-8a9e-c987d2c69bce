# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---

$id: http://devicetree.org/schemas/hwmon/maxim,max20730.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Maxim max20730

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description: |
  The MAX20730 is a fully integrated, highly efficient switching regulator
  with PMBus for applications operating from 4.5V to 16V and requiring
  up to 25A (max) load. This single-chip regulator provides extremely
  compact, high efficiency power-delivery solutions with high-precision
  output voltages and excellent transient response.

  Datasheets:
    https://datasheets.maximintegrated.com/en/ds/MAX20730.pdf
    https://datasheets.maximintegrated.com/en/ds/MAX20734.pdf
    https://datasheets.maximintegrated.com/en/ds/MAX20743.pdf

properties:
  compatible:
    enum:
      - maxim,max20730
      - maxim,max20734
      - maxim,max20743

  reg:
    maxItems: 1

  vout-voltage-divider:
    description: |
      If voltage divider present at vout, the voltage at voltage sensor pin
      will be scaled. The properties will convert the raw reading to a more
      meaningful number if voltage divider present. It has two numbers,
      the first number is the output resistor, the second number is the total
      resistance. Therefore, the adjusted vout is equal to
      Vout = Vout * output_resistance / total resistance.
    $ref: /schemas/types.yaml#/definitions/uint32-array
    minItems: 2
    maxItems: 2

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    i2c {
      #address-cells = <1>;
      #size-cells = <0>;

      max20730@10 {
        compatible = "maxim,max20730";
        reg = <0x10>;
        vout-voltage-divider = <1000 2000>; // vout would be scaled to 0.5
      };
    };
