max6697 properties

Required properties:
- compatible:
	Should be one of
		maxim,max6581
		maxim,max6602
		maxim,max6622
		maxim,max6636
		maxim,max6689
		maxim,max6693
		maxim,max6694
		maxim,max6697
		maxim,max6698
		maxim,max6699
- reg: I2C address

Optional properties:

- smbus-timeout-disable
	Set to disable SMBus timeout. If not specified, SMBus timeout will be
	enabled.
- extended-range-enable
	Only valid for MAX6581. Set to enable extended temperature range.
	Extended temperature will be disabled if not specified.
- beta-compensation-enable
	Only valid for MAX6693 and MX6694. Set to enable beta compensation on
	remote temperature channel 1.
	Beta compensation will be disabled if not specified.
- alert-mask
	Alert bit mask. Alert disabled for bits set.
	Select bit 0 for local temperature, bit 1..7 for remote temperatures.
	If not specified, alert will be enabled for all channels.
- over-temperature-mask
	Over-temperature bit mask. Over-temperature reporting disabled for
	bits set.
	Select bit 0 for local temperature, bit 1..7 for remote temperatures.
	If not specified, over-temperature reporting will be enabled for all
	channels.
- resistance-cancellation
	Boolean for all chips other than MAX6581. Set to enable resistance
	cancellation on remote temperature channel 1.
	For MAX6581, resistance cancellation enabled for all channels if
	specified as boolean, otherwise as per bit mask specified.
	Only supported for remote temperatures (bit 1..7).
	If not specified, resistance cancellation will be disabled for all
	channels.
- transistor-ideality
	For MAX6581 only. Two values; first is bit mask, second is ideality
	select value as per MAX6581 data sheet. Select bit 1..7 for remote
	channels.
	Transistor ideality will be initialized to default (1.008) if not
	specified.

Example:

temp-sensor@1a {
	compatible = "maxim,max6697";
	reg = <0x1a>;
	smbus-timeout-disable;
	resistance-cancellation;
	alert-mask = <0x72>;
	over-temperature-mask = <0x7f>;
};
