# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/hwmon/microchip,sparx5-temp.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Microchip Sparx5 Temperature Monitor

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Microchip Sparx5 embedded temperature monitor

properties:
  compatible:
    enum:
      - microchip,sparx5-temp

  reg:
    maxItems: 1

  clocks:
    items:
      - description: System reference clock

  '#thermal-sensor-cells':
    const: 0

required:
  - compatible
  - reg
  - clocks

additionalProperties: false

examples:
  - |
    tmon0: tmon@610508110 {
        compatible = "microchip,sparx5-temp";
        reg = <0x10508110 0xc>;
        #thermal-sensor-cells = <0>;
        clocks = <&sys_clk>;
    };
