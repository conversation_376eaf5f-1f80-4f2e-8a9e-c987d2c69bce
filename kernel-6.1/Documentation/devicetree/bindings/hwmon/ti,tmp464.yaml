# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/hwmon/ti,tmp464.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: TMP464 and TMP468 temperature sensors

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description: |
  ±0.0625°C Remote and Local temperature sensor
  https://www.ti.com/lit/ds/symlink/tmp464.pdf
  https://www.ti.com/lit/ds/symlink/tmp468.pdf

properties:
  compatible:
    enum:
      - ti,tmp464
      - ti,tmp468

  reg:
    maxItems: 1

  '#address-cells':
    const: 1

  '#size-cells':
    const: 0

required:
  - compatible
  - reg

additionalProperties: false

patternProperties:
  "^channel@([0-8])$":
    type: object
    description: |
      Represents channels of the device and their specific configuration.

    properties:
      reg:
        description: |
          The channel number. 0 is local channel, 1-8 are remote channels.
        items:
          minimum: 0
          maximum: 8

      label:
        description: |
          A descriptive name for this channel, like "ambient" or "psu".

      ti,n-factor:
        description: |
          The value (two's complement) to be programmed in the channel specific N correction register.
          For remote channels only.
        $ref: /schemas/types.yaml#/definitions/int32
        minimum: -128
        maximum: 127

    required:
      - reg

    additionalProperties: false

examples:
  - |
    i2c {
      #address-cells = <1>;
      #size-cells = <0>;

      sensor@4b {
        compatible = "ti,tmp464";
        reg = <0x4b>;
      };
    };
  - |
    i2c {
      #address-cells = <1>;
      #size-cells = <0>;

      sensor@4b {
        compatible = "ti,tmp464";
        reg = <0x4b>;
        #address-cells = <1>;
        #size-cells = <0>;

        channel@0 {
          reg = <0x0>;
          label = "local";
        };

        channel@1 {
          reg = <0x1>;
          ti,n-factor = <(-10)>;
          label = "external";
        };

        channel@2 {
          reg = <0x2>;
          ti,n-factor = <0x10>;
          label = "somelabel";
        };

        channel@3 {
          reg = <0x3>;
          status = "disabled";
        };
      };
    };
