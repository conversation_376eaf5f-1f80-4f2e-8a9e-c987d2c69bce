# SPDX-License-Identifier: GPL-2.0-only or BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/hwmon/microchip,mcp3021.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Microchip MCP3021 A/D converter

maintainers:
  - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - microchip,mcp3021
      - microchip,mcp3221

  reg:
    maxItems: 1

  reference-voltage-microvolt:
    description:
      VDD supply power and reference voltage

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        adc@4d {
            compatible = "microchip,mcp3021";
            reg = <0x4d>;

            reference-voltage-microvolt = <4500000>; /* 4.5 V */
        };
    };
