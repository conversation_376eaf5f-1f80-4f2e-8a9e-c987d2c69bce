# SPDX-License-Identifier: GPL-2.0-only or BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/hwmon/ti,tmp102.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: TMP102 temperature sensor

maintainers:
  - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - ti,tmp102

  interrupts:
    maxItems: 1

  reg:
    maxItems: 1

  "#thermal-sensor-cells":
    const: 1

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>

    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        sensor@48 {
            compatible = "ti,tmp102";
            reg = <0x48>;
            interrupt-parent = <&gpio7>;
            interrupts = <16 IRQ_TYPE_LEVEL_LOW>;
            #thermal-sensor-cells = <1>;
        };
    };
