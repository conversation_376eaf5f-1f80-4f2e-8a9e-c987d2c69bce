# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---

$id: http://devicetree.org/schemas/hwmon/ti,tps23861.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: TI TPS23861 PoE PSE

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  The TPS23861 is a IEEE 802.3at Quad Port Power-over-Ethernet PSE Controller.

  Datasheets:
  https://www.ti.com/lit/gpn/tps23861


properties:
  compatible:
    enum:
      - ti,tps23861

  reg:
    maxItems: 1

  shunt-resistor-micro-ohms:
    description: The value of curent sense resistor in microohms.
    default: 255000
    minimum: 250000
    maximum: 255000

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    i2c {
          #address-cells = <1>;
          #size-cells = <0>;

          tps23861@30 {
              compatible = "ti,tps23861";
              reg = <0x30>;
              shunt-resistor-micro-ohms = <255000>;
          };
    };
