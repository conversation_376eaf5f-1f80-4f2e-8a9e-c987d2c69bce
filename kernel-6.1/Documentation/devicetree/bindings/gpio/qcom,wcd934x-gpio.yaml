# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/gpio/qcom,wcd934x-gpio.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: WCD9340/WCD9341 GPIO controller

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description: |
  Qualcomm Technologies Inc WCD9340/WCD9341 Audio Codec has integrated
  gpio controller to control 5 gpios on the chip.

properties:
  compatible:
    enum:
      - qcom,wcd9340-gpio
      - qcom,wcd9341-gpio

  reg:
    maxItems: 1

  gpio-controller: true

  '#gpio-cells':
    const: 2

required:
  - compatible
  - reg
  - gpio-controller
  - "#gpio-cells"

additionalProperties: false

examples:
  - |
    wcdgpio: gpio@42 {
        compatible = "qcom,wcd9340-gpio";
        reg = <0x042 0x2>;
        gpio-controller;
        #gpio-cells = <2>;
    };

...
