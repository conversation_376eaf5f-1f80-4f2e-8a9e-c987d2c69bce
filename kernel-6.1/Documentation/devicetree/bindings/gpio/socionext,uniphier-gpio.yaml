# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/gpio/socionext,uniphier-gpio.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: UniPhier GPIO controller

maintainers:
  - <PERSON><PERSON><PERSON> <yamada.ma<PERSON><EMAIL>>

properties:
  $nodename:
    pattern: "^gpio@[0-9a-f]+$"

  compatible:
    const: socionext,uniphier-gpio

  reg:
    maxItems: 1

  gpio-controller: true

  "#gpio-cells":
    const: 2

  interrupt-controller: true

  "#interrupt-cells":
    description: |
      The first cell defines the interrupt number.
      The second cell bits[3:0] is used to specify trigger type as follows:
        1 = low-to-high edge triggered
        2 = high-to-low edge triggered
        4 = active high level-sensitive
        8 = active low level-sensitive
      Valid combinations are 1, 2, 3, 4, 8.
    const: 2

  ngpios:
    minimum: 0
    maximum: 512

  gpio-ranges: true

  gpio-ranges-group-names: true

  socionext,interrupt-ranges:
    description: |
      Specifies an interrupt number mapping between this GPIO controller and
      its interrupt parent, in the form of arbitrary number of
      <child-interrupt-base parent-interrupt-base length> triplets.
    $ref: /schemas/types.yaml#/definitions/uint32-matrix

patternProperties:
  "^.+-hog(-[0-9]+)?$":
    type: object
    properties:
      gpio-hog: true
      gpios: true
      input: true
      output-high: true
      output-low: true
      line-name: true

    required:
      - gpio-hog
      - gpios

    additionalProperties: false

required:
  - compatible
  - reg
  - gpio-controller
  - "#gpio-cells"
  - interrupt-controller
  - "#interrupt-cells"
  - ngpios
  - gpio-ranges
  - socionext,interrupt-ranges

additionalProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>
    #include <dt-bindings/gpio/uniphier-gpio.h>

    gpio: gpio@55000000 {
        compatible = "socionext,uniphier-gpio";
        reg = <0x55000000 0x200>;
        interrupt-parent = <&aidet>;
        interrupt-controller;
        #interrupt-cells = <2>;
        gpio-controller;
        #gpio-cells = <2>;
        gpio-ranges = <&pinctrl 0 0 0>;
        gpio-ranges-group-names = "gpio_range";
        ngpios = <248>;
        socionext,interrupt-ranges = <0 48 16>, <16 154 5>, <21 217 3>;
    };

    // Consumer:
    // Please note UNIPHIER_GPIO_PORT(29, 4) represents PORT294 in the SoC
    // document. Unfortunately, only the one's place is octal in the port
    // numbering. (That is, PORT 8, 9, 18, 19, 28, 29, ... do not exist.)
    // UNIPHIER_GPIO_PORT() is a helper macro to calculate 29 * 8 + 4.
    sdhci0_pwrseq {
        compatible = "mmc-pwrseq-emmc";
        reset-gpios = <&gpio UNIPHIER_GPIO_PORT(29, 4) GPIO_ACTIVE_LOW>;
    };
