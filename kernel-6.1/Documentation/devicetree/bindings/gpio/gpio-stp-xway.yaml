# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/gpio/gpio-stp-xway.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Lantiq SoC Serial To Parallel (STP) GPIO controller

description: |
  The Serial To Parallel (STP) is found on MIPS based Lantiq socs. It is a
  peripheral controller used to drive external shift register cascades. At most
  3 groups of 8 bits can be driven. The hardware is able to allow the DSL modem
  and Ethernet PHYs to drive some bytes of the cascade automatically.

maintainers:
  - <PERSON> <<EMAIL>>

properties:
  $nodename:
    pattern: "^gpio@[0-9a-f]+$"

  compatible:
    const: lantiq,gpio-stp-xway

  reg:
    maxItems: 1

  gpio-controller: true

  "#gpio-cells":
    description:
      The first cell is the pin number and the second cell is used to specify
      consumer flags.
    const: 2

  lantiq,shadow:
    description:
      The default value that we shall assume as already set on the
      shift register cascade.
    $ref: /schemas/types.yaml#/definitions/uint32
    minimum: 0x000000
    maximum: 0xffffff

  lantiq,groups:
    description:
      Set the 3 bit mask to select which of the 3 groups are enabled
      in the shift register cascade.
    $ref: /schemas/types.yaml#/definitions/uint32
    minimum: 0x0
    maximum: 0x7

  lantiq,dsl:
    description:
      The dsl core can control the 2 LSBs of the gpio cascade. This 2 bit
      property can enable this feature.
    $ref: /schemas/types.yaml#/definitions/uint32
    minimum: 0x0
    maximum: 0x3

  lantiq,rising:
    description:
      Use rising instead of falling edge for the shift register.
    type: boolean

patternProperties:
  "^lantiq,phy[1-4]$":
    description:
      The gphy core can control 3 bits of the gpio cascade. In the xRX200 family
      phy[1-2] are available, in xRX330 phy[1-3] and in XRX330 phy[1-4].
    $ref: /schemas/types.yaml#/definitions/uint32
    minimum: 0x0
    maximum: 0x7

required:
  - compatible
  - reg
  - gpio-controller
  - "#gpio-cells"

additionalProperties: false

examples:
  - |
    gpio@e100bb0 {
        compatible = "lantiq,gpio-stp-xway";
        reg = <0xE100BB0 0x40>;
        #gpio-cells = <2>;
        gpio-controller;

        pinctrl-0 = <&stp_pins>;
        pinctrl-names = "default";

        lantiq,shadow = <0xffffff>;
        lantiq,groups = <0x7>;
        lantiq,dsl = <0x3>;
        lantiq,phy1 = <0x7>;
        lantiq,phy2 = <0x7>;
    };
...
