Lantiq SoC External Bus memory mapped GPIO controller

By attaching hardware latches to the EBU it is possible to create output
only gpios. This driver configures a special memory address, which when
written to outputs 16 bit to the latches.

The node describing the memory mapped GPIOs needs to be a child of the node
describing the "lantiq,localbus".

Required properties:
- compatible : Should be "lantiq,gpio-mm-lantiq"
- reg : Address and length of the register set for the device
- #gpio-cells : Should be two.  The first cell is the pin number and
  the second cell is used to specify optional parameters (currently
  unused).
- gpio-controller : Marks the device node as a gpio controller.

Optional properties:
- lantiq,shadow : The default value that we shall assume as already set on the
  shift register cascade.

Example:

localbus@0 {
	#address-cells = <2>;
	#size-cells = <1>;
	ranges = <0 0 0x0 0x3ffffff /* addrsel0 */
		1 0 0x4000000 0x4000010>; /* addsel1 */
	compatible = "lantiq,localbus", "simple-bus";

	gpio_mm0: gpio@4000000 {
		compatible = "lantiq,gpio-mm";
		reg = <1 0x0 0x10>;
		gpio-controller;
		#gpio-cells = <2>;
		lantiq,shadow = <0x77f>
	};
}
