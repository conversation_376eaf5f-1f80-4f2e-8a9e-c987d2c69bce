GPIO driver for Maxim MAX3191x industrial serializer

Required properties:
 - compatible:		Must be one of:
			"maxim,max31910"
			"maxim,max31911"
			"maxim,max31912"
			"maxim,max31913"
			"maxim,max31953"
			"maxim,max31963"
 - reg: 		Chip select number.
 - gpio-controller:	Marks the device node as a GPIO controller.
 - #gpio-cells: 	Should be two. For consumer use see gpio.txt.

Optional properties:
 - #daisy-chained-devices:
			Number of chips in the daisy-chain (default is 1).
 - maxim,modesel-gpios: GPIO pins to configure modesel of each chip.
			The number of GPIOs must equal "#daisy-chained-devices"
			(if each chip is driven by a separate pin) or 1
			(if all chips are wired to the same pin).
 - maxim,fault-gpios: 	GPIO pins to read fault of each chip.
			The number of GPIOs must equal "#daisy-chained-devices"
			or 1.
 - maxim,db0-gpios:	GPIO pins to configure debounce of each chip.
			The number of GPIOs must equal "#daisy-chained-devices"
			or 1.
 - maxim,db1-gpios:	GPIO pins to configure debounce of each chip.
			The number of GPIOs must equal "maxim,db0-gpios".
 - maxim,modesel-8bit:	Boolean whether the modesel pin of the chips is
			pulled high (8-bit mode).  Use this if the modesel pin
			is hardwired and consequently "maxim,modesel-gpios"
			cannot be specified.  By default if neither this nor
			"maxim,modesel-gpios" is given, the driver assumes
			that modesel is pulled low (16-bit mode).
 - maxim,ignore-undervoltage:
			Boolean whether to ignore undervoltage alarms signaled
			by the "maxim,fault-gpios" or by the status byte
			(in 16-bit mode).  Use this if the chips are powered
			through 5VOUT instead of VCC24V, in which case they
			will constantly signal undervoltage.

For other required and optional properties of SPI slave nodes please refer to
../spi/spi-bus.txt.

Example:
	gpio@0 {
		compatible = "maxim,max31913";
		reg = <0>;
		gpio-controller;
		#gpio-cells = <2>;

		maxim,modesel-gpios = <&gpio2 23>;
		maxim,fault-gpios   = <&gpio2 24 GPIO_ACTIVE_LOW>;
		maxim,db0-gpios     = <&gpio2 25>;
		maxim,db1-gpios     = <&gpio2 26>;

		spi-max-frequency = <25000000>;
	};
