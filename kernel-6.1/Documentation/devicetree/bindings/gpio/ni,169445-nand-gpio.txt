Bindings for the National Instruments 169445 GPIO NAND controller

The 169445 GPIO NAND controller has two memory mapped GPIO registers, one
for input (the ready signal) and one for output (control signals).  It is
intended to be used with the GPIO NAND driver.

Required properties:
	- compatible: should be "ni,169445-nand-gpio"
	- reg-names: must contain
		"dat" - data register
	- reg: address + size pairs describing the GPIO register sets;
		order must correspond with the order of entries in reg-names
	- #gpio-cells: must be set to 2. The first cell is the pin number and
			the second cell is used to specify the gpio polarity:
			0 = active high
			1 = active low
	- gpio-controller: Marks the device node as a gpio controller.

Optional properties:
	- no-output: disables driving output on the pins

Examples:
	gpio1: nand-gpio-out@1f300010 {
		compatible = "ni,169445-nand-gpio";
		reg = <0x1f300010 0x4>;
		reg-names = "dat";
		gpio-controller;
		#gpio-cells = <2>;
	};

	gpio2: nand-gpio-in@1f300014 {
		compatible = "ni,169445-nand-gpio";
		reg = <0x1f300014 0x4>;
		reg-names = "dat";
		gpio-controller;
		#gpio-cells = <2>;
		no-output;
	};
