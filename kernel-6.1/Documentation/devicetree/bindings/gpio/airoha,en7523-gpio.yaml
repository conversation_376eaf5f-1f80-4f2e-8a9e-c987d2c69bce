# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/gpio/airoha,en7523-gpio.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Airoha EN7523 GPIO controller

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Airoha's GPIO controller on their ARM EN7523 SoCs consists of two banks of 32
  GPIOs.

properties:
  $nodename:
    pattern: "^gpio@[0-9a-f]+$"

  compatible:
    items:
      - const: airoha,en7523-gpio

  reg:
    description: |
      The first tuple points to the input register.
      The second and third tuple point to the direction registers
      The fourth tuple points to the output register
    maxItems: 4

  "#gpio-cells":
    const: 2

  gpio-controller: true

required:
  - compatible
  - reg
  - "#gpio-cells"
  - gpio-controller

additionalProperties: false

examples:
  - |
    gpio0: gpio@1fbf0200 {
        compatible = "airoha,en7523-gpio";
        reg = <0x1fbf0204 0x4>,
              <0x1fbf0200 0x4>,
              <0x1fbf0220 0x4>,
              <0x1fbf0214 0x4>;
        gpio-controller;
        #gpio-cells = <2>;
    };

    gpio1: gpio@1fbf0270 {
        compatible = "airoha,en7523-gpio";
        reg = <0x1fbf0270 0x4>,
              <0x1fbf0260 0x4>,
              <0x1fbf0264 0x4>,
              <0x1fbf0278 0x4>;
        gpio-controller;
        #gpio-cells = <2>;
    };

...
