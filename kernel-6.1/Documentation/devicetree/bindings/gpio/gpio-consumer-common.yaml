# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/gpio/gpio-consumer-common.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Common GPIO lines

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>

description:
  Pay attention to using proper GPIO flag (e.g. GPIO_ACTIVE_LOW) for the GPIOs
  using inverted signal (e.g. RESETN).

select: true

properties:
  enable-gpios:
    maxItems: 1
    description:
      GPIO connected to the enable control pin.

  reset-gpios:
    description:
      GPIO (or GPIOs for power sequence) connected to the device reset pin
      (e.g. RESET or RESETN).

  powerdown-gpios:
    maxItems: 1
    description:
      GPIO connected to the power down pin (hardware power down or power cut,
      e.g. PD or PWDN).

  pwdn-gpios:
    maxItems: 1
    description: Use powerdown-gpios
    deprecated: true

  wakeup-gpios:
    maxItems: 1
    description:
      GPIO connected to the pin waking up the device from suspend or other
      power-saving modes.

allOf:
  - if:
      properties:
        compatible:
          contains:
            enum:
              - mmc-pwrseq-simple
    then:
      properties:
        reset-gpios:
          minItems: 1
          maxItems: 32
    else:
      properties:
        reset-gpios:
          maxItems: 1

additionalProperties: true
