Synopsys GPIO via CREG (Control REGisters) driver

Required properties:
- compatible : "snps,creg-gpio-hsdk" or "snps,creg-gpio-axs10x".
- reg : Exactly one register range with length 0x4.
- #gpio-cells : Since the generic GPIO binding is used, the
  amount of cells must be specified as 2. The first cell is the
  pin number, the second cell is used to specify optional parameters:
  See "gpio-specifier" in .../devicetree/bindings/gpio/gpio.txt.
- gpio-controller : Marks the device node as a GPIO controller.
- ngpios: Number of GPIO pins.

Example:

gpio: gpio@f00014b0 {
	compatible = "snps,creg-gpio-hsdk";
	reg = <0xf00014b0 0x4>;
	gpio-controller;
	#gpio-cells = <2>;
	ngpios = <2>;
};
