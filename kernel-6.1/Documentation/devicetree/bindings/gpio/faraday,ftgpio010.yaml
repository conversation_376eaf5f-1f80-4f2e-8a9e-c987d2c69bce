# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/gpio/faraday,ftgpio010.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Faraday Technology FTGPIO010 GPIO Controller

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    oneOf:
      - items:
          - const: cortina,gemini-gpio
          - const: faraday,ftgpio010
      - items:
          - const: moxa,moxart-gpio
          - const: faraday,ftgpio010
      - const: faraday,ftgpio010

  reg:
    maxItems: 1

  resets:
    maxItems: 1

  clocks:
    maxItems: 1

  interrupts:
    maxItems: 1
    description: Should contain the interrupt line for the GPIO block

  gpio-controller: true
  "#gpio-cells":
    const: 2

  interrupt-controller: true
  "#interrupt-cells":
    const: 2

required:
  - compatible
  - reg
  - interrupts
  - "#gpio-cells"
  - interrupt-controller
  - "#interrupt-cells"

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    gpio@4d000000 {
      compatible = "cortina,gemini-gpio", "faraday,ftgpio010";
      reg = <0x4d000000 0x100>;
      interrupts = <22 IRQ_TYPE_LEVEL_HIGH>;
      gpio-controller;
      #gpio-cells = <2>;
      interrupt-controller;
      #interrupt-cells = <2>;
    };
