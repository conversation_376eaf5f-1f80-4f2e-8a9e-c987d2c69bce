Generic Parallel-in/Serial-out Shift Register GPIO Driver

This binding describes generic parallel-in/serial-out shift register
devices that can be used for GPI (General Purpose Input). This includes
SN74165 serial-out shift registers and the SN65HVS88x series of
industrial serializers.

Required properties:
 - compatible		: Should be "pisosr-gpio".
 - gpio-controller	: Marks the device node as a GPIO controller.
 - #gpio-cells		: Should be two. For consumer use see gpio.txt.

Optional properties:
 - ngpios		: Number of used GPIO lines (0..n-1), default is 8.
 - load-gpios		: GPIO pin specifier attached to load enable, this
			  pin is pulsed before reading from the device to
			  load input pin values into the device.

For other required and optional properties of SPI slave
nodes please refer to ../spi/spi-bus.txt.

Example:

	gpio@0 {
		compatible = "ti,sn65hvs882", "pisosr-gpio";
		gpio-controller;
		#gpio-cells = <2>;

		load-gpios = <&gpio2 23 GPIO_ACTIVE_LOW>;

		reg = <0>;
		spi-max-frequency = <1000000>;
		spi-cpol;
	};
