Binding for Qualcomm Atheros AR7xxx/AR9xxx GPIO controller

Required properties:
- compatible: has to be "qca,<soctype>-gpio" and one of the following
  fallbacks:
  - "qca,ar7100-gpio"
  - "qca,ar9340-gpio"
- reg: Base address and size of the controllers memory area
- gpio-controller : Marks the device node as a GPIO controller.
- #gpio-cells : Should be two. The first cell is the pin number and the
  second cell is used to specify optional parameters.
- ngpios: Should be set to the number of GPIOs available on the SoC.

Optional properties:
- interrupts: Interrupt specifier for the controllers interrupt.
- interrupt-controller : Identifies the node as an interrupt controller
- #interrupt-cells : Specifies the number of cells needed to encode interrupt
		     source, should be 2

Please refer to interrupts.txt in this directory for details of the common
Interrupt Controllers bindings used by client devices.

Example:

	gpio@18040000 {
		compatible = "qca,ar9132-gpio", "qca,ar7100-gpio";
		reg = <0x18040000 0x30>;
		interrupts = <2>;

		ngpios = <22>;

		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};
