# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: "http://devicetree.org/schemas/gpio/x-powers,axp209-gpio.yaml#"
$schema: "http://devicetree.org/meta-schemas/core.yaml#"

title: X-Powers AXP209 GPIO

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

properties:
  "#gpio-cells":
    const: 2
    description: >
      The first cell is the pin number and the second is the GPIO flags.

  compatible:
    oneOf:
      - enum:
          - x-powers,axp209-gpio
          - x-powers,axp221-gpio
          - x-powers,axp813-gpio
      - items:
          - enum:
              - x-powers,axp223-gpio
              - x-powers,axp809-gpio
          - const: x-powers,axp221-gpio
      - items:
          - const: x-powers,axp803-gpio
          - const: x-powers,axp813-gpio

  gpio-controller: true

patternProperties:
  "^.*-pins?$":
    $ref: /schemas/pinctrl/pinmux-node.yaml#

    properties:
      pins:
        items:
          enum:
            - GPIO0
            - GPIO1
            - GPIO2

      function:
        enum:
          - adc
          - ldo
          - gpio_in
          - gpio_out

required:
  - compatible
  - "#gpio-cells"
  - gpio-controller

additionalProperties: false

...
