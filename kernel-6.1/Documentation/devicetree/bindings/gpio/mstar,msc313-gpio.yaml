# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/gpio/mstar,msc313-gpio.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: MStar/SigmaStar GPIO controller

maintainers:
  - <PERSON> <<EMAIL>>

properties:
  $nodename:
    pattern: "^gpio@[0-9a-f]+$"

  compatible:
    enum:
      - mstar,msc313-gpio
      - sstar,ssd20xd-gpio

  reg:
    maxItems: 1

  gpio-controller: true

  "#gpio-cells":
    const: 2

  gpio-ranges: true

  interrupt-controller: true

  "#interrupt-cells":
    const: 2

required:
  - compatible
  - reg
  - gpio-controller
  - "#gpio-cells"
  - interrupt-controller
  - "#interrupt-cells"

additionalProperties: false

examples:
  - |
    #include <dt-bindings/gpio/msc313-gpio.h>

    gpio: gpio@207800 {
      compatible = "mstar,msc313-gpio";
      #gpio-cells = <2>;
      reg = <0x207800 0x200>;
      gpio-controller;
      gpio-ranges = <&pinctrl 0 36 22>,
                    <&pinctrl 22 63 4>,
                    <&pinctrl 26 68 6>;
      #interrupt-cells = <2>;
      interrupt-controller;
      interrupt-parent = <&intc_fiq>;
    };
