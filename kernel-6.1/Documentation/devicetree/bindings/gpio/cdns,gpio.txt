Cadence GPIO controller bindings

Required properties:
- compatible: should be "cdns,gpio-r1p02".
- reg: the register base address and size.
- #gpio-cells: should be 2.
	* first cell is the GPIO number.
	* second cell specifies the GPIO flags, as defined in
		<dt-bindings/gpio/gpio.h>. Only the GPIO_ACTIVE_HIGH
		and GPIO_ACTIVE_LOW flags are supported.
- gpio-controller: marks the device as a GPIO controller.
- clocks: should contain one entry referencing the peripheral clock driving
	the GPIO controller.

Optional properties:
- ngpios: integer number of gpio lines supported by this controller, up to 32.
- interrupts: interrupt specifier for the controllers interrupt.
- interrupt-controller: marks the device as an interrupt controller. When
	defined, interrupts, interrupt-parent and #interrupt-cells
	are required.
- interrupt-cells: should be 2.
	* first cell is the GPIO number you want to use as an IRQ source.
	* second cell specifies the IRQ type, as defined in
		<dt-bindings/interrupt-controller/irq.h>.
		Currently only level sensitive IRQs are supported.


Example:
	gpio0: gpio-controller@fd060000 {
		compatible = "cdns,gpio-r1p02";
		reg =<0xfd060000 0x1000>;

		clocks = <&gpio_clk>;

		interrupt-parent = <&gic>;
		interrupts = <0 5 IRQ_TYPE_LEVEL_HIGH>;

		gpio-controller;
		#gpio-cells = <2>;

		interrupt-controller;
		#interrupt-cells = <2>;
	};
