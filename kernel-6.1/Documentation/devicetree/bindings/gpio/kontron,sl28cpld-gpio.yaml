# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/gpio/kontron,sl28cpld-gpio.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: GPIO driver for the sl28cpld board management controller

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  This module is part of the sl28cpld multi-function device. For more
  details see ../mfd/kontron,sl28cpld.yaml.

  There are three flavors of the GPIO controller, one full featured
  input/output with interrupt support (kontron,sl28cpld-gpio), one
  output-only (kontron,sl28-gpo) and one input-only (kontron,sl28-gpi).

  Each controller supports 8 GPIO lines.

properties:
  compatible:
    enum:
      - kontron,sl28cpld-gpio
      - kontron,sl28cpld-gpi
      - kontron,sl28cpld-gpo

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  "#interrupt-cells":
    const: 2

  interrupt-controller: true

  "#gpio-cells":
    const: 2

  gpio-controller: true

  gpio-line-names:
    minItems: 1
    maxItems: 8

required:
  - compatible
  - "#gpio-cells"
  - gpio-controller

additionalProperties: false
