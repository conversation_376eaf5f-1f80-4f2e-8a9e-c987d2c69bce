# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/gpio/nvidia,tegra186-gpio.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: NVIDIA Tegra GPIO Controller (Tegra186 and later)

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

description: |
  Tegra186 contains two GPIO controllers; a main controller and an "AON"
  controller. This binding document applies to both controllers. The register
  layouts for the controllers share many similarities, but also some
  significant differences. Hence, this document describes closely related but
  different bindings and compatible values.

  The Tegra186 GPIO controller allows software to set the IO direction of,
  and read/write the value of, numerous GPIO signals. Routing of GPIO signals
  to package balls is under the control of a separate pin controller hardware
  block. Two major sets of registers exist:

    a) Security registers, which allow configuration of allowed access to the
       GPIO register set. These registers exist in a single contiguous block
       of physical address space. The size of this block, and the security
       features available, varies between the different GPIO controllers.

       Access to this set of registers is not necessary in all circumstances.
       Code that wishes to configure access to the GPIO registers needs access
       to these registers to do so. Code which simply wishes to read or write
       GPIO data does not need access to these registers.

    b) GPIO registers, which allow manipulation of the GPIO signals. In some
       GPIO controllers, these registers are exposed via multiple "physical
       aliases" in address space, each of which access the same underlying
       state. See the hardware documentation for rationale. Any particular
       GPIO client is expected to access just one of these physical aliases.

    Tegra HW documentation describes a unified naming convention for all GPIOs
    implemented by the SoC. Each GPIO is assigned to a port, and a port may
    control a number of GPIOs. Thus, each GPIO is named according to an
    alphabetical port name and an integer GPIO name within the port. For
    example, GPIO_PA0, GPIO_PN6, or GPIO_PCC3.

    The number of ports implemented by each GPIO controller varies. The number
    of implemented GPIOs within each port varies. GPIO registers within a
    controller are grouped and laid out according to the port they affect.

    The mapping from port name to the GPIO controller that implements that
    port, and the mapping from port name to register offset within a
    controller, are both extremely non-linear. The header file
    <dt-bindings/gpio/tegra186-gpio.h> describes the port-level mapping. In
    that file, the naming convention for ports matches the HW documentation.
    The values chosen for the names are alphabetically sorted within a
    particular controller. Drivers need to map between the DT GPIO IDs and HW
    register offsets using a lookup table.

    Each GPIO controller can generate a number of interrupt signals. Each
    signal represents the aggregate status for all GPIOs within a set of
    ports. Thus, the number of interrupt signals generated by a controller
    varies as a rough function of the number of ports it implements. Note
    that the HW documentation refers to both the overall controller HW
    module and the sets-of-ports as "controllers".

    Each GPIO controller in fact generates multiple interrupts signals for
    each set of ports. Each GPIO may be configured to feed into a specific
    one of the interrupt signals generated by a set-of-ports. The intent is
    for each generated signal to be routed to a different CPU, thus allowing
    different CPUs to each handle subsets of the interrupts within a port.
    The status of each of these per-port-set signals is reported via a
    separate register. Thus, a driver needs to know which status register to
    observe. This binding currently defines no configuration mechanism for
    this. By default, drivers should use register
    GPIO_${port}_INTERRUPT_STATUS_G1_0. Future revisions to the binding could
    define a property to configure this.

properties:
  compatible:
    enum:
      - nvidia,tegra186-gpio
      - nvidia,tegra186-gpio-aon
      - nvidia,tegra194-gpio
      - nvidia,tegra194-gpio-aon
      - nvidia,tegra234-gpio
      - nvidia,tegra234-gpio-aon

  reg-names:
    items:
      - const: security
      - const: gpio
    minItems: 1

  reg:
    items:
      - description: Security configuration registers.
      - description: |
          GPIO control registers. This may cover either:

            a) The single physical alias that this OS should use.
            b) All physical aliases that exist in the controller. This is
               appropriate when the OS is responsible for managing assignment
               of the physical aliases.
    minItems: 1

  interrupts:
    description: The interrupt outputs from the HW block, one per set of
      ports, in the order the HW manual describes them. The number of entries
      required varies depending on compatible value.

  gpio-controller: true

  "#gpio-cells":
    description: |
      Indicates how many cells are used in a consumer's GPIO specifier. In the
      specifier:

        - The first cell is the pin number.
          See <dt-bindings/gpio/tegra186-gpio.h>.
        - The second cell contains flags:
          - Bit 0 specifies polarity
            - 0: Active-high (normal).
            - 1: Active-low (inverted).
    const: 2

  interrupt-controller: true

  "#interrupt-cells":
    description: |
      Indicates how many cells are used in a consumer's interrupt specifier.
      In the specifier:

        - The first cell is the GPIO number.
          See <dt-bindings/gpio/tegra186-gpio.h>.
        - The second cell is contains flags:
          - Bits [3:0] indicate trigger type and level:
            - 1: Low-to-high edge triggered.
            - 2: High-to-low edge triggered.
            - 4: Active high level-sensitive.
            - 8: Active low level-sensitive.

            Valid combinations are 1, 2, 3, 4, 8.
    const: 2

allOf:
  - if:
      properties:
        compatible:
          contains:
            enum:
              - nvidia,tegra186-gpio
              - nvidia,tegra194-gpio
              - nvidia,tegra234-gpio
    then:
      properties:
        interrupts:
          minItems: 6
          maxItems: 48

  - if:
      properties:
        compatible:
          contains:
            enum:
              - nvidia,tegra186-gpio-aon
              - nvidia,tegra194-gpio-aon
              - nvidia,tegra234-gpio-aon
    then:
      properties:
        interrupts:
          minItems: 1
          maxItems: 4

required:
  - compatible
  - reg
  - reg-names
  - interrupts

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>

    gpio@2200000 {
        compatible = "nvidia,tegra186-gpio";
        reg-names = "security", "gpio";
        reg = <0x2200000 0x10000>,
              <0x2210000 0x10000>;
        interrupts = <0  47 IRQ_TYPE_LEVEL_HIGH>,
                     <0  50 IRQ_TYPE_LEVEL_HIGH>,
                     <0  53 IRQ_TYPE_LEVEL_HIGH>,
                     <0  56 IRQ_TYPE_LEVEL_HIGH>,
                     <0  59 IRQ_TYPE_LEVEL_HIGH>,
                     <0 180 IRQ_TYPE_LEVEL_HIGH>;
        gpio-controller;
        #gpio-cells = <2>;
        interrupt-controller;
        #interrupt-cells = <2>;
    };

    gpio@c2f0000 {
        compatible = "nvidia,tegra186-gpio-aon";
        reg-names = "security", "gpio";
        reg = <0xc2f0000 0x1000>,
              <0xc2f1000 0x1000>;
        interrupts = <0 60 IRQ_TYPE_LEVEL_HIGH>;
        gpio-controller;
        #gpio-cells = <2>;
        interrupt-controller;
        #interrupt-cells = <2>;
    };
