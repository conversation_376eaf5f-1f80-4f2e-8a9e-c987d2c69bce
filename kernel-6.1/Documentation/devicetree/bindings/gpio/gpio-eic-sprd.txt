Spreadtrum EIC controller bindings

The EIC is the abbreviation of external interrupt controller, which can
be used only in input mode. The Spreadtrum platform has 2 EIC controllers,
one is in digital chip, and another one is in PMIC. The digital chip EIC
controller contains 4 sub-modules: EIC-debounce, EIC-latch, EIC-async and
EIC-sync. But the PMIC EIC controller contains only one EIC-debounce sub-
module.

The EIC-debounce sub-module provides up to 8 source input signal
connections. A debounce mechanism is used to capture the input signals'
stable status (millisecond resolution) and a single-trigger mechanism
is introduced into this sub-module to enhance the input event detection
reliability. In addition, this sub-module's clock can be shut off
automatically to reduce power dissipation. Moreover the debounce range
is from 1ms to 4s with a step size of 1ms. The input signal will be
ignored if it is asserted for less than 1 ms.

The EIC-latch sub-module is used to latch some special power down signals
and generate interrupts, since the EIC-latch does not depend on the APB
clock to capture signals.

The EIC-async sub-module uses a 32kHz clock to capture the short signals
(microsecond resolution) to generate interrupts by level or edge trigger.

The EIC-sync is similar with GPIO's input function, which is a synchronized
signal input register. It can generate interrupts by level or edge trigger
when detecting input signals.

Required properties:
- compatible: Should be one of the following:
  "sprd,sc9860-eic-debounce",
  "sprd,sc9860-eic-latch",
  "sprd,sc9860-eic-async",
  "sprd,sc9860-eic-sync",
  "sprd,sc2731-eic".
- reg: Define the base and range of the I/O address space containing
  the GPIO controller registers.
- gpio-controller: Marks the device node as a GPIO controller.
- #gpio-cells: Should be <2>. The first cell is the gpio number and
  the second cell is used to specify optional parameters.
- interrupt-controller: Marks the device node as an interrupt controller.
- #interrupt-cells: Should be <2>. Specifies the number of cells needed
  to encode interrupt source.
- interrupts: Should be the port interrupt shared by all the gpios.

Example:
	eic_debounce: gpio@40210000 {
		compatible = "sprd,sc9860-eic-debounce";
		reg = <0 0x40210000 0 0x80>;
		gpio-controller;
		#gpio-cells = <2>;
		interrupt-controller;
		#interrupt-cells = <2>;
		interrupts = <GIC_SPI 52 IRQ_TYPE_LEVEL_HIGH>;
	};

	eic_latch: gpio@40210080 {
		compatible = "sprd,sc9860-eic-latch";
		reg = <0 0x40210080 0 0x20>;
		gpio-controller;
		#gpio-cells = <2>;
		interrupt-controller;
		#interrupt-cells = <2>;
		interrupts = <GIC_SPI 52 IRQ_TYPE_LEVEL_HIGH>;
	};

	eic_async: gpio@402100a0 {
		compatible = "sprd,sc9860-eic-async";
		reg = <0 0x402100a0 0 0x20>;
		gpio-controller;
		#gpio-cells = <2>;
		interrupt-controller;
		#interrupt-cells = <2>;
		interrupts = <GIC_SPI 52 IRQ_TYPE_LEVEL_HIGH>;
	};

	eic_sync: gpio@402100c0 {
		compatible = "sprd,sc9860-eic-sync";
		reg = <0 0x402100c0 0 0x20>;
		gpio-controller;
		#gpio-cells = <2>;
		interrupt-controller;
		#interrupt-cells = <2>;
		interrupts = <GIC_SPI 52 IRQ_TYPE_LEVEL_HIGH>;
	};

	pmic_eic: gpio@300 {
		compatible = "sprd,sc2731-eic";
		reg = <0x300>;
		interrupt-parent = <&sc2731_pmic>;
		interrupts = <5 IRQ_TYPE_LEVEL_HIGH>;
		gpio-controller;
		#gpio-cells = <2>;
		interrupt-controller;
		#interrupt-cells = <2>;
	};
