# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/gpio/rockchip,gpio-bank.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Rockchip GPIO bank

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - rockchip,gpio-bank
      - rockchip,rk3188-gpio-bank0

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks:
    minItems: 1
    items:
      - description: APB interface clock source
      - description: GPIO debounce reference clock source

  gpio-ranges: true

  gpio-controller: true

  gpio-line-names: true

  "#gpio-cells":
    const: 2

  interrupt-controller: true

  "#interrupt-cells":
    const: 2

required:
  - compatible
  - reg
  - interrupts
  - clocks
  - gpio-controller
  - "#gpio-cells"
  - interrupt-controller
  - "#interrupt-cells"

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    pinctrl: pinctrl {
      #address-cells = <1>;
      #size-cells = <1>;
      ranges;

      gpio0: gpio@2000a000 {
        compatible = "rockchip,rk3188-gpio-bank0";
        reg = <0x2000a000 0x100>;
        interrupts = <GIC_SPI 54 IRQ_TYPE_LEVEL_HIGH>;
        clocks = <&clk_gates8 9>;

        gpio-controller;
        #gpio-cells = <2>;

        interrupt-controller;
        #interrupt-cells = <2>;
      };

      gpio1: gpio@2003c000 {
        compatible = "rockchip,gpio-bank";
        reg = <0x2003c000 0x100>;
        interrupts = <GIC_SPI 55 IRQ_TYPE_LEVEL_HIGH>;
        clocks = <&clk_gates8 10>;

        gpio-controller;
        #gpio-cells = <2>;

        interrupt-controller;
        #interrupt-cells = <2>;
      };
    };
