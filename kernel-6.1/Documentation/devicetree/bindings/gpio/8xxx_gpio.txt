GPIO controllers on MPC8xxx SoCs

This is for the non-QE/CPM/GUTs GPIO controllers as found on
8349, 8572, 8610 and compatible.

Every GPIO controller node must have #gpio-cells property defined,
this information will be used to translate gpio-specifiers.
See bindings/gpio/gpio.txt for details of how to specify GPIO
information for devices.

The GPIO module usually is connected to the SoC's internal interrupt
controller, see bindings/interrupt-controller/interrupts.txt (the
interrupt client nodes section) for details how to specify this GPIO
module's interrupt.

The GPIO module may serve as another interrupt controller (cascaded to
the SoC's internal interrupt controller).  See the interrupt controller
nodes section in bindings/interrupt-controller/interrupts.txt for
details.

Required properties:
- compatible:		"fsl,<chip>-gpio" followed by "fsl,mpc8349-gpio"
			for 83xx, "fsl,mpc8572-gpio" for 85xx, or
			"fsl,mpc8610-gpio" for 86xx.
- #gpio-cells:		Should be two. The first cell is the pin number
			and the second cell is used to specify optional
			parameters (currently unused).
- interrupts:		Interrupt mapping for GPIO IRQ.
- gpio-controller:	Marks the port as GPIO controller.

Optional properties:
- interrupt-controller:	Empty boolean property which marks the GPIO
			module as an IRQ controller.
- #interrupt-cells:	Should be two.  Defines the number of integer
			cells required to specify an interrupt within
			this interrupt controller.  The first cell
			defines the pin number, the second cell
			defines additional flags (trigger type,
			trigger polarity).  Note that the available
			set of trigger conditions supported by the
			GPIO module depends on the actual SoC.

Example of gpio-controller nodes for a MPC8347 SoC:

	gpio1: gpio-controller@c00 {
		#gpio-cells = <2>;
		compatible = "fsl,mpc8347-gpio", "fsl,mpc8349-gpio";
		reg = <0xc00 0x100>;
		interrupt-parent = <&ipic>;
		interrupts = <74 0x8>;
		gpio-controller;
		interrupt-controller;
		#interrupt-cells = <2>;
	};

	gpio2: gpio-controller@d00 {
		#gpio-cells = <2>;
		compatible = "fsl,mpc8347-gpio", "fsl,mpc8349-gpio";
		reg = <0xd00 0x100>;
		interrupt-parent = <&ipic>;
		interrupts = <75 0x8>;
		gpio-controller;
	};

Example of a peripheral using the GPIO module as an IRQ controller:

	funkyfpga@0 {
		compatible = "funky-fpga";
		...
		interrupt-parent = <&gpio1>;
		interrupts = <4 3>;
	};
