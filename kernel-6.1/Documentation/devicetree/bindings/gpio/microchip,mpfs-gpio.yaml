# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/gpio/microchip,mpfs-gpio.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Microchip MPFS GPIO Controller

maintainers:
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    items:
      - enum:
          - microchip,mpfs-gpio

  reg:
    maxItems: 1

  interrupts:
    description:
      Interrupt mapping, one per GPIO. Maximum 32 GPIOs.
    minItems: 1
    maxItems: 32

  interrupt-controller: true

  clocks:
    maxItems: 1

  "#gpio-cells":
    const: 2

  "#interrupt-cells":
    const: 1

  ngpios:
    description:
      The number of GPIOs available.
    minimum: 1
    maximum: 32
    default: 32

  gpio-controller: true

patternProperties:
  "^.+-hog(-[0-9]+)?$":
    type: object

    additionalProperties: false

    properties:
      gpio-hog: true
      gpios: true
      input: true
      output-high: true
      output-low: true
      line-name: true

    required:
      - gpio-hog
      - gpios

required:
  - compatible
  - reg
  - interrupts
  - "#interrupt-cells"
  - interrupt-controller
  - "#gpio-cells"
  - gpio-controller
  - clocks

additionalProperties: false

examples:
  - |
    gpio@20122000 {
        compatible = "microchip,mpfs-gpio";
        reg = <0x20122000 0x1000>;
        clocks = <&clkcfg 25>;
        interrupt-parent = <&plic>;
        gpio-controller;
        #gpio-cells = <2>;
        interrupt-controller;
        #interrupt-cells = <1>;
        interrupts = <53>, <53>, <53>, <53>,
                     <53>, <53>, <53>, <53>,
                     <53>, <53>, <53>, <53>,
                     <53>, <53>, <53>, <53>,
                     <53>, <53>, <53>, <53>,
                     <53>, <53>, <53>, <53>,
                     <53>, <53>, <53>, <53>,
                     <53>, <53>, <53>, <53>;
    };
...
