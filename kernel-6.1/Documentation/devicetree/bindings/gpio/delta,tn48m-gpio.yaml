# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/gpio/delta,tn48m-gpio.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Delta Networks TN48M CPLD GPIO controller

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  This module is part of the Delta TN48M multi-function device. For more
  details see ../mfd/delta,tn48m-cpld.yaml.

  Delta TN48M has an onboard Lattice CPLD that is used as an GPIO expander.
  It provides 12 pins in total, they are input-only or ouput-only type.

properties:
  compatible:
    enum:
      - delta,tn48m-gpo
      - delta,tn48m-gpi

  reg:
    maxItems: 1

  "#gpio-cells":
    const: 2

  gpio-controller: true

required:
  - compatible
  - reg
  - "#gpio-cells"
  - gpio-controller

additionalProperties: false
