# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/gpio/gpio-zynq.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Xilinx Zynq GPIO controller

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - xlnx,zynq-gpio-1.0
      - xlnx,zynqmp-gpio-1.0
      - xlnx,versal-gpio-1.0
      - xlnx,pmc-gpio-1.0

  reg:
    maxItems: 1

  "#gpio-cells":
    const: 2

  interrupts:
    maxItems: 1

  gpio-controller: true

  gpio-line-names:
    description: strings describing the names of each gpio line
    minItems: 58
    maxItems: 174

  interrupt-controller: true

  "#interrupt-cells":
    const: 2

  clocks:
    maxItems: 1

  power-domains:
    maxItems: 1

allOf:
  - if:
      properties:
        compatible:
          enum:
            - xlnx,zynqmp-gpio-1.0
    then:
      properties:
        gpio-line-names:
          minItems: 174
          maxItems: 174

  - if:
      properties:
        compatible:
          enum:
            - xlnx,zynq-gpio-1.0
    then:
      properties:
        gpio-line-names:
          minItems: 118
          maxItems: 118

  - if:
      properties:
        compatible:
          enum:
            - xlnx,versal-gpio-1.0
    then:
      properties:
        gpio-line-names:
          minItems: 58
          maxItems: 58

  - if:
      properties:
        compatible:
          enum:
            - xlnx,pmc-gpio-1.0
    then:
      properties:
        gpio-line-names:
          minItems: 116
          maxItems: 116

required:
  - compatible
  - reg
  - "#gpio-cells"
  - interrupts
  - gpio-controller
  - interrupt-controller
  - "#interrupt-cells"
  - clocks

additionalProperties: false

examples:
  - |
    gpio@e000a000 {
      #gpio-cells = <2>;
      compatible = "xlnx,zynq-gpio-1.0";
      clocks = <&clkc 42>;
      gpio-controller;
      interrupt-parent = <&intc>;
      interrupts = <0 20 4>;
      interrupt-controller;
      #interrupt-cells = <2>;
      reg = <0xe000a000 0x1000>;
    };
