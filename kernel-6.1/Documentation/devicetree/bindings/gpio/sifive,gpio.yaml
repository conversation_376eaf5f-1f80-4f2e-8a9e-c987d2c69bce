# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/gpio/sifive,gpio.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: SiFive GPIO controller

maintainers:
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    items:
      - enum:
          - sifive,fu540-c000-gpio
          - sifive,fu740-c000-gpio
          - canaan,k210-gpiohs
      - const: sifive,gpio0

  reg:
    maxItems: 1

  interrupts:
    description:
      Interrupt mapping, one per GPIO. Maximum 32 GPIOs.
    minItems: 1
    maxItems: 32

  interrupt-controller: true

  "#interrupt-cells":
    const: 2

  clocks:
    maxItems: 1

  "#gpio-cells":
    const: 2

  ngpios:
    description:
      The number of GPIOs available on the controller implementation.
      It is 16 for the SiFive SoCs and 32 for the Canaan K210.
    minimum: 1
    maximum: 32
    default: 16

  gpio-line-names:
    minItems: 1
    maxItems: 32

  gpio-controller: true

required:
  - compatible
  - reg
  - interrupts
  - interrupt-controller
  - "#interrupt-cells"
  - "#gpio-cells"
  - gpio-controller

if:
  properties:
    compatible:
      contains:
        enum:
          - sifive,fu540-c000-gpio
          - sifive,fu740-c000-gpio
then:
  required:
    - clocks

additionalProperties: false

examples:
  - |
      #include <dt-bindings/clock/sifive-fu540-prci.h>
      gpio@10060000 {
        compatible = "sifive,fu540-c000-gpio", "sifive,gpio0";
        interrupt-parent = <&plic>;
        interrupts = <7>, <8>, <9>, <10>, <11>, <12>, <13>, <14>, <15>, <16>,
                     <17>, <18>, <19>, <20>, <21>, <22>;
        reg = <0x10060000 0x1000>;
        clocks = <&tlclk FU540_PRCI_CLK_TLCLK>;
        gpio-controller;
        #gpio-cells = <2>;
        interrupt-controller;
        #interrupt-cells = <2>;
      };

...
