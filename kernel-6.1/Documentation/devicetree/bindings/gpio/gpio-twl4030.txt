twl4030 GPIO controller bindings

Required properties:
- compatible:
  - "ti,twl4030-gpio" for twl4030 GPIO controller
- #gpio-cells : Should be two.
  - first cell is the pin number
  - second cell is used to specify optional parameters (unused)
- gpio-controller : Marks the device node as a GPIO controller.
- #interrupt-cells : Should be 2.
- interrupt-controller: Mark the device node as an interrupt controller
  The first cell is the GPIO number.
  The second cell is not used.
- ti,use-leds : Enables LEDA and LEDB outputs if set
- ti,debounce : if n-th bit is set, debounces GPIO-n
- ti,mmc-cd : if n-th bit is set, GPIO-n controls VMMC(n+1)
- ti,pullups : if n-th bit is set, set a pullup on GPIO-n
- ti,pulldowns : if n-th bit is set, set a pulldown on GPIO-n

Example:

twl_gpio: gpio {
    compatible = "ti,twl4030-gpio";
    #gpio-cells = <2>;
    gpio-controller;
    #interrupt-cells = <2>;
    interrupt-controller;
    ti,use-leds;
};
