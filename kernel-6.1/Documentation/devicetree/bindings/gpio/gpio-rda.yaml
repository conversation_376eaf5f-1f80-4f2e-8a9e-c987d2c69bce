# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/gpio/gpio-rda.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: RDA Micro GPIO controller

maintainers:
  - <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    const: rda,8810pl-gpio

  reg:
    maxItems: 1

  gpio-controller: true

  "#gpio-cells":
    const: 2

  ngpios:
    description:
      Number of available gpios in a bank.
    minimum: 1
    maximum: 32

  interrupt-controller: true

  "#interrupt-cells":
    const: 2

  interrupts:
    maxItems: 1

required:
  - compatible
  - reg
  - gpio-controller
  - "#gpio-cells"
  - ngpios
  - interrupt-controller
  - "#interrupt-cells"
  - interrupts

additionalProperties: false

...
