# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/gpio/fairchild,74hc595.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Generic 8-bit shift register

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - fairchild,74hc595
      - nxp,74lvc594

  reg:
    maxItems: 1

  gpio-controller: true

  '#gpio-cells':
    description:
      The second cell is only used to specify the GPIO polarity.
    const: 2

  registers-number:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: Number of daisy-chained shift registers

  enable-gpios:
    description: GPIO connected to the OE (Output Enable) pin.
    maxItems: 1

patternProperties:
  "^(hog-[0-9]+|.+-hog(-[0-9]+)?)$":
    type: object

    properties:
      gpio-hog: true
      gpios: true
      output-high: true
      output-low: true
      line-name: true

    required:
      - gpio-hog
      - gpios

    additionalProperties: false

required:
  - compatible
  - reg
  - gpio-controller
  - '#gpio-cells'
  - registers-number

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

unevaluatedProperties: false

examples:
  - |
    spi {
            #address-cells = <1>;
            #size-cells = <0>;

            gpio5: gpio5@0 {
                    compatible = "fairchild,74hc595";
                    reg = <0>;
                    gpio-controller;
                    #gpio-cells = <2>;
                    registers-number = <4>;
                    spi-max-frequency = <100000>;
            };
    };
