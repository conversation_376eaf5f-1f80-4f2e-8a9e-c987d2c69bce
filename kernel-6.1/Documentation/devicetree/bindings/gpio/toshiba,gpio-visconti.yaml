# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/gpio/toshiba,gpio-visconti.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Toshiba Visconti ARM SoCs GPIO controller

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    items:
      - const: toshiba,gpio-tmpv7708

  reg:
    maxItems: 1

  "#gpio-cells":
    const: 2

  gpio-ranges: true

  gpio-controller: true

  interrupt-controller: true

  "#interrupt-cells":
    const: 2

  interrupts:
    description:
      interrupt mapping one per GPIO.
    minItems: 16
    maxItems: 16

required:
  - compatible
  - reg
  - "#gpio-cells"
  - gpio-ranges
  - gpio-controller
  - interrupt-controller
  - "#interrupt-cells"

additionalProperties: false

examples:
  - |
      #include <dt-bindings/interrupt-controller/irq.h>
      #include <dt-bindings/interrupt-controller/arm-gic.h>

      soc {
        #address-cells = <2>;
        #size-cells = <2>;

        gpio: gpio@28020000 {
          compatible = "toshiba,gpio-tmpv7708";
          reg = <0 0x28020000 0 0x1000>;
          #gpio-cells = <0x2>;
          gpio-ranges = <&pmux 0 0 32>;
          gpio-controller;
          interrupt-controller;
          #interrupt-cells = <2>;
          interrupt-parent = <&gic>;
        };
      };
...
