Aspeed GPIO controller Device Tree Bindings
-------------------------------------------

Required properties:
- compatible		: Either "aspeed,ast2400-gpio", "aspeed,ast2500-gpio",
					or "aspeed,ast2600-gpio".

- #gpio-cells 		: Should be two
			  - First cell is the GPIO line number
			  - Second cell is used to specify optional
			    parameters (unused)

- reg			: Address and length of the register set for the device
- gpio-controller	: Marks the device node as a GPIO controller.
- interrupts		: Interrupt specifier (see interrupt bindings for
			  details)
- interrupt-controller	: Mark the GPIO controller as an interrupt-controller

Optional properties:

- clocks		: A phandle to the clock to use for debounce timings
- ngpios		: Number of GPIOs controlled by this controller. Should	be set
				  when there are multiple GPIO controllers on a SoC (ast2600).

The gpio and interrupt properties are further described in their respective
bindings documentation:

- Documentation/devicetree/bindings/gpio/gpio.txt
- Documentation/devicetree/bindings/interrupt-controller/interrupts.txt

  Example:
	gpio@1e780000 {
		#gpio-cells = <2>;
		compatible = "aspeed,ast2400-gpio";
		gpio-controller;
		interrupts = <20>;
		reg = <0x1e780000 0x1000>;
		interrupt-controller;
	};
