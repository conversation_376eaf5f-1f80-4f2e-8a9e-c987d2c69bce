# SPDX-License-Identifier: GPL-2.0-only
%YAML 1.2
---
$id: http://devicetree.org/schemas/gpio/mrvl-gpio.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: <PERSON>l PXA GPIO controller

maintainers:
  - <PERSON><PERSON> <<EMAIL>>
  - <PERSON><PERSON><PERSON> <b<PERSON><PERSON><PERSON><PERSON>@baylibre.com>
  - <PERSON> <<EMAIL>>

allOf:
  - if:
      properties:
        compatible:
          contains:
            enum:
              - intel,pxa25x-gpio
              - intel,pxa26x-gpio
              - intel,pxa27x-gpio
              - intel,pxa3xx-gpio
    then:
      properties:
        interrupts:
          minItems: 3
          maxItems: 3
        interrupt-names:
          items:
            - const: gpio0
            - const: gpio1
            - const: gpio_mux
  - if:
      properties:
        compatible:
          contains:
            enum:
              - marvell,mmp-gpio
              - marvell,mmp2-gpio
    then:
      properties:
        interrupts:
          maxItems: 1
        interrupt-names:
          items:
            - const: gpio_mux

properties:
  $nodename:
    pattern: '^gpio@[0-9a-f]+$'

  compatible:
    enum:
      - intel,pxa25x-gpio
      - intel,pxa26x-gpio
      - intel,pxa27x-gpio
      - intel,pxa3xx-gpio
      - marvell,mmp-gpio
      - marvell,mmp2-gpio
      - marvell,pxa93x-gpio

  reg:
    maxItems: 1

  clocks:
    maxItems: 1

  resets:
    maxItems: 1

  ranges: true

  '#address-cells':
    const: 1

  '#size-cells':
    const: 1

  gpio-controller: true

  '#gpio-cells':
    const: 2

  gpio-ranges: true

  interrupts: true

  interrupt-names: true

  interrupt-controller: true

  '#interrupt-cells':
    const: 2

patternProperties:
  '^gpio@[0-9a-f]*$':
    type: object
    properties:
      reg:
        maxItems: 1

    required:
      - reg

    additionalProperties: false

required:
  - compatible
  - '#address-cells'
  - '#size-cells'
  - reg
  - gpio-controller
  - '#gpio-cells'
  - interrupts
  - interrupt-names
  - interrupt-controller
  - '#interrupt-cells'

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/pxa-clock.h>
    gpio@40e00000 {
        compatible = "intel,pxa3xx-gpio";
        #address-cells = <1>;
        #size-cells = <1>;
        reg = <0x40e00000 0x10000>;
        gpio-controller;
        #gpio-cells = <2>;
        interrupts = <8>, <9>, <10>;
        interrupt-names = "gpio0", "gpio1", "gpio_mux";
        clocks = <&clks CLK_GPIO>;
        interrupt-controller;
        #interrupt-cells = <2>;
    };
  - |
    #include <dt-bindings/clock/marvell,pxa910.h>
    gpio@d4019000 {
        compatible = "marvell,mmp-gpio";
        #address-cells = <1>;
        #size-cells = <1>;
        reg = <0xd4019000 0x1000>;
        gpio-controller;
        #gpio-cells = <2>;
        interrupts = <49>;
        interrupt-names = "gpio_mux";
        clocks = <&soc_clocks PXA910_CLK_GPIO>;
        resets = <&soc_clocks PXA910_CLK_GPIO>;
        interrupt-controller;
        #interrupt-cells = <2>;
        ranges;

        gpio@d4019000 {
            reg = <0xd4019000 0x4>;
        };

        gpio@d4019004 {
            reg = <0xd4019004 0x4>;
        };

        gpio@d4019008 {
            reg = <0xd4019008 0x4>;
        };

        gpio@d4019100 {
            reg = <0xd4019100 0x4>;
        };
     };

...
