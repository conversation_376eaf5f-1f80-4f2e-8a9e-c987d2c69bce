# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/gpio/realtek,otto-gpio.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Realtek Otto GPIO controller

maintainers:
  - <PERSON><PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

description: |
  Realtek's GPIO controller on their MIPS switch SoCs (Otto platform) consists
  of two banks of 32 GPIOs. These GPIOs can generate edge-triggered interrupts.
  Each bank's interrupts are cascased into one interrupt line on the parent
  interrupt controller, if provided.
  This binding allows defining a single bank in the devicetree. The interrupt
  controller is not supported on the fallback compatible name, which only
  allows for GPIO port use.

properties:
  $nodename:
    pattern: "^gpio@[0-9a-f]+$"

  compatible:
    items:
      - enum:
          - realtek,rtl8380-gpio
          - realtek,rtl8390-gpio
          - realtek,rtl9300-gpio
          - realtek,rtl9310-gpio
      - const: realtek,otto-gpio

  reg: true

  "#gpio-cells":
    const: 2

  gpio-controller: true

  ngpios:
    minimum: 1
    maximum: 32

  interrupt-controller: true

  "#interrupt-cells":
    const: 2

  interrupts:
    maxItems: 1

if:
  properties:
    compatible:
      contains:
        const: realtek,rtl9300-gpio
then:
  properties:
    reg:
      items:
        - description: GPIO and interrupt control
        - description: interrupt CPU map
else:
  properties:
    reg:
      items:
        - description: GPIO and interrupt control

required:
  - compatible
  - reg
  - "#gpio-cells"
  - gpio-controller

additionalProperties: false

dependencies:
  interrupt-controller: [ interrupts ]

examples:
  - |
      gpio@3500 {
        compatible = "realtek,rtl8380-gpio", "realtek,otto-gpio";
        reg = <0x3500 0x1c>;
        gpio-controller;
        #gpio-cells = <2>;
        ngpios = <24>;
        interrupt-controller;
        #interrupt-cells = <2>;
        interrupt-parent = <&rtlintc>;
        interrupts = <23>;
      };
  - |
      gpio@3300 {
        compatible = "realtek,rtl9300-gpio", "realtek,otto-gpio";
        reg = <0x3300 0x1c>, <0x3338 0x8>;
        gpio-controller;
        #gpio-cells = <2>;
        ngpios = <24>;
        interrupt-controller;
        #interrupt-cells = <2>;
        interrupt-parent = <&rtlintc>;
        interrupts = <13>;
      };

...
