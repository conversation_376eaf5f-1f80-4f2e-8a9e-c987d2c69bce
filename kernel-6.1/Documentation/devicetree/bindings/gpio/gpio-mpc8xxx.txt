* Freescale MPC512x/MPC8xxx/QorIQ/Layerscape GPIO controller

Required properties:
- compatible : Should be "fsl,<soc>-gpio"
  The following <soc>s are known to be supported:
	mpc5121, mpc5125, mpc8349, mpc8572, mpc8610, pq3, qoriq,
	ls1021a, ls1043a, ls2080a, ls1028a, ls1088a.
- reg : Address and length of the register set for the device
- interrupts : Should be the port interrupt shared by all 32 pins.
- #gpio-cells : Should be two.  The first cell is the pin number and
  the second cell is used to specify the gpio polarity:
      0 = active high
      1 = active low

Optional properties:
- little-endian : GPIO registers are used as little endian. If not
                  present registers are used as big endian by default.

Example of gpio-controller node for a mpc5125 SoC:

gpio0: gpio@1100 {
	compatible = "fsl,mpc5125-gpio";
	#gpio-cells = <2>;
	reg = <0x1100 0x080>;
	interrupts = <78 0x8>;
};

Example of gpio-controller node for a ls2080a SoC:

gpio0: gpio@2300000 {
	compatible = "fsl,ls2080a-gpio", "fsl,qoriq-gpio";
	reg = <0x0 0x2300000 0x0 0x10000>;
	interrupts = <0 36 0x4>; /* Level high type */
	gpio-controller;
	little-endian;
	#gpio-cells = <2>;
	interrupt-controller;
	#interrupt-cells = <2>;
};


Example of gpio-controller node for a ls1028a/ls1088a SoC:

gpio1: gpio@2300000 {
	compatible = "fsl,ls1028a-gpio", "fsl,ls1088a-gpio", "fsl,qoriq-gpio";
	reg = <0x0 0x2300000 0x0 0x10000>;
	interrupts = <GIC_SPI 36 IRQ_TYPE_LEVEL_HIGH>;
	gpio-controller;
	#gpio-cells = <2>;
	interrupt-controller;
	#interrupt-cells = <2>;
	little-endian;
};
