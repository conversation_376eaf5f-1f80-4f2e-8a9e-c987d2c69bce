* Microchip PIC32 GPIO devices (PIO).

Required properties:
 - compatible: "microchip,pic32mzda-gpio"
 - reg: Base address and length for the device.
 - interrupts: The port interrupt shared by all pins.
 - gpio-controller: Marks the port as GPIO controller.
 - #gpio-cells: Two. The first cell is the pin number and
   the second cell is used to specify the gpio polarity as defined in
   defined in <dt-bindings/gpio/gpio.h>:
      0 = GPIO_ACTIVE_HIGH
      1 = GPIO_ACTIVE_LOW
      2 = GPIO_OPEN_DRAIN
 - interrupt-controller: Marks the device node as an interrupt controller.
 - #interrupt-cells: Two. The first cell is the GPIO number and second cell
   is used to specify the trigger type as defined in
   <dt-bindings/interrupt-controller/irq.h>:
      IRQ_TYPE_EDGE_RISING
      IRQ_TYPE_EDGE_FALLING
      IRQ_TYPE_EDGE_BOTH
 - clocks: Clock specifier (see clock bindings for details).
 - microchip,gpio-bank: Specifies which bank a controller owns.
 - gpio-ranges: Interaction with the PINCTRL subsystem.

Example:

/* PORTA */
gpio0: gpio0@1f860000 {
	compatible = "microchip,pic32mzda-gpio";
	reg = <0x1f860000 0x100>;
	interrupts = <118 IRQ_TYPE_LEVEL_HIGH>;
	#gpio-cells = <2>;
	gpio-controller;
	interrupt-controller;
	#interrupt-cells = <2>;
	clocks = <&rootclk PB4CLK>;
	microchip,gpio-bank = <0>;
	gpio-ranges = <&pic32_pinctrl 0 0 16>;
};

keys {
	...

	button@sw1 {
		label = "ESC";
		linux,code = <1>;
		gpios = <&gpio0 12 0>;
	};
};
