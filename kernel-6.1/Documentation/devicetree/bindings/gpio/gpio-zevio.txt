Zevio GPIO controller

Required properties:
- compatible: Should be "lsi,zevio-gpio"
- reg: Address and length of the register set for the device
- #gpio-cells: Should be two. The first cell is the pin number and the
  second cell is used to specify optional parameters (currently unused).
- gpio-controller: Marks the device node as a GPIO controller.

Example:
	gpio: gpio@90000000 {
		compatible = "lsi,zevio-gpio";
		reg = <0x90000000 0x1000>;
		gpio-controller;
		#gpio-cells = <2>;
	};
