# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/gpio/fsl,imx8qxp-sc-gpio.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: GPIO driver over IMX SCU firmware API

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description: |
  This module provides the standard interface to control the
  resource pins in SCU domain on i.MX8 platforms.

properties:
  compatible:
    enum:
      - fsl,imx8qxp-sc-gpio

  "#gpio-cells":
    const: 2

  gpio-controller: true

required:
  - compatible
  - "#gpio-cells"
  - gpio-controller

additionalProperties: false

examples:
  - |
    gpio0: gpio {
        compatible = "fsl,imx8qxp-sc-gpio";
        gpio-controller;
        #gpio-cells = <2>;
    };
