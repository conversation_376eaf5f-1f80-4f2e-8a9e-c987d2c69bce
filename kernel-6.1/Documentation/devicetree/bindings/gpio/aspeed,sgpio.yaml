# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/gpio/aspeed,sgpio.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Aspeed SGPIO controller

maintainers:
  - <PERSON> <<EMAIL>>

description:
  This SGPIO controller is for ASPEED AST2400, AST2500 and AST2600 SoC,
  AST2600 have two sgpio master one with 128 pins another one with 80 pins,
  AST2500/AST2400 have one sgpio master with 80 pins. Each of the Serial
  GPIO pins can be programmed to support the following options
  - Support interrupt option for each input port and various interrupt
    sensitivity option (level-high, level-low, edge-high, edge-low)
  - Support reset tolerance option for each output port
  - Directly connected to APB bus and its shift clock is from APB bus clock
    divided by a programmable value.
  - Co-work with external signal-chained TTL components (74LV165/74LV595)

properties:
  compatible:
    enum:
      - aspeed,ast2400-sgpio
      - aspeed,ast2500-sgpio
      - aspeed,ast2600-sgpiom

  reg:
    maxItems: 1

  gpio-controller: true

  '#gpio-cells':
    const: 2

  interrupts:
    maxItems: 1

  interrupt-controller: true

  clocks:
    maxItems: 1

  ngpios: true

  bus-frequency: true

required:
  - compatible
  - reg
  - gpio-controller
  - '#gpio-cells'
  - interrupts
  - interrupt-controller
  - ngpios
  - clocks
  - bus-frequency

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/aspeed-clock.h>
    sgpio: sgpio@1e780200 {
        #gpio-cells = <2>;
        compatible = "aspeed,ast2500-sgpio";
        gpio-controller;
        interrupts = <40>;
        reg = <0x1e780200 0x0100>;
        clocks = <&syscon ASPEED_CLK_APB>;
        interrupt-controller;
        ngpios = <80>;
        bus-frequency = <12000000>;
    };
