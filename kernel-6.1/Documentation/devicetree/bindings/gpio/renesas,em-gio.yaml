# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/gpio/renesas,em-gio.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Renesas EMMA Mobile General Purpose I/O Interface

maintainers:
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    const: renesas,em-gio

  reg:
    items:
      - description: First set of contiguous registers
      - description: Second set of contiguous registers

  interrupts:
    items:
      - description: Interrupt for the first set of 16 GPIO ports
      - description: Interrupt for the second set of 16 GPIO ports

  gpio-controller: true

  '#gpio-cells':
    const: 2

  gpio-ranges:
    maxItems: 1

  ngpios:
    minimum: 1
    maximum: 32

  interrupt-controller: true

  '#interrupt-cells':
    const: 2

required:
  - compatible
  - reg
  - interrupts
  - gpio-controller
  - '#gpio-cells'
  - gpio-ranges
  - ngpios
  - interrupt-controller
  - '#interrupt-cells'

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    gpio0: gpio@e0050000 {
            compatible = "renesas,em-gio";
            reg = <0xe0050000 0x2c>, <0xe0050040 0x20>;
            interrupts = <GIC_SPI 67 IRQ_TYPE_LEVEL_HIGH>,
                         <GIC_SPI 68 IRQ_TYPE_LEVEL_HIGH>;
            gpio-controller;
            #gpio-cells = <2>;
            gpio-ranges = <&pfc 0 0 32>;
            ngpios = <32>;
            interrupt-controller;
            #interrupt-cells = <2>;
    };
