# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/gpio/rockchip,rk3328-grf-gpio.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Rockchip RK3328 General Register Files GPIO controller

description:
  The Rockchip RK3328 General Register File (GRF) outputs only the
  GPIO_MUTE pin, originally for codec mute control, but it can also be used
  for general purpose. It is manipulated by the GRF_SOC_CON10 register.
  If needed in the future support for the HDMI pins can also be added.
  The GPIO node should be declared as the child of the GRF node.

  The GPIO_MUTE pin is referred to in the format

  <&grf_gpio 0 GPIO_ACTIVE_LOW>

  The first cell is the pin number and
  the second cell is used to specify the GPIO polarity
    0 = Active high
    1 = Active low

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    const: rockchip,rk3328-grf-gpio

  gpio-controller: true

  "#gpio-cells":
    const: 2

required:
  - compatible
  - gpio-controller
  - "#gpio-cells"

additionalProperties: false

examples:
  - |
    grf_gpio: gpio {
      compatible = "rockchip,rk3328-grf-gpio";
      gpio-controller;
      #gpio-cells = <2>;
    };
