Specifying GPIO information for devices
=======================================

1) gpios property
-----------------

GPIO properties should be named "[<name>-]gpios", with <name> being the purpose
of this GPIO for the device. While a non-existent <name> is considered valid
for compatibility reasons (resolving to the "gpios" property), it is not allowed
for new bindings. Also, GPIO properties named "[<name>-]gpio" are valid and old
bindings use it, but are only supported for compatibility reasons and should not
be used for newer bindings since it has been deprecated.

GPIO properties can contain one or more GPIO phandles, but only in exceptional
cases should they contain more than one. If your device uses several GPIOs with
distinct functions, reference each of them under its own property, giving it a
meaningful name. The only case where an array of GPIOs is accepted is when
several GPIOs serve the same function (e.g. a parallel data line).

The exact purpose of each gpios property must be documented in the device tree
binding of the device.

The following example could be used to describe GPIO pins used as device enable
and bit-banged data signals:

	gpio1: gpio1 {
		gpio-controller;
		#gpio-cells = <2>;
	};
	[...]

	data-gpios = <&gpio1 12 0>,
		     <&gpio1 13 0>,
		     <&gpio1 14 0>,
		     <&gpio1 15 0>;

In the above example, &gpio1 uses 2 cells to specify a gpio. The first cell is
a local offset to the GPIO line and the second cell represent consumer flags,
such as if the consumer desire the line to be active low (inverted) or open
drain. This is the recommended practice.

The exact meaning of each specifier cell is controller specific, and must be
documented in the device tree binding for the device, but it is strongly
recommended to use the two-cell approach.

Most controllers are specifying a generic flag bitfield in the last cell, so
for these, use the macros defined in
include/dt-bindings/gpio/gpio.h whenever possible:

Example of a node using GPIOs:

	node {
		enable-gpios = <&qe_pio_e 18 GPIO_ACTIVE_HIGH>;
	};

GPIO_ACTIVE_HIGH is 0, so in this example gpio-specifier is "18 0" and encodes
GPIO pin number, and GPIO flags as accepted by the "qe_pio_e" gpio-controller.

Optional standard bitfield specifiers for the last cell:

- Bit 0: 0 means active high, 1 means active low
- Bit 1: 0 mean push-pull wiring, see:
           https://en.wikipedia.org/wiki/Push-pull_output
         1 means single-ended wiring, see:
           https://en.wikipedia.org/wiki/Single-ended_triode
- Bit 2: 0 means open-source, 1 means open drain, see:
           https://en.wikipedia.org/wiki/Open_collector
- Bit 3: 0 means the output should be maintained during sleep/low-power mode
         1 means the output state can be lost during sleep/low-power mode
- Bit 4: 0 means no pull-up resistor should be enabled
         1 means a pull-up resistor should be enabled
         This setting only applies to hardware with a simple on/off
         control for pull-up configuration. If the hardware has more
         elaborate pull-up configuration, it should be represented
         using a pin control binding.
- Bit 5: 0 means no pull-down resistor should be enabled
         1 means a pull-down resistor should be enabled
         This setting only applies to hardware with a simple on/off
         control for pull-down configuration. If the hardware has more
         elaborate pull-down configuration, it should be represented
         using a pin control binding.

1.1) GPIO specifier best practices
----------------------------------

A gpio-specifier should contain a flag indicating the GPIO polarity; active-
high or active-low. If it does, the following best practices should be
followed:

The gpio-specifier's polarity flag should represent the physical level at the
GPIO controller that achieves (or represents, for inputs) a logically asserted
value at the device. The exact definition of logically asserted should be
defined by the binding for the device. If the board inverts the signal between
the GPIO controller and the device, then the gpio-specifier will represent the
opposite physical level than the signal at the device's pin.

When the device's signal polarity is configurable, the binding for the
device must either:

a) Define a single static polarity for the signal, with the expectation that
any software using that binding would statically program the device to use
that signal polarity.

The static choice of polarity may be either:

a1) (Preferred) Dictated by a binding-specific DT property.

or:

a2) Defined statically by the DT binding itself.

In particular, the polarity cannot be derived from the gpio-specifier, since
that would prevent the DT from separately representing the two orthogonal
concepts of configurable signal polarity in the device, and possible board-
level signal inversion.

or:

b) Pick a single option for device signal polarity, and document this choice
in the binding. The gpio-specifier should represent the polarity of the signal
(at the GPIO controller) assuming that the device is configured for this
particular signal polarity choice. If software chooses to program the device
to generate or receive a signal of the opposite polarity, software will be
responsible for correctly interpreting (inverting) the GPIO signal at the GPIO
controller.

2) gpio-controller nodes
------------------------

Every GPIO controller node must contain both an empty "gpio-controller"
property, and a #gpio-cells integer property, which indicates the number of
cells in a gpio-specifier.

Some system-on-chips (SoCs) use the concept of GPIO banks. A GPIO bank is an
instance of a hardware IP core on a silicon die, usually exposed to the
programmer as a coherent range of I/O addresses. Usually each such bank is
exposed in the device tree as an individual gpio-controller node, reflecting
the fact that the hardware was synthesized by reusing the same IP block a
few times over.

Optionally, a GPIO controller may have a "ngpios" property. This property
indicates the number of in-use slots of available slots for GPIOs. The
typical example is something like this: the hardware register is 32 bits
wide, but only 18 of the bits have a physical counterpart. The driver is
generally written so that all 32 bits can be used, but the IP block is reused
in a lot of designs, some using all 32 bits, some using 18 and some using
12. In this case, setting "ngpios = <18>;" informs the driver that only the
first 18 GPIOs, at local offset 0 .. 17, are in use.

If these GPIOs do not happen to be the first N GPIOs at offset 0...N-1, an
additional set of tuples is needed to specify which GPIOs are unusable, with
the gpio-reserved-ranges binding. This property indicates the start and size
of the GPIOs that can't be used.

Optionally, a GPIO controller may have a "gpio-line-names" property. This is
an array of strings defining the names of the GPIO lines going out of the
GPIO controller. This name should be the most meaningful producer name
for the system, such as a rail name indicating the usage. Package names
such as pin name are discouraged: such lines have opaque names (since they
are by definition generic purpose) and such names are usually not very
helpful. For example "MMC-CD", "Red LED Vdd" and "ethernet reset" are
reasonable line names as they describe what the line is used for. "GPIO0"
is not a good name to give to a GPIO line. Placeholders are discouraged:
rather use the "" (blank string) if the use of the GPIO line is undefined
in your design. The names are assigned starting from line offset 0 from
left to right from the passed array. An incomplete array (where the number
of passed named are less than ngpios) will still be used up until the last
provided valid line index.

Example:

gpio-controller@00000000 {
	compatible = "foo";
	reg = <0x00000000 0x1000>;
	gpio-controller;
	#gpio-cells = <2>;
	ngpios = <18>;
	gpio-reserved-ranges = <0 4>, <12 2>;
	gpio-line-names = "MMC-CD", "MMC-WP", "VDD eth", "RST eth", "LED R",
		"LED G", "LED B", "Col A", "Col B", "Col C", "Col D",
		"Row A", "Row B", "Row C", "Row D", "NMI button",
		"poweroff", "reset";
}

The GPIO chip may contain GPIO hog definitions. GPIO hogging is a mechanism
providing automatic GPIO request and configuration as part of the
gpio-controller's driver probe function.

Each GPIO hog definition is represented as a child node of the GPIO controller.
Required properties:
- gpio-hog:   A property specifying that this child node represents a GPIO hog.
- gpios:      Store the GPIO information (id, flags, ...) for each GPIO to
	      affect. Shall contain an integer multiple of the number of cells
	      specified in its parent node (GPIO controller node).
Only one of the following properties scanned in the order shown below.
This means that when multiple properties are present they will be searched
in the order presented below and the first match is taken as the intended
configuration.
- input:      A property specifying to set the GPIO direction as input.
- output-low  A property specifying to set the GPIO direction as output with
	      the value low.
- output-high A property specifying to set the GPIO direction as output with
	      the value high.

Optional properties:
- line-name:  The GPIO label name. If not present the node name is used.

Example of two SOC GPIO banks defined as gpio-controller nodes:

	qe_pio_a: gpio-controller@1400 {
		compatible = "fsl,qe-pario-bank-a", "fsl,qe-pario-bank";
		reg = <0x1400 0x18>;
		gpio-controller;
		#gpio-cells = <2>;

		line_b-hog {
			gpio-hog;
			gpios = <6 0>;
			output-low;
			line-name = "foo-bar-gpio";
		};
	};

	qe_pio_e: gpio-controller@1460 {
		compatible = "fsl,qe-pario-bank-e", "fsl,qe-pario-bank";
		reg = <0x1460 0x18>;
		gpio-controller;
		#gpio-cells = <2>;
	};

2.1) gpio- and pin-controller interaction
-----------------------------------------

Some or all of the GPIOs provided by a GPIO controller may be routed to pins
on the package via a pin controller. This allows muxing those pins between
GPIO and other functions. It is a fairly common practice among silicon
engineers.

2.2) Ordinary (numerical) GPIO ranges
-------------------------------------

It is useful to represent which GPIOs correspond to which pins on which pin
controllers. The gpio-ranges property described below represents this with
a discrete set of ranges mapping pins from the pin controller local number space
to pins in the GPIO controller local number space.

The format is: <[pin controller phandle], [GPIO controller offset],
                [pin controller offset], [number of pins]>;

The GPIO controller offset pertains to the GPIO controller node containing the
range definition.

The pin controller node referenced by the phandle must conform to the bindings
described in pinctrl/pinctrl-bindings.txt.

Each offset runs from 0 to N. It is perfectly fine to pile any number of
ranges with just one pin-to-GPIO line mapping if the ranges are concocted, but
in practice these ranges are often lumped in discrete sets.

Example:

    gpio-ranges = <&foo 0 20 10>, <&bar 10 50 20>;

This means:
- pins 20..29 on pin controller "foo" is mapped to GPIO line 0..9 and
- pins 50..69 on pin controller "bar" is mapped to GPIO line 10..29


Verbose example:

	qe_pio_e: gpio-controller@1460 {
		#gpio-cells = <2>;
		compatible = "fsl,qe-pario-bank-e", "fsl,qe-pario-bank";
		reg = <0x1460 0x18>;
		gpio-controller;
		gpio-ranges = <&pinctrl1 0 20 10>, <&pinctrl2 10 50 20>;
	};

Here, a single GPIO controller has GPIOs 0..9 routed to pin controller
pinctrl1's pins 20..29, and GPIOs 10..29 routed to pin controller pinctrl2's
pins 50..69.


2.3) GPIO ranges from named pin groups
--------------------------------------

It is also possible to use pin groups for gpio ranges when pin groups are the
easiest and most convenient mapping.

Both both <pinctrl-base> and <count> must set to 0 when using named pin groups
names.

The property gpio-ranges-group-names must contain exactly one string for each
range.

Elements of gpio-ranges-group-names must contain the name of a pin group
defined in the respective pin controller. The number of pins/GPIO lines in the
range is the number of pins in that pin group. The number of pins of that
group is defined int the implementation and not in the device tree.

If numerical and named pin groups are mixed, the string corresponding to a
numerical pin range in gpio-ranges-group-names must be empty.

Example:

	gpio_pio_i: gpio-controller@14b0 {
		#gpio-cells = <2>;
		compatible = "fsl,qe-pario-bank-e", "fsl,qe-pario-bank";
		reg = <0x1480 0x18>;
		gpio-controller;
		gpio-ranges =			<&pinctrl1 0 20 10>,
						<&pinctrl2 10 0 0>,
						<&pinctrl1 15 0 10>,
						<&pinctrl2 25 0 0>;
		gpio-ranges-group-names =	"",
						"foo",
						"",
						"bar";
	};

Here, three GPIO ranges are defined referring to two pin controllers.

pinctrl1 GPIO ranges are defined using pin numbers whereas the GPIO ranges
in pinctrl2 are defined using the pin groups named "foo" and "bar".

Previous versions of this binding required all pin controller nodes that
were referenced by any gpio-ranges property to contain a property named
#gpio-range-cells with value <3>. This requirement is now deprecated.
However, that property may still exist in older device trees for
compatibility reasons, and would still be required even in new device
trees that need to be compatible with older software.
