* 74XX MMIO GPIO driver

Required properties:
- compatible: Should contain one of the following:
   "ti,741g125": for 741G125 (1-bit Input),
   "ti,741g174": for 741G74 (1-bit Output),
   "ti,742g125": for 742G125 (2-bit Input),
   "ti,7474"   : for 7474 (2-bit Output),
   "ti,74125"  : for 74125 (4-bit Input),
   "ti,74175"  : for 74175 (4-bit Output),
   "ti,74365"  : for 74365 (6-bit Input),
   "ti,74174"  : for 74174 (6-bit Output),
   "ti,74244"  : for 74244 (8-bit Input),
   "ti,74273"  : for 74273 (8-bit Output),
   "ti,741624" : for 741624 (16-bit Input),
   "ti,7416374": for 7416374 (16-bit Output).
- reg: Physical base address and length where IC resides.
- gpio-controller: Marks the device node as a gpio controller.
- #gpio-cells: Should be two. The first cell is the pin number and
   the second cell is used to specify the GPIO polarity:
    0 = Active High,
    1 = Active Low.

Example:
	ctrl: gpio@30008004 {
		compatible = "ti,74174";
		reg = <0x30008004 0x1>;
		gpio-controller;
		#gpio-cells = <2>;
	};
