APM X-Gene SoC GPIO controller bindings

This is a gpio controller that is part of the flash controller.
This gpio controller controls a total of 48 gpios.

Required properties:
- compatible: "apm,xgene-gpio" for X-Gene GPIO controller
- reg: Physical base address and size of the controller's registers
- #gpio-cells: Should be two.
	- first cell is the pin number
	- second cell is used to specify the gpio polarity:
		0 = active high
		1 = active low
- gpio-controller: Marks the device node as a GPIO controller.

Example:
	gpio0: gpio0@1701c000 {
		compatible = "apm,xgene-gpio";
		reg = <0x0 0x1701c000 0x0 0x40>;
		gpio-controller;
		#gpio-cells = <2>;
	};
