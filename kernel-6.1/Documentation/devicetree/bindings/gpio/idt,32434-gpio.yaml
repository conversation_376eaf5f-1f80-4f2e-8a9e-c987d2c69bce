# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/gpio/idt,32434-gpio.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: IDT 79RC32434 GPIO controller

maintainers:
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    const: idt,32434-gpio

  reg:
    maxItems: 2

  reg-names:
    items:
      - const: gpio
      - const: pic

  gpio-controller: true

  "#gpio-cells":
    const: 2

  ngpios:
    minimum: 1
    maximum: 32

  interrupt-controller: true

  "#interrupt-cells":
    const: 2

  interrupts:
    maxItems: 1

required:
  - compatible
  - reg
  - reg-names
  - gpio-controller
  - "#gpio-cells"

additionalProperties: false

examples:
  - |
    gpio0: gpio@50004 {
        compatible = "idt,32434-gpio";
        reg = <0x50004 0x10>, <0x38030 0x0c>;
        reg-names = "gpio", "pic";

        interrupt-controller;
        #interrupt-cells = <2>;

        interrupt-parent = <&cpuintc>;
        interrupts = <6>;

        gpio-controller;
        #gpio-cells = <2>;

        ngpios = <14>;
    };
