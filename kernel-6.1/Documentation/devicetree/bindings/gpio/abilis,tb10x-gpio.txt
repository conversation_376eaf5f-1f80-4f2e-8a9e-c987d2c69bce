* Abilis TB10x GPIO controller

Required Properties:
- compatible: Should be "abilis,tb10x-gpio"
- reg: Address and length of the register set for the device
- gpio-controller: Marks the device node as a gpio controller.
- #gpio-cells: Should be <2>. The first cell is the pin number and the
  second cell is used to specify optional parameters:
   - bit 0 specifies polarity (0 for normal, 1 for inverted).
- abilis,ngpio: the number of GPIO pins this driver controls.

Optional Properties:
- interrupt-controller: Marks the device node as an interrupt controller.
- #interrupt-cells: Should be <1>. Interrupts are triggered on both edges.
- interrupts: Defines the interrupt line connecting this GPIO controller to
  its parent interrupt controller.

GPIO ranges are specified as described in
Documentation/devicetree/bindings/gpio/gpio.txt

Example:

	gpioa: gpio@ff140000 {
		compatible = "abilis,tb10x-gpio";
		interrupt-controller;
		#interrupt-cells = <1>;
		interrupt-parent = <&tb10x_ictl>;
		interrupts = <27 2>;
		reg = <0xFF140000 0x1000>;
		gpio-controller;
		#gpio-cells = <2>;
		abilis,ngpio = <3>;
		gpio-ranges = <&iomux 0 0 0>;
		gpio-ranges-group-names = "gpioa_pins";
	};
