* IBM/AMCC/APM GPIO Controller for PowerPC 4XX series and compatible SoCs

All GPIOs are pin-shared with other functions. DCRs control whether a
particular pin that has GPIO capabilities acts as a GPIO or is used for
another purpose. GPIO outputs are separately programmable to emulate
an open-drain driver.

Required properties:
	- compatible: must be "ibm,ppc4xx-gpio"
	- reg: address and length of the register set for the device
	- #gpio-cells: must be set to 2. The first cell is the pin number
		and the second cell is used to specify the gpio polarity:
		0 = active high
		1 = active low
	- gpio-controller: marks the device node as a gpio controller.

Example:

GPIO0: gpio@ef600b00 {
	compatible = "ibm,ppc4xx-gpio";
	reg = <0xef600b00 0x00000048>;
	#gpio-cells = <2>;
	gpio-controller;
};
