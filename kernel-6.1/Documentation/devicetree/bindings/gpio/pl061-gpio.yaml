# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/gpio/pl061-gpio.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: ARM PL061 GPIO controller

maintainers:
  - <PERSON><PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

# We need a select here so we don't match all nodes with 'arm,primecell'
select:
  properties:
    compatible:
      contains:
        const: arm,pl061
  required:
    - compatible

properties:
  $nodename:
    pattern: "^gpio@[0-9a-f]+$"

  compatible:
    items:
      - const: arm,pl061
      - const: arm,primecell

  reg:
    maxItems: 1

  interrupts:
    oneOf:
      - maxItems: 1
      - maxItems: 8

  interrupt-controller: true

  "#interrupt-cells":
    const: 2

  clocks:
    maxItems: 1

  clock-names: true

  "#gpio-cells":
    const: 2

  gpio-controller: true

  gpio-line-names: true

  gpio-ranges:
    minItems: 1
    maxItems: 8

required:
  - compatible
  - reg
  - interrupts
  - interrupt-controller
  - "#interrupt-cells"
  - clocks
  - "#gpio-cells"
  - gpio-controller

additionalProperties: false

...
