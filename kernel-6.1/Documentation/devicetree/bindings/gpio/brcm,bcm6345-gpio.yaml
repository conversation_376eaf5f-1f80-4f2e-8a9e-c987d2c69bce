# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/gpio/brcm,bcm6345-gpio.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Broadcom BCM6345 GPIO controller

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

description: |+
  Bindings for Broadcom's BCM63xx memory-mapped GPIO controllers.

  These bindings can be used on any BCM63xx SoC. However, BCM6338 and BCM6345
  are the only ones which don't need a pinctrl driver.

  BCM6338 have 8-bit data and dirout registers, where GPIO state can be read
  and/or written, and the direction changed from input to output.
  BCM6345 have 16-bit data and dirout registers, where GPIO state can be read
  and/or written, and the direction changed from input to output.
  BCM6318, BCM6328, BCM6358, BCM6362, BCM6368 and BCM63268 have 32-bit data
  and dirout registers, where GPIO state can be read and/or written, and the
  direction changed from input to output.

properties:
  compatible:
    enum:
      - brcm,bcm6318-gpio
      - brcm,bcm6328-gpio
      - brcm,bcm6345-gpio
      - brcm,bcm6358-gpio
      - brcm,bcm6362-gpio
      - brcm,bcm6368-gpio
      - brcm,bcm63268-gpio

  gpio-controller: true

  "#gpio-cells":
    const: 2

  gpio-ranges:
    maxItems: 1

  native-endian: true

  reg:
    maxItems: 2

  reg-names:
    items:
      - const: dirout
      - const: dat

required:
  - compatible
  - reg
  - reg-names
  - gpio-controller
  - '#gpio-cells'

additionalProperties: false

examples:
  - |
    gpio@fffe0406 {
      compatible = "brcm,bcm6345-gpio";
      reg-names = "dirout", "dat";
      reg = <0xfffe0406 2>, <0xfffe040a 2>;
      native-endian;

      gpio-controller;
      #gpio-cells = <2>;
    };

  - |
    gpio@0 {
      compatible = "brcm,bcm63268-gpio";
      reg-names = "dirout", "dat";
      reg = <0x0 0x8>, <0x8 0x8>;

      gpio-controller;
      gpio-ranges = <&pinctrl 0 0 52>;
      #gpio-cells = <2>;
    };
