# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
# Copyright 2019 Bootlin
%YAML 1.2
---
$id: "http://devicetree.org/schemas/gpio/xylon,logicvc-gpio.yaml#"
$schema: "http://devicetree.org/meta-schemas/core.yaml#"

title: Xylon LogiCVC GPIO controller

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  The LogiCVC GPIO describes the GPIO block included in the LogiCVC display
  controller. These are meant to be used for controlling display-related
  signals.

  The controller exposes GPIOs from the display and power control registers,
  which are mapped by the driver as follows:
  - <PERSON><PERSON>[4:0] (display control) mapped to index 0-4
  - EN_BLIGHT (power control) mapped to index 5
  - EN_VDD (power control) mapped to index 6
  - EN_VEE (power control) mapped to index 7
  - V_EN (power control) mapped to index 8

properties:
  $nodename:
    pattern: "^gpio@[0-9a-f]+$"

  compatible:
    enum:
      - xylon,logicvc-3.02.a-gpio

  reg:
    maxItems: 1

  "#gpio-cells":
    const: 2

  gpio-controller: true

  gpio-line-names:
    minItems: 1
    maxItems: 9

required:
  - compatible
  - reg
  - "#gpio-cells"
  - gpio-controller

additionalProperties: false

examples:
  - |
    logicvc: logicvc@43c00000 {
      compatible = "xylon,logicvc-3.02.a", "syscon", "simple-mfd";
      reg = <0x43c00000 0x6000>;

      #address-cells = <1>;
      #size-cells = <1>;

      logicvc_gpio: gpio@40 {
        compatible = "xylon,logicvc-3.02.a-gpio";
        reg = <0x40 0x40>;
        gpio-controller;
        #gpio-cells = <2>;
        gpio-line-names = "GPIO0", "GPIO1", "GPIO2", "GPIO3", "GPIO4",
               "EN_BLIGHT", "EN_VDD", "EN_VEE", "V_EN";
      };
    };
