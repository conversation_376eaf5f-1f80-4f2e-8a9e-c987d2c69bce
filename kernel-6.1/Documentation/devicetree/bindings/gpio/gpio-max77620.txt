GPIO driver for MAX77620 Power management IC from Maxim Semiconductor.

Device has 8 GPIO pins which can be configured as GPIO as well as the
special IO functions.

Required properties:
-------------------
- gpio-controller : 	Marks the device node as a gpio controller.
- #gpio-cells : 	Should be two.  The first cell is the pin number and
			the second cell is used to specify the gpio polarity:
				0 = active high
				1 = active low
For more details, please refer generic GPIO DT binding document
<devicetree/bindings/gpio/gpio.txt>.

Example:
--------
#include <dt-bindings/mfd/max77620.h>
...
max77620@3c {
	compatible = "maxim,max77620";

	gpio-controller;
	#gpio-cells = <2>;
};
