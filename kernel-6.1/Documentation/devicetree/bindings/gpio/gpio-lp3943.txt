TI/National Semiconductor LP3943 GPIO controller

Required properties:
  - compatible: "ti,lp3943-gpio"
  - gpio-controller: Marks the device node as a GPIO controller.
  - #gpio-cells: Should be 2. See gpio.txt in this directory for a
                 description of the cells format.

Example:
Simple LED controls with LP3943 GPIO controller

&i2c4 {
	lp3943@60 {
		compatible = "ti,lp3943";
		reg = <0x60>;

		gpioex: gpio {
			compatible = "ti,lp3943-gpio";
			gpio-controller;
			#gpio-cells = <2>;
		};
	};
};

leds {
	compatible = "gpio-leds";
	indicator1 {
		label = "indi1";
		gpios = <&gpioex 9 GPIO_ACTIVE_LOW>;
	};

	indicator2 {
		label = "indi2";
		gpios = <&gpioex 10 GPIO_ACTIVE_LOW>;
		default-state = "off";
	};
};
