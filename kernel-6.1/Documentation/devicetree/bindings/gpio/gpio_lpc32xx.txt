NXP LPC32xx SoC GPIO controller

Required properties:
- compatible: must be "nxp,lpc3220-gpio"
- reg: Physical base address and length of the controller's registers.
- gpio-controller: Marks the device node as a GPIO controller.
- #gpio-cells: Should be 3:
   1) bank:
      0: GPIO P0
      1: GPIO P1
      2: GPIO P2
      3: GPIO P3
      4: GPI P3
      5: GPO P3
   2) pin number
   3) optional parameters:
      - bit 0 specifies polarity (0 for normal, 1 for inverted)
- reg: Index of the GPIO group

Example:

	gpio: gpio@******** {
		compatible = "nxp,lpc3220-gpio";
		reg = <0x******** 0x1000>;
		gpio-controller;
		#gpio-cells = <3>; /* bank, pin, flags */
	};

	leds {
		compatible = "gpio-leds";

		led0 {
			gpios = <&gpio 5 1 1>; /* GPO_P3 1, active low */
			linux,default-trigger = "heartbeat";
			default-state = "off";
		};

		led1 {
			gpios = <&gpio 5 14 1>; /* GPO_P3 14, active low */
			linux,default-trigger = "timer";
			default-state = "off";
		};
	};
