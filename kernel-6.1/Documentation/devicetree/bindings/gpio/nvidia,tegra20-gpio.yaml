# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/gpio/nvidia,tegra20-gpio.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: NVIDIA Tegra GPIO Controller (Tegra20 - Tegra210)

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    oneOf:
      - enum:
          - nvidia,tegra20-gpio
          - nvidia,tegra30-gpio

      - items:
          - enum:
              - nvidia,tegra114-gpio
              - nvidia,tegra124-gpio
              - nvidia,tegra210-gpio
          - const: nvidia,tegra30-gpio

  reg:
    maxItems: 1

  interrupts:
    description: The interrupt outputs from the controller. For Tegra20,
      there should be 7 interrupts specified, and for Tegra30, there should
      be 8 interrupts specified.

  "#gpio-cells":
    description: The first cell is the pin number and the second cell is used
      to specify the GPIO polarity (0 = active high, 1 = active low).
    const: 2

  gpio-controller: true

  gpio-ranges:
    maxItems: 1

  "#interrupt-cells":
    description: |
      Should be 2. The first cell is the GPIO number. The second cell is
      used to specify flags:

        bits[3:0] trigger type and level flags:
          1 = low-to-high edge triggered.
          2 = high-to-low edge triggered.
          4 = active high level-sensitive.
          8 = active low level-sensitive.

      Valid combinations are 1, 2, 3, 4, 8.
    const: 2

  interrupt-controller: true

allOf:
  - if:
      properties:
        compatible:
          contains:
            const: nvidia,tegra30-gpio
    then:
      properties:
        interrupts:
          minItems: 8
          maxItems: 8
    else:
      properties:
        interrupts:
          minItems: 7
          maxItems: 7

required:
  - compatible
  - reg
  - interrupts
  - "#gpio-cells"
  - gpio-controller
  - "#interrupt-cells"
  - interrupt-controller

additionalProperties:
  type: object
  required:
    - gpio-hog

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>

    gpio: gpio@6000d000 {
        compatible = "nvidia,tegra20-gpio";
        reg = <0x6000d000 0x1000>;
        interrupts = <GIC_SPI 32 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 33 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 34 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 35 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 55 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 87 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 89 IRQ_TYPE_LEVEL_HIGH>;
        #gpio-cells = <2>;
        gpio-controller;
        #interrupt-cells = <2>;
        interrupt-controller;
    };
