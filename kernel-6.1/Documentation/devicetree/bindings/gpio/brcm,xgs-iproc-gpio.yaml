# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/gpio/brcm,xgs-iproc-gpio.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Broadcom XGS iProc GPIO controller

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  This controller is the Chip Common A GPIO present on a number of Broadcom
  switch ASICs with integrated SoCs.

properties:
  compatible:
    const: brcm,iproc-gpio-cca

  reg:
    items:
      - description: the I/O address containing the GPIO controller registers.
      - description: the I/O address containing the Chip Common A interrupt registers.

  gpio-controller: true

  '#gpio-cells':
    const: 2

  ngpios:
    minimum: 0
    maximum: 32

  interrupt-controller: true

  '#interrupt-cells':
    const: 2

  interrupts:
    maxItems: 1

required:
  - compatible
  - reg
  - "#gpio-cells"
  - gpio-controller

additionalProperties: false

dependencies:
  interrupt-controller: [ interrupts ]

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    gpio@18000060 {
        compatible = "brcm,iproc-gpio-cca";
        #gpio-cells = <2>;
        reg = <0x18000060 0x50>,
              <0x18000000 0x50>;
        ngpios = <12>;
        gpio-controller;
        interrupt-controller;
        #interrupt-cells = <2>;
        interrupts = <GIC_SPI 91 IRQ_TYPE_LEVEL_HIGH>;
    };


...
