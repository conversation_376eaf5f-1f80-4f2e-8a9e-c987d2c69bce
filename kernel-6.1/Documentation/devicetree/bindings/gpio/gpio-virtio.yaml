# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/gpio/gpio-virtio.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Virtio GPIO controller

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

allOf:
  - $ref: /schemas/virtio/virtio-device.yaml#

description:
  Virtio GPIO controller, see /schemas/virtio/virtio-device.yaml for more
  details.

properties:
  $nodename:
    const: gpio

  compatible:
    const: virtio,device29

  gpio-controller: true

  "#gpio-cells":
    const: 2

  interrupt-controller: true

  "#interrupt-cells":
    const: 2

required:
  - compatible
  - gpio-controller
  - "#gpio-cells"

unevaluatedProperties: false

examples:
  - |
    virtio@3000 {
        compatible = "virtio,mmio";
        reg = <0x3000 0x100>;
        interrupts = <41>;

        gpio {
            compatible = "virtio,device29";
            gpio-controller;
            #gpio-cells = <2>;
            interrupt-controller;
            #interrupt-cells = <2>;
        };
    };

...
