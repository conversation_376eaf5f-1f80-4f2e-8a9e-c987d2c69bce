# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/gpio/mediatek,mt7621-gpio.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Mediatek MT7621 SoC GPIO controller

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  The IP core used inside these SoCs has 3 banks of 32 GPIOs each.
  The registers of all the banks are interwoven inside one single IO range.
  We load one GPIO controller instance per bank. Also the GPIO controller can receive
  interrupts on any of the GPIOs, either edge or level. It then interrupts the CPU
  using GIC INT12.

properties:
  $nodename:
    pattern: "^gpio@[0-9a-f]+$"

  compatible:
    const: mediatek,mt7621-gpio

  reg:
    maxItems: 1

  "#gpio-cells":
    const: 2

  gpio-controller: true
  gpio-ranges: true

  interrupt-controller: true

  "#interrupt-cells":
    const: 2

  interrupts:
    maxItems: 1

required:
  - compatible
  - reg
  - "#gpio-cells"
  - gpio-controller
  - gpio-ranges
  - interrupt-controller
  - "#interrupt-cells"
  - interrupts

additionalProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>
    #include <dt-bindings/interrupt-controller/mips-gic.h>

    gpio@600 {
      compatible = "mediatek,mt7621-gpio";
      reg = <0x600 0x100>;
      #gpio-cells = <2>;
      gpio-controller;
      gpio-ranges = <&pinctrl 0 0 95>;
      interrupt-controller;
      #interrupt-cells = <2>;
      interrupt-parent = <&gic>;
      interrupts = <GIC_SHARED 12 IRQ_TYPE_LEVEL_HIGH>;
    };

...
