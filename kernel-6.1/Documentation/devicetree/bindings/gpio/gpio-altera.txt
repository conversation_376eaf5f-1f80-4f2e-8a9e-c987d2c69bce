Altera GPIO controller bindings

Required properties:
- compatible:
  - "altr,pio-1.0"
- reg: Physical base address and length of the controller's registers.
- #gpio-cells : Should be 2
  - The first cell is the gpio offset number.
  - The second cell is reserved and is currently unused.
- gpio-controller : Marks the device node as a GPIO controller.
- interrupt-controller: Mark the device node as an interrupt controller
- #interrupt-cells : Should be 2. The interrupt type is fixed in the hardware.
  - The first cell is the GPIO offset number within the GPIO controller.
  - The second cell is the interrupt trigger type and level flags.
- interrupts: Specify the interrupt.
- altr,interrupt-type: Specifies the interrupt trigger type the GPIO
  hardware is synthesized. This field is required if the Altera GPIO controller
  used has IRQ enabled as the interrupt type is not software controlled,
  but hardware synthesized. Required if GPIO is used as an interrupt
  controller. The value is defined in <dt-bindings/interrupt-controller/irq.h>
  Only the following flags are supported:
    IRQ_TYPE_EDGE_RISING
    IRQ_TYPE_EDGE_FALLING
    IRQ_TYPE_EDGE_BOTH
    IRQ_TYPE_LEVEL_HIGH

Optional properties:
- altr,ngpio: Width of the GPIO bank. This defines how many pins the
  GPIO device has. Ranges between 1-32. Optional and defaults to 32 if not
  specified.

Example:

gpio_altr: gpio@ff200000 {
	compatible = "altr,pio-1.0";
	reg = <0xff200000 0x10>;
	interrupts = <0 45 4>;
	altr,ngpio = <32>;
	altr,interrupt-type = <IRQ_TYPE_EDGE_RISING>;
	#gpio-cells = <2>;
	gpio-controller;
	#interrupt-cells = <2>;
	interrupt-controller;
};
