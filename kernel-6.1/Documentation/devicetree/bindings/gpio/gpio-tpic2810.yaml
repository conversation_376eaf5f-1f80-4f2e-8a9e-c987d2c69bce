# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/gpio/gpio-tpic2810.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: TPIC2810 GPIO controller bindings

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - ti,tpic2810

  reg:
    maxItems: 1

  gpio-controller: true

  "#gpio-cells":
    const: 2

  gpio-line-names:
    minItems: 1
    maxItems: 32

required:
  - compatible
  - reg
  - gpio-controller
  - "#gpio-cells"

additionalProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>

    i2c {
        #address-cells = <1>;
        #size-cells = <0>;
        gpio@60 {
            compatible = "ti,tpic2810";
            reg = <0x60>;
            gpio-controller;
            #gpio-cells = <2>;
            gpio-line-names = "LED A", "LED B", "LED C";
        };
    };
