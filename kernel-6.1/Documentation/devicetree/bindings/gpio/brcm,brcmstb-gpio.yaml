# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/gpio/brcm,brcmstb-gpio.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Broadcom STB "UPG GIO" GPIO controller

description: >
  The controller's registers are organized as sets of eight 32-bit
  registers with each set controlling a bank of up to 32 pins.  A single
  interrupt is shared for all of the banks handled by the controller.

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    items:
      - enum:
          - brcm,bcm7445-gpio
      - const: brcm,brcmstb-gpio

  reg:
    maxItems: 1
    description: >
      Define the base and range of the I/O address space containing
      the brcmstb GPIO controller registers

  "#gpio-cells":
    const: 2
    description: >
      The first cell is the pin number (within the controller's
      pin space), and the second is used for the following:
      bit[0]: polarity (0 for active-high, 1 for active-low)

  gpio-controller: true

  brcm,gpio-bank-widths:
    $ref: /schemas/types.yaml#/definitions/uint32-array
    description: >
      Number of GPIO lines for each bank.  Number of elements must
      correspond to number of banks suggested by the 'reg' property.

  interrupts:
    maxItems: 1
    description: >
      The interrupt shared by all GPIO lines for this controller.

  "#interrupt-cells":
    const: 2
    description: |
      The first cell is the GPIO number, the second should specify
      flags.  The following subset of flags is supported:
      - bits[3:0] trigger type and level flags
        1 = low-to-high edge triggered
        2 = high-to-low edge triggered
        4 = active high level-sensitive
        8 = active low level-sensitive
      Valid combinations are 1, 2, 3, 4, 8.

  interrupt-controller: true

  wakeup-source:
    type: boolean
    description: >
      GPIOs for this controller can be used as a wakeup source

required:
  - compatible
  - reg
  - gpio-controller
  - "#gpio-cells"
  - "brcm,gpio-bank-widths"

additionalProperties: false

examples:
  - |
    upg_gio: gpio@f040a700 {
        #gpio-cells = <2>;
        #interrupt-cells = <2>;
        compatible = "brcm,bcm7445-gpio", "brcm,brcmstb-gpio";
        gpio-controller;
        interrupt-controller;
        reg = <0xf040a700 0x80>;
        interrupt-parent = <&irq0_intc>;
        interrupts = <0x6>;
        brcm,gpio-bank-widths = <32 32 32 24>;
    };

    upg_gio_aon: gpio@f04172c0 {
        #gpio-cells = <2>;
        #interrupt-cells = <2>;
        compatible = "brcm,bcm7445-gpio", "brcm,brcmstb-gpio";
        gpio-controller;
        interrupt-controller;
        reg = <0xf04172c0 0x40>;
        interrupt-parent = <&irq0_aon_intc>;
        interrupts = <0x6>;
        wakeup-source;
        brcm,gpio-bank-widths = <18 4>;
    };
