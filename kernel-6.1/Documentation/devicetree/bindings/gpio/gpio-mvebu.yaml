# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/gpio/gpio-mvebu.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: <PERSON>l EBU GPIO controller

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    oneOf:
      - enum:
          - marvell,armada-8k-gpio
          - marvell,orion-gpio

      - items:
          - enum:
              - marvell,mv78200-gpio
              - marvell,armada-370-gpio
          - const: marvell,orion-gpio

      - description: Deprecated binding
        items:
          - const: marvell,armadaxp-gpio
          - const: marvell,orion-gpio
        deprecated: true

  reg:
    description: |
      Address and length of the register set for the device. Not used for
      marvell,armada-8k-gpio.

      A second entry can be provided, for the PWM function using the GPIO Blink
      Counter on/off registers.
    minItems: 1
    maxItems: 2

  reg-names:
    items:
      - const: gpio
      - const: pwm
    minItems: 1

  offset:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: Offset in the register map for the gpio registers (in bytes)

  interrupts:
    description: |
      The list of interrupts that are used for all the pins managed by this
      GPIO bank. There can be more than one interrupt (example: 1 interrupt
      per 8 pins on Armada XP, which means 4 interrupts per bank of 32
      GPIOs).
    minItems: 1
    maxItems: 4

  interrupt-controller: true

  "#interrupt-cells":
    const: 2

  gpio-controller: true

  ngpios:
    minimum: 1
    maximum: 32

  "#gpio-cells":
    const: 2

  marvell,pwm-offset:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: Offset in the register map for the pwm registers (in bytes)

  "#pwm-cells":
    description:
      The first cell is the GPIO line number. The second cell is the period
      in nanoseconds.
    const: 2

  clocks:
    description:
      Clock(s) used for PWM function.
    items:
      - description: Core clock
      - description: AXI bus clock
    minItems: 1

  clock-names:
    items:
      - const: core
      - const: axi
    minItems: 1

required:
  - compatible
  - gpio-controller
  - ngpios
  - "#gpio-cells"

allOf:
  - if:
      properties:
        compatible:
          contains:
            const: marvell,armada-8k-gpio
    then:
      required:
        - offset
    else:
      required:
        - reg

unevaluatedProperties: true

examples:
  - |
    gpio@d0018100 {
      compatible = "marvell,armadaxp-gpio", "marvell,orion-gpio";
      reg = <0xd0018100 0x40>, <0xd0018800 0x30>;
      ngpios = <32>;
      gpio-controller;
      #gpio-cells = <2>;
      interrupt-controller;
      #interrupt-cells = <2>;
      interrupts = <16>, <17>, <18>, <19>;
    };

  - |
    gpio@18140 {
      compatible = "marvell,armada-370-gpio", "marvell,orion-gpio";
      reg = <0x18140 0x40>, <0x181c8 0x08>;
      reg-names = "gpio", "pwm";
      ngpios = <17>;
      gpio-controller;
      #gpio-cells = <2>;
      #pwm-cells = <2>;
      interrupt-controller;
      #interrupt-cells = <2>;
      interrupts = <87>, <88>, <89>;
      clocks = <&coreclk 0>;
    };
