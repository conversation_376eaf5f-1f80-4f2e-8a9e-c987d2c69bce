# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/gnss/mediatek.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Mediatek GNSS Receiver

maintainers:
  - <PERSON> <<EMAIL>>

description:
  Mediatek chipsets are used in GNSS-receiver modules produced by several
  vendors and can use a UART interface.

allOf:
  - $ref: gnss-common.yaml#

properties:
  compatible:
    const: globaltop,pa6h

  vcc-supply:
    description:
      Main voltage regulator, pin name VCC.

  reset-gpios:
    maxItems: 1
    description: An optional reset line, with names such as RESET or NRESET.
      If the line is active low it should be flagged with GPIO_ACTIVE_LOW.

  timepulse-gpios:
    description: Comes with pin names such as PPS1 or 1PPS.

  gnss-fix-gpios:
    maxItems: 1
    description: GPIO used to determine device position fix state, pin names
      FIX or 3D_FIX.

  vbackup-supply:
    description:
      Regulator providing backup voltage, pin names such as VBAT or VBACKUP.

required:
  - compatible
  - vcc-supply

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>
    serial {
        gnss {
            compatible = "globaltop,pa6h";
            vcc-supply = <&vcc_3v3>;
            reset-gpios = <&gpio 1 GPIO_ACTIVE_LOW>;
        };
    };
