# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/gnss/gnss-common.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Common Properties for Global Navigation Satellite Systems (GNSS)
  receiver devices

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  This document defines device tree properties common to Global Navigation
  Satellite System receivers.

properties:
  $nodename:
    pattern: "^gnss(@.*)?$"

  lna-supply:
    description: A separate regulator supplying power for the Low Noise
      Amplifier (LNA). This is an amplifier connected between the GNSS
      device and the receiver antenna.

  enable-gpios:
    description: A GPIO line that will enable the GNSS receiver when
      asserted. If this line is active low, the GPIO phandle should
      consequently be tagged with the GPIO_ACTIVE_LOW flag so the operating
      system can rely on asserting the line to enable the GNSS device.
    maxItems: 1

  timepulse-gpios:
    description: When a timepulse is provided to the GNSS device using a
      GPIO line, this is used.
    maxItems: 1

  current-speed:
    description: The baudrate in bits per second of the device as it comes
      online, current active speed.
    $ref: /schemas/types.yaml#/definitions/uint32

additionalProperties: true

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>
    serial {
      gnss {
        compatible = "u-blox,neo-8";
        vcc-supply = <&gnss_reg>;
        timepulse-gpios = <&gpio0 16 GPIO_ACTIVE_HIGH>;
        current-speed = <4800>;
      };
    };
