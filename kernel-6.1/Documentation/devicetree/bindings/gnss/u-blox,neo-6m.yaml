# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/gnss/u-blox,neo-6m.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: U-blox GNSS Receiver

allOf:
  - $ref: gnss-common.yaml#

maintainers:
  - <PERSON> <<EMAIL>>

description: >
  The U-blox GNSS receivers can use UART, DDC (I2C), SPI and USB interfaces.

properties:
  compatible:
    enum:
      - u-blox,neo-6m
      - u-blox,neo-8
      - u-blox,neo-m8

  reg:
    description: >
      The DDC Slave Address, SPI chip select address, the number of the USB hub
      port or the USB host-controller port to which this device is attached,
      depending on the bus used. Required for the DDC, SPI or USB busses.

  vcc-supply:
    description: >
      Main voltage regulator

  u-blox,extint-gpios:
    maxItems: 1
    description: >
      GPIO connected to the "external interrupt" input pin

  v-bckp-supply:
    description: >
      Backup voltage regulator

required:
  - compatible
  - vcc-supply

unevaluatedProperties: false

examples:
  - |
    serial {
        gnss {
            compatible = "u-blox,neo-8";
            v-bckp-supply = <&gnss_v_bckp_reg>;
            vcc-supply = <&gnss_vcc_reg>;
        };
    };
