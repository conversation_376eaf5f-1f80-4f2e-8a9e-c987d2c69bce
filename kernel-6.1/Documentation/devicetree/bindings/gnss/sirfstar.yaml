# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/gnss/sirfstar.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: SiRFstar GNSS Receiver

maintainers:
  - <PERSON> <<EMAIL>>

description:
  The SiRFstar GNSS receivers have incarnated over the years in different
  chips, starting from the SiRFstarIII which was a chip that was introduced in
  2004 and used in a lot of dedicated GPS devices. In 2009 SiRF was acquired
  by CSR (Cambridge Silicon Radio) and in 2012 the CSR GPS business was
  acquired by Samsung, while some products remained with CSR. In 2014 CSR
  was acquired by Qualcomm who still sell some of the SiRF products.

  SiRF chips can be used over UART, I2C or SPI buses.

allOf:
  - $ref: gnss-common.yaml#

properties:
  compatible:
    enum:
      - csr,gsd4t
      - csr,csrg05ta03-icje-r
      - fastrax,uc430
      - linx,r4
      - wi2wi,w2sg0004
      - wi2wi,w2sg0008i
      - wi2wi,w2sg0084i

  reg:
    description:
      The I2C Address, SPI chip select address. Not required on UART buses.

  vcc-supply:
    description:
      Main voltage regulator, pin names such as 3V3_IN, VCC, VDD.

  reset-gpios:
    maxItems: 1
    description: An optional active low reset line, should be flagged with
      GPIO_ACTIVE_LOW.

  sirf,onoff-gpios:
    maxItems: 1
    description: GPIO used to power on and off device, pin name ON_OFF.

  sirf,wakeup-gpios:
    maxItems: 1
    description: GPIO used to determine device power state, pin names such
      as RFPWRUP, WAKEUP.

required:
  - compatible
  - vcc-supply

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>
    serial {
        gnss {
            compatible = "wi2wi,w2sg0084i";
            vcc-supply = <&gnss_vcc_reg>;
            reset-gpios = <&gpio0 15 GPIO_ACTIVE_LOW>;
            sirf,onoff-gpios = <&gpio0 16 GPIO_ACTIVE_HIGH>;
            sirf,wakeup-gpios = <&gpio0 17 GPIO_ACTIVE_HIGH>;
            current-speed = <38400>;
        };
    };
