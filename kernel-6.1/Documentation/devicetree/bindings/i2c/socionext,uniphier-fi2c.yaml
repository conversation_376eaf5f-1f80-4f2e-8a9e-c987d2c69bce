# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/i2c/socionext,uniphier-fi2c.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: UniPhier I2C controller (FIFO-builtin)

maintainers:
  - <PERSON><PERSON><PERSON> <yamada.ma<PERSON><EMAIL>>

allOf:
  - $ref: /schemas/i2c/i2c-controller.yaml#

properties:
  compatible:
    const: socionext,uniphier-fi2c

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks:
    maxItems: 1

  clock-frequency:
    minimum: 100000
    maximum: 400000

required:
  - compatible
  - reg
  - "#address-cells"
  - "#size-cells"
  - interrupts
  - clocks

unevaluatedProperties: false

examples:
  - |
    i2c0: i2c@58780000 {
        compatible = "socionext,uniphier-fi2c";
        reg = <0x58780000 0x80>;
        #address-cells = <1>;
        #size-cells = <0>;
        interrupts = <0 41 4>;
        clocks = <&i2c_clk>;
        clock-frequency = <100000>;
    };
