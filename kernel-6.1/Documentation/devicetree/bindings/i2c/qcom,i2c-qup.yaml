# SPDX-License-Identifier: GPL-2.0 OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/i2c/qcom,i2c-qup.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Universal Peripheral (QUP) I2C controller

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON><PERSON> <bjorn.and<PERSON><PERSON>@linaro.org>
  - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>

allOf:
  - $ref: /schemas/i2c/i2c-controller.yaml#

properties:
  compatible:
    enum:
      - qcom,i2c-qup-v1.1.1     # for 8660, 8960 and 8064
      - qcom,i2c-qup-v2.1.1     # for 8974 v1
      - qcom,i2c-qup-v2.2.1     # for 8974 v2 and later

  clocks:
    maxItems: 2

  clock-names:
    items:
      - const: core
      - const: iface

  clock-frequency:
    default: 100000

  dmas:
    maxItems: 2

  dma-names:
    items:
      - const: tx
      - const: rx

  interrupts:
    maxItems: 1

  pinctrl-0: true
  pinctrl-1: true

  pinctrl-names:
    minItems: 1
    items:
      - const: default
      - const: sleep

  reg:
    maxItems: 1

required:
  - compatible
  - clock-names
  - clocks
  - interrupts
  - reg

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,gcc-msm8998.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>

    i2c@c175000 {
        compatible = "qcom,i2c-qup-v2.2.1";
        reg = <0x0c175000 0x600>;
        interrupts = <GIC_SPI 95 IRQ_TYPE_LEVEL_HIGH>;

        clocks = <&gcc GCC_BLSP1_QUP1_I2C_APPS_CLK>,
                 <&gcc GCC_BLSP1_AHB_CLK>;
        clock-names = "core", "iface";
        dmas = <&blsp1_dma 6>, <&blsp1_dma 7>;
        dma-names = "tx", "rx";
        pinctrl-names = "default", "sleep";
        pinctrl-0 = <&blsp1_i2c1_default>;
        pinctrl-1 = <&blsp1_i2c1_sleep>;
        clock-frequency = <400000>;

        #address-cells = <1>;
        #size-cells = <0>;
    };
