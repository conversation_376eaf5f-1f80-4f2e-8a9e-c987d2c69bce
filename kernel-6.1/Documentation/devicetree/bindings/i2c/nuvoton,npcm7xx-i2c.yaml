# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/i2c/nuvoton,npcm7xx-i2c.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: nuvoton NPCM7XX I2C Controller

description: |
  I2C bus controllers of the NPCM series support both master and
  slave mode. Each controller can switch between master and slave at run time
  (i.e. IPMB mode). HW FIFO for TX and RX are supported.

maintainers:
  - <PERSON><PERSON> <PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - nuvoton,npcm750-i2c
      - nuvoton,npcm845-i2c

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks:
    maxItems: 1
    description: Reference clock for the I2C bus

  clock-frequency:
    description: Desired I2C bus clock frequency in Hz. If not specified,
                 the default 100 kHz frequency will be used.
                 possible values are 100000, 400000 and 1000000.
    default: 100000
    enum: [100000, 400000, 1000000]

  nuvoton,sys-mgr:
    $ref: /schemas/types.yaml#/definitions/phandle
    description: The phandle of system manager register node.

required:
  - compatible
  - reg
  - interrupts
  - clocks

allOf:
  - $ref: /schemas/i2c/i2c-controller.yaml#
  - if:
      properties:
        compatible:
          contains:
            const: nuvoton,npcm845-i2c

    then:
      required:
        - nuvoton,sys-mgr

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/nuvoton,npcm7xx-clock.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    i2c0: i2c@80000 {
        reg = <0x80000 0x1000>;
        clocks = <&clk NPCM7XX_CLK_APB2>;
        clock-frequency = <100000>;
        interrupts = <GIC_SPI 64 IRQ_TYPE_LEVEL_HIGH>;
        compatible = "nuvoton,npcm750-i2c";
        nuvoton,sys-mgr = <&gcr>;
    };

...
