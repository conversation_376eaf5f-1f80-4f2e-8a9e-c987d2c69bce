Generic device tree bindings for I2C busses
===========================================

This document describes generic bindings which can be used to describe I2C
busses and their child devices in a device tree.

Required properties (per bus)
-----------------------------

- #address-cells  - should be <1>. Read more about addresses below.
- #size-cells     - should be <0>.
- compatible      - name of I2C bus controller

For other required properties e.g. to describe register sets,
clocks, etc. check the binding documentation of the specific driver.

The cells properties above define that an address of children of an I2C bus
are described by a single value.

Optional properties (per bus)
-----------------------------

These properties may not be supported by all drivers. However, if a driver
wants to support one of the below features, it should adapt these bindings.

- clock-frequency
	frequency of bus clock in Hz.

- i2c-bus
	For I2C adapters that have child nodes that are a mixture of both I2C
	devices and non-I2C devices, the 'i2c-bus' subnode can be used for
	populating I2C devices. If the 'i2c-bus' subnode is present, only
	subnodes of this will be considered as I2C slaves. The properties,
	'#address-cells' and '#size-cells' must be defined under this subnode
	if present.

- i2c-scl-falling-time-ns
	Number of nanoseconds the SCL signal takes to fall; t(f) in the I2C
	specification.

- i2c-scl-internal-delay-ns
	Number of nanoseconds the IP core additionally needs to setup SCL.

- i2c-scl-rising-time-ns
	Number of nanoseconds the SCL signal takes to rise; t(r) in the I2C
	specification.

- i2c-sda-falling-time-ns
	Number of nanoseconds the SDA signal takes to fall; t(f) in the I2C
	specification.

- i2c-analog-filter
	Enable analog filter for i2c lines.

- i2c-digital-filter
	Enable digital filter for i2c lines.

- i2c-digital-filter-width-ns
	Width of spikes which can be filtered by digital filter
	(i2c-digital-filter). This width is specified in nanoseconds.

- i2c-analog-filter-cutoff-frequency
	Frequency that the analog filter (i2c-analog-filter) uses to distinguish
	which signal to filter. Signal with higher frequency than specified will
	be filtered out. Only lower frequency will pass (this is applicable to
	a low-pass analog filter). Typical value should be above the normal
	i2c bus clock frequency (clock-frequency).
	Specified in Hz.

- multi-master
	states that there is another master active on this bus. The OS can use
	this information to adapt power management to keep the arbitration awake
	all the time, for example. Can not be combined with 'single-master'.

- pinctrl
	add extra pinctrl to configure SCL/SDA pins to GPIO function for bus
	recovery, call it "gpio" or "recovery" (deprecated) state

- scl-gpios
	specify the gpio related to SCL pin. Used for GPIO bus recovery.

- sda-gpios
	specify the gpio related to SDA pin. Optional for GPIO bus recovery.

- single-master
	states that there is no other master active on this bus. The OS can use
	this information to detect a stalled bus more reliably, for example.
	Can not be combined with 'multi-master'.

- smbus
	states that additional SMBus restrictions and features apply to this bus.
	An example of feature is SMBusHostNotify. Examples of restrictions are
	more reserved addresses and timeout definitions.

- smbus-alert
	states that the optional SMBus-Alert feature apply to this bus.

- mctp-controller
	indicates that the system is accessible via this bus as an endpoint for
	MCTP over I2C transport.

Required properties (per child device)
--------------------------------------

- compatible
	name of I2C slave device

- reg
	One or many I2C slave addresses. These are usually a 7 bit addresses.
	However, flags can be attached to an address. I2C_TEN_BIT_ADDRESS is
	used to mark a 10 bit address. It is needed to avoid the ambiguity
	between e.g. a 7 bit address of 0x50 and a 10 bit address of 0x050
	which, in theory, can be on the same bus.
	Another flag is I2C_OWN_SLAVE_ADDRESS to mark addresses on which we
	listen to be devices ourselves.

Optional properties (per child device)
--------------------------------------

These properties may not be supported by all drivers. However, if a driver
wants to support one of the below features, it should adapt these bindings.

- host-notify
	device uses SMBus host notify protocol instead of interrupt line.

- interrupts
	interrupts used by the device.

- interrupt-names
	"irq", "wakeup" and "smbus_alert" names are recognized by I2C core,
	other names are	left to individual drivers.

- reg-names
	Names of map programmable addresses.
	It can contain any map needing another address than default one.

- wakeup-source
	device can be used as a wakeup source.

Binding may contain optional "interrupts" property, describing interrupts
used by the device. I2C core will assign "irq" interrupt (or the very first
interrupt if not using interrupt names) as primary interrupt for the slave.

Alternatively, devices supporting SMBus Host Notify, and connected to
adapters that support this feature, may use "host-notify" property. I2C
core will create a virtual interrupt for Host Notify and assign it as
primary interrupt for the slave.

Also, if device is marked as a wakeup source, I2C core will set up "wakeup"
interrupt for the device. If "wakeup" interrupt name is not present in the
binding, then primary interrupt will be used as wakeup interrupt.
