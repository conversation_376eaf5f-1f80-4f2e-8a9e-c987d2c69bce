CE4100 I2C
----------

CE4100 has one PCI device which is described as the I2C-Controller. This
PCI device has three PCI-bars, each bar contains a complete I2C
controller. So we have a total of three independent I2C-Controllers
which share only an interrupt line.
The driver is probed via the PCI-ID and is gathering the information of
attached devices from the devices tree.
<PERSON> Like<PERSON> recommended to use the ranges property to map the PCI-Bar
number to its physical address and to use this to find the child nodes
of the specific I2C controller. This were his exact words:

       Here's where the magic happens.  Each entry in
       ranges describes how the parent pci address space
       (middle group of 3) is translated to the local
       address space (first group of 2) and the size of
       each range (last cell).  In this particular case,
       the first cell of the local address is chosen to be
       1:1 mapped to the BARs, and the second is the
       offset from be base of the BAR (which would be
       non-zero if you had 2 or more devices mapped off
       the same BAR)

       ranges allows the address mapping to be described
       in a way that the OS can interpret without
       requiring custom device driver code.

This is an example which is used on FalconFalls:
------------------------------------------------
	i2c-controller@b,2 {
		#address-cells = <2>;
		#size-cells = <1>;
		compatible = "pci8086,2e68.2",
				"pci8086,2e68",
				"pciclass,ff0000",
				"pciclass,ff00";

		reg = <0x15a00 0x0 0x0 0x0 0x0>;
		interrupts = <16 1>;

		/* as described by Grant, the first number in the group of
		* three is the bar number followed by the 64bit bar address
		* followed by size of the mapping. The bar address
		* requires also a valid translation in parents ranges
		* property.
		*/
		ranges = <0 0   0x02000000 0 0xdffe0500 0x100
			  1 0   0x02000000 0 0xdffe0600 0x100
			  2 0   0x02000000 0 0xdffe0700 0x100>;

		i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "intel,ce4100-i2c-controller";

			/* The first number in the reg property is the
			* number of the bar
			*/
			reg = <0 0 0x100>;

			/* This I2C controller has no devices */
		};

		i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "intel,ce4100-i2c-controller";
			reg = <1 0 0x100>;

			/* This I2C controller has one gpio controller */
			gpio@26 {
				#gpio-cells = <2>;
				compatible = "nxp,pcf8575";
				reg = <0x26>;
				gpio-controller;
			};
		};

		i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "intel,ce4100-i2c-controller";
			reg = <2 0 0x100>;

			gpio@26 {
				#gpio-cells = <2>;
				compatible = "nxp,pcf8575";
				reg = <0x26>;
				gpio-controller;
			};
		};
	};
