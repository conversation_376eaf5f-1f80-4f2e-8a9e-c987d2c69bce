# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/i2c/qcom,i2c-geni-qcom.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Geni based QUP I2C Controller

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON><PERSON> <bjorn.and<PERSON><PERSON>@linaro.org>

allOf:
  - $ref: /schemas/i2c/i2c-controller.yaml#

properties:
  compatible:
    const: qcom,geni-i2c

  clocks:
    maxItems: 1

  clock-names:
    const: se

  clock-frequency:
    default: 100000

  dmas:
    maxItems: 2

  dma-names:
    items:
      - const: tx
      - const: rx

  interconnects:
    maxItems: 3

  interconnect-names:
    items:
      - const: qup-core
      - const: qup-config
      - const: qup-memory

  interrupts:
    maxItems: 1

  pinctrl-0: true
  pinctrl-1: true

  pinctrl-names:
    minItems: 1
    items:
      - const: default
      - const: sleep

  power-domains:
    maxItems: 1

  reg:
    maxItems: 1

  required-opps:
    maxItems: 1

required:
  - compatible
  - interrupts
  - clocks
  - clock-names
  - reg

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/clock/qcom,gcc-sc7180.h>
    #include <dt-bindings/interconnect/qcom,sc7180.h>
    #include <dt-bindings/power/qcom-rpmpd.h>

    i2c@88000 {
        compatible = "qcom,geni-i2c";
        reg = <0x00880000 0x4000>;
        clock-names = "se";
        clocks = <&gcc GCC_QUPV3_WRAP0_S0_CLK>;
        pinctrl-names = "default";
        pinctrl-0 = <&qup_i2c0_default>;
        interrupts = <GIC_SPI 601 IRQ_TYPE_LEVEL_HIGH>;
        #address-cells = <1>;
        #size-cells = <0>;
        interconnects = <&qup_virt MASTER_QUP_CORE_0 0 &qup_virt SLAVE_QUP_CORE_0 0>,
                        <&gem_noc MASTER_APPSS_PROC 0 &config_noc SLAVE_QUP_0 0>,
                        <&aggre1_noc MASTER_QUP_0 0 &mc_virt SLAVE_EBI1 0>;
        interconnect-names = "qup-core", "qup-config", "qup-memory";
        power-domains = <&rpmhpd SC7180_CX>;
        required-opps = <&rpmhpd_opp_low_svs>;
    };
...
