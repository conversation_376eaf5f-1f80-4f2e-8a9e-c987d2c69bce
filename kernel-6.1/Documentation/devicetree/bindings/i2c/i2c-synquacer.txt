Socionext SynQuacer I2C

Required properties:
- compatible      : Must be "socionext,synquacer-i2c"
- reg             : Offset and length of the register set for the device
- interrupts      : A single interrupt specifier
- #address-cells  : Must be <1>;
- #size-cells     : Must be <0>;
- clock-names     : Must contain "pclk".
- clocks          : Must contain an entry for each name in clock-names.
                    (See the common clock bindings.)

Optional properties:
- clock-frequency : Desired I2C bus clock frequency in Hz. As only Normal and
                    Fast modes are supported, possible values are 100000 and
                    400000.

Example :

    i2c@51210000 {
        compatible = "socionext,synquacer-i2c";
        reg = <0x51210000 0x1000>;
        interrupts = <GIC_SPI 165 IRQ_TYPE_LEVEL_HIGH>;
        #address-cells = <1>;
        #size-cells = <0>;
        clock-names = "pclk";
        clocks = <&clk_i2c>;
        clock-frequency = <400000>;
    };
