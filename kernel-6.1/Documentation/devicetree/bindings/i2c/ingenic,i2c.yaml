# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/i2c/ingenic,i2c.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Ingenic SoCs I2C controller devicetree bindings

maintainers:
  - <PERSON> <<EMAIL>>

allOf:
  - $ref: /schemas/i2c/i2c-controller.yaml#

properties:
  $nodename:
    pattern: "^i2c@[0-9a-f]+$"

  compatible:
    oneOf:
      - enum:
          - ingenic,jz4770-i2c
          - ingenic,x1000-i2c
      - items:
          - const: ingenic,jz4780-i2c
          - const: ingenic,jz4770-i2c

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks:
    maxItems: 1

  clock-frequency:
    enum: [ 100000, 400000 ]

  dmas:
    items:
      - description: DMA controller phandle and request line for RX
      - description: DMA controller phandle and request line for TX

  dma-names:
    items:
      - const: rx
      - const: tx

required:
  - compatible
  - reg
  - interrupts
  - clocks
  - clock-frequency
  - dmas
  - dma-names

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/ingenic,jz4780-cgu.h>
    #include <dt-bindings/dma/jz4780-dma.h>
    #include <dt-bindings/interrupt-controller/irq.h>
    i2c@10054000 {
      compatible = "ingenic,jz4780-i2c", "ingenic,jz4770-i2c";
      #address-cells = <1>;
      #size-cells = <0>;
      reg = <0x10054000 0x1000>;

      interrupts = <56 IRQ_TYPE_LEVEL_LOW>;

      clocks = <&cgu JZ4780_CLK_SMB4>;
      pinctrl-names = "default";
      pinctrl-0 = <&pins_i2c4_data>;

      dmas = <&dma JZ4780_DMA_SMB4_RX 0xffffffff>,
             <&dma JZ4780_DMA_SMB4_TX 0xffffffff>;
      dma-names = "rx", "tx";

      clock-frequency = <400000>;

      rtc@51 {
        compatible = "nxp,pcf8563";
        reg = <0x51>;

        interrupts = <30 IRQ_TYPE_LEVEL_LOW>;
      };
    };
