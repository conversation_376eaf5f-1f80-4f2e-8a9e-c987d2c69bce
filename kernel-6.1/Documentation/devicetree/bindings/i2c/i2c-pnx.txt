* NXP PNX I2C Controller

Required properties:

 - reg: Offset and length of the register set for the device
 - compatible: should be "nxp,pnx-i2c"
 - interrupts: configure one interrupt line
 - #address-cells: always 1 (for i2c addresses)
 - #size-cells: always 0

Optional properties:

 - clock-frequency: desired I2C bus clock frequency in Hz, Default: 100000 Hz

Examples:

	i2c1: i2c@400a0000 {
		compatible = "nxp,pnx-i2c";
		reg = <0x400a0000 0x100>;
		interrupt-parent = <&mic>;
		interrupts = <51 0>;
		#address-cells = <1>;
		#size-cells = <0>;
	};

	i2c2: i2c@400a8000 {
		compatible = "nxp,pnx-i2c";
		reg = <0x400a8000 0x100>;
		interrupt-parent = <&mic>;
		interrupts = <50 0>;
		#address-cells = <1>;
		#size-cells = <0>;
		clock-frequency = <100000>;
	};
