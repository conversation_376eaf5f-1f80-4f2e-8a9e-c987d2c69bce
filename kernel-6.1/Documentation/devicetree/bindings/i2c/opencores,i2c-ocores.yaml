# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/i2c/opencores,i2c-ocores.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: OpenCores I2C controller

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

allOf:
  - $ref: /schemas/i2c/i2c-controller.yaml#

properties:
  compatible:
    oneOf:
      - items:
          - enum:
              - sifive,fu740-c000-i2c # Opencore based IP block FU740-C000 SoC
              - sifive,fu540-c000-i2c # Opencore based IP block FU540-C000 SoC
          - const: sifive,i2c0
      - enum:
          - opencores,i2c-ocores
          - aeroflex<PERSON>sler,i2cmst

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks:
    maxItems: 1

  clock-frequency:
    description: |
      clock-frequency property is meant to control the bus frequency for i2c bus
      drivers, but it was incorrectly used to specify i2c controller input clock
      frequency. So the following rules are set to fix this situation:
      - if clock-frequency is present and neither opencores,ip-clock-frequency nor
        clocks are, then clock-frequency specifies i2c controller clock frequency.
        This is to keep backwards compatibility with setups using old DTB. i2c bus
        frequency is fixed at 100 KHz.
      - if clocks is present it specifies i2c controller clock. clock-frequency
        property specifies i2c bus frequency.
      - if opencores,ip-clock-frequency is present it specifies i2c controller
        clock frequency. clock-frequency property specifies i2c bus frequency.
    default: 100000

  reg-io-width:
    description: |
      io register width in bytes
    enum: [1, 2, 4]

  reg-shift:
    description: |
      device register offsets are shifted by this value
    default: 0

  regstep:
    description: |
      deprecated, use reg-shift above
    deprecated: true

  opencores,ip-clock-frequency:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: |
      Frequency of the controller clock in Hz. Mutually exclusive with clocks.
      See the note above.

required:
  - compatible
  - reg
  - "#address-cells"
  - "#size-cells"

oneOf:
  - required:
      - opencores,ip-clock-frequency
  - required:
      - clocks

unevaluatedProperties: false

examples:
  - |
    i2c@a0000000 {
      compatible = "opencores,i2c-ocores";
      reg = <0xa0000000 0x8>;
      #address-cells = <1>;
      #size-cells = <0>;
      interrupts = <10>;
      opencores,ip-clock-frequency = <20000000>;

      reg-shift = <0>;	/* 8 bit registers */
      reg-io-width = <1>;	/* 8 bit read/write */
    };

    i2c@b0000000 {
      compatible = "opencores,i2c-ocores";
      reg = <0xa0000000 0x8>;
      #address-cells = <1>;
      #size-cells = <0>;
      interrupts = <10>;
      clocks = <&osc>;
      clock-frequency = <400000>; /* i2c bus frequency 400 KHz */

      reg-shift = <0>;	/* 8 bit registers */
      reg-io-width = <1>;	/* 8 bit read/write */
    };
...
