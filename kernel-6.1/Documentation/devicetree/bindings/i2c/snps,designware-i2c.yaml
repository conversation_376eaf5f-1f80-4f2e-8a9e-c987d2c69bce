# SPDX-License-Identifier: GPL-2.0-only
%YAML 1.2
---
$id: http://devicetree.org/schemas/i2c/snps,designware-i2c.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Synopsys DesignWare APB I2C Controller

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

allOf:
  - $ref: /schemas/i2c/i2c-controller.yaml#
  - if:
      properties:
        compatible:
          not:
            contains:
              const: mscc,ocelot-i2c
    then:
      properties:
        reg:
          maxItems: 1

properties:
  compatible:
    oneOf:
      - description: Generic Synopsys DesignWare I2C controller
        const: snps,designware-i2c
      - description: Microsemi Ocelot SoCs I2C controller
        items:
          - const: mscc,ocelot-i2c
          - const: snps,designware-i2c
      - description: Baikal-T1 SoC System I2C controller
        const: baikal,bt1-sys-i2c

  reg:
    minItems: 1
    items:
      - description: DW APB I2C controller memory mapped registers
      - description: |
          ICPU_CFG:TWI_DELAY registers to setup the SDA hold time.
          This registers are specific to the Ocelot I2C-controller.

  interrupts:
    maxItems: 1

  clocks:
    minItems: 1
    items:
      - description: I2C controller reference clock source
      - description: APB interface clock source

  clock-names:
    minItems: 1
    items:
      - const: ref
      - const: pclk

  resets:
    maxItems: 1

  clock-frequency:
    description: Desired I2C bus clock frequency in Hz
    enum: [100000, 400000, 1000000, 3400000]
    default: 400000

  i2c-sda-hold-time-ns:
    description: |
      The property should contain the SDA hold time in nanoseconds. This option
      is only supported in hardware blocks version 1.11a or newer or on
      Microsemi SoCs.

  i2c-scl-falling-time-ns:
    description: |
      The property should contain the SCL falling time in nanoseconds.
      This value is used to compute the tLOW period.
    default: 300

  i2c-sda-falling-time-ns:
    description: |
      The property should contain the SDA falling time in nanoseconds.
      This value is used to compute the tHIGH period.
    default: 300

  dmas:
    items:
      - description: TX DMA Channel
      - description: RX DMA Channel

  dma-names:
    items:
      - const: tx
      - const: rx

unevaluatedProperties: false

required:
  - compatible
  - reg
  - interrupts

examples:
  - |
    i2c@f0000 {
      compatible = "snps,designware-i2c";
      reg = <0xf0000 0x1000>;
      interrupts = <11>;
      clock-frequency = <400000>;
    };
  - |
    i2c@1120000 {
      compatible = "snps,designware-i2c";
      reg = <0x1120000 0x1000>;
      interrupts = <12 1>;
      clock-frequency = <400000>;
      i2c-sda-hold-time-ns = <300>;
      i2c-sda-falling-time-ns = <300>;
      i2c-scl-falling-time-ns = <300>;
    };
  - |
    i2c@2000 {
      compatible = "snps,designware-i2c";
      reg = <0x2000 0x100>;
      #address-cells = <1>;
      #size-cells = <0>;
      clock-frequency = <400000>;
      clocks = <&i2cclk>;
      interrupts = <0>;

      eeprom@64 {
        compatible = "atmel,24c02";
        reg = <0x64>;
      };
    };
  - |
    i2c@100400 {
      compatible = "mscc,ocelot-i2c", "snps,designware-i2c";
      reg = <0x100400 0x100>, <0x198 0x8>;
      pinctrl-0 = <&i2c_pins>;
      pinctrl-names = "default";
      interrupts = <8>;
      clocks = <&ahb_clk>;
    };
...
