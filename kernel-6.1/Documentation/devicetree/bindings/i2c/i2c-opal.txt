Device-tree bindings for I2C OPAL driver
----------------------------------------

Most of the device node and properties layout is specific to the firmware and
used by the firmware itself for configuring the port. From the linux
perspective, the properties of use are "ibm,port-name" and "ibm,opal-id".

Required properties:

- reg: Port-id within a given master
- compatible: must be "ibm,opal-i2c"
- ibm,opal-id: Refers to a specific bus and used to identify it when calling
	       the relevant OPAL functions.
- bus-frequency: Operating frequency of the i2c bus (in HZ). Informational for
		 linux, used by the FW though.

Optional properties:
- ibm,port-name: Firmware provides this name that uniquely identifies the i2c
		 port.

The node contains a number of other properties that are used by the FW itself
and depend on the specific hardware implementation. The example below depicts
a P8 on-chip bus.

Example:

i2c-bus@0 {
	reg = <0x0>;
	bus-frequency = <0x61a80>;
	compatible = "ibm,power8-i2c-port", "ibm,opal-i2c";
	ibm,opal-id = <0x1>;
	ibm,port-name = "p8_00000000_e1p0";
	#address-cells = <0x1>;
	phandle = <0x10000006>;
	#size-cells = <0x0>;
	linux,phandle = <0x10000006>;
};
