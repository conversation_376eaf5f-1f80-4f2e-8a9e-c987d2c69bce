# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/i2c/renesas,rmobile-iic.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Renesas R-Mobile I2C Bus Interface (IIC)

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    oneOf:
      - items:
          - enum:
              - renesas,iic-r8a73a4      # R-Mobile APE6
              - renesas,iic-r8a7740      # R-Mobile A1
              - renesas,iic-sh73a0       # SH-Mobile AG5
          - const: renesas,rmobile-iic   # Generic

      - items:
          - enum:
              - renesas,iic-r8a7742      # RZ/G1H
              - renesas,iic-r8a7743      # RZ/G1M
              - renesas,iic-r8a7744      # RZ/G1N
              - renesas,iic-r8a7745      # RZ/G1E
              - renesas,iic-r8a7790      # R-Car H2
              - renesas,iic-r8a7791      # R-Car M2-W
              - renesas,iic-r8a7792      # R-Car V2H
              - renesas,iic-r8a7793      # R-Car M2-N
              - renesas,iic-r8a7794      # R-Car E2
          - const: renesas,rcar-gen2-iic # R-Car Gen2 and RZ/G1
          - const: renesas,rmobile-iic   # Generic

      - items:
          - enum:
              - renesas,iic-r8a774a1     # RZ/G2M
              - renesas,iic-r8a774b1     # RZ/G2N
              - renesas,iic-r8a774c0     # RZ/G2E
              - renesas,iic-r8a774e1     # RZ/G2H
              - renesas,iic-r8a7795      # R-Car H3
              - renesas,iic-r8a7796      # R-Car M3-W
              - renesas,iic-r8a77961     # R-Car M3-W+
              - renesas,iic-r8a77965     # R-Car M3-N
              - renesas,iic-r8a77990     # R-Car E3
          - const: renesas,rcar-gen3-iic # R-Car Gen3 and RZ/G2
          - const: renesas,rmobile-iic   # Generic

  reg:
    maxItems: 1

  interrupts: true

  clock-frequency:
    description:
      Desired I2C bus clock frequency in Hz. The absence of this property
      indicates the default frequency 100 kHz.

  clocks:
    maxItems: 1

  power-domains:
    maxItems: 1

  resets:
    maxItems: 1

  dmas:
    minItems: 2
    maxItems: 4
    description:
      Must contain a list of pairs of references to DMA specifiers, one for
      transmission, and one for reception.

  dma-names:
    minItems: 2
    maxItems: 4
    items:
      enum:
        - tx
        - rx

required:
  - compatible
  - reg
  - interrupts
  - clocks
  - power-domains
  - '#address-cells'
  - '#size-cells'

allOf:
  - $ref: /schemas/i2c/i2c-controller.yaml#

  - if:
      properties:
        compatible:
          contains:
            enum:
              - renesas,iic-r8a7740
              - renesas,iic-sh73a0
    then:
      properties:
        interrupts:
          items:
            - description: Arbitration Lost Interrupt (ALI)
            - description: Non-acknowledge Detection Interrupt (TACKI)
            - description: Wait Interrupt (WAITI)
            - description: Data Transmit Enable interrupt (DTEI)
    else:
      properties:
        interrupts:
          items:
            - description: Single combined interrupt

  - if:
      properties:
        compatible:
          contains:
            enum:
              - renesas,rcar-gen2-iic
              - renesas,rcar-gen3-iic
    then:
      required:
        - resets

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/r8a7790-cpg-mssr.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/power/r8a7790-sysc.h>

    iic0: i2c@e6500000 {
            compatible = "renesas,iic-r8a7790", "renesas,rcar-gen2-iic",
                         "renesas,rmobile-iic";
            reg = <0xe6500000 0x425>;
            interrupts = <GIC_SPI 174 IRQ_TYPE_LEVEL_HIGH>;
            clocks = <&cpg CPG_MOD 318>;
            clock-frequency = <400000>;
            dmas = <&dmac0 0x61>, <&dmac0 0x62>, <&dmac1 0x61>, <&dmac1 0x62>;
            dma-names = "tx", "rx", "tx", "rx";
            power-domains = <&sysc R8A7790_PD_ALWAYS_ON>;
            resets = <&cpg 318>;
            #address-cells = <1>;
            #size-cells = <0>;
    };
