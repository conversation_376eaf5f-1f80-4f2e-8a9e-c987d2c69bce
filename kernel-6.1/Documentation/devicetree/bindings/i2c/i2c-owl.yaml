# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/i2c/i2c-owl.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Actions Semi Owl I2C Controller

maintainers:
  - <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>

description: |
  This I2C controller is found in the Actions Semi Owl SoCs:
  S500, S700 and S900.

allOf:
  - $ref: /schemas/i2c/i2c-controller.yaml#

properties:
  compatible:
    enum:
      - actions,s500-i2c # Actions Semi S500 compatible SoCs
      - actions,s700-i2c # Actions Semi S700 compatible SoCs
      - actions,s900-i2c # Actions Semi S900 compatible SoCs

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks:
    description: Phandle of the clock feeding the I2C controller.
    minItems: 1

  clock-frequency:
    description: |
      Desired I2C bus clock frequency in Hz. As only Standard and Fast
      modes are supported, possible values are 100000 and 400000.
    enum: [100000, 400000]

required:
  - compatible
  - reg
  - interrupts
  - clocks

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/actions,s900-cmu.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    i2c@e0170000 {
        compatible = "actions,s900-i2c";
        reg = <0xe0170000 0x1000>;
        interrupts = <GIC_SPI 25 IRQ_TYPE_LEVEL_HIGH>;
        clocks = <&cmu CLK_I2C0>;
        clock-frequency = <100000>;
    };

...
