# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/i2c/i2c-mxs.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Freescale MXS Inter IC (I2C) Controller

maintainers:
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - fsl,imx23-i2c
      - fsl,imx28-i2c

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clock-frequency:
    enum: [ 100000, 400000 ]

  dmas:
    maxItems: 1

  dma-names:
    const: rx-tx

required:
  - compatible
  - reg
  - interrupts
  - dmas
  - dma-names

additionalProperties: false

examples:
  - |
    i2c@80058000 {
        compatible = "fsl,imx28-i2c";
        reg = <0x80058000 2000>;
        interrupts = <111>;
        clock-frequency = <100000>;
        dmas = <&dma_apbx 6>;
        dma-names = "rx-tx";
    };
