# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/i2c/renesas,iic-emev2.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Renesas EMMA Mobile EV2 IIC Interface

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

allOf:
  - $ref: /schemas/i2c/i2c-controller.yaml#

properties:
  compatible:
    const: renesas,iic-emev2

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks:
    maxItems: 1

  clock-names:
    const: sclk

required:
  - compatible
  - reg
  - interrupts
  - clocks
  - clock-names
  - '#address-cells'
  - '#size-cells'

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>

    iic0: i2c@e0070000 {
            #address-cells = <1>;
            #size-cells = <0>;
            compatible = "renesas,iic-emev2";
            reg = <0xe0070000 0x28>;
            interrupts = <GIC_SPI 32 IRQ_TYPE_EDGE_RISING>;
            clocks = <&iic0_sclk>;
            clock-names = "sclk";
    };
