# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/i2c/socionext,uniphier-i2c.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: UniPhier I2C controller (FIFO-less)

maintainers:
  - <PERSON><PERSON><PERSON> <yamada.ma<PERSON><EMAIL>>

allOf:
  - $ref: /schemas/i2c/i2c-controller.yaml#

properties:
  compatible:
    const: socionext,uniphier-i2c

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks:
    maxItems: 1

  clock-frequency:
    minimum: 100000
    maximum: 400000

required:
  - compatible
  - reg
  - "#address-cells"
  - "#size-cells"
  - interrupts
  - clocks

unevaluatedProperties: false

examples:
  - |
    i2c0: i2c@58400000 {
        compatible = "socionext,uniphier-i2c";
        reg = <0x58400000 0x40>;
        #address-cells = <1>;
        #size-cells = <0>;
        interrupts = <0 41 1>;
        clocks = <&i2c_clk>;
        clock-frequency = <100000>;
    };
