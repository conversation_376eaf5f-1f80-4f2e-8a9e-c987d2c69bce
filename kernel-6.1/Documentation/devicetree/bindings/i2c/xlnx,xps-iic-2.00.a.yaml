# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/i2c/xlnx,xps-iic-2.00.a.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Xilinx IIC controller

maintainers:
  - <EMAIL>

allOf:
  - $ref: /schemas/i2c/i2c-controller.yaml#

properties:
  compatible:
    const: xlnx,xps-iic-2.00.a

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks:
    minItems: 1

  clock-name:
    const: pclk
    description: |
      Input clock name.

required:
  - compatible
  - reg
  - interrupts
  - clocks

unevaluatedProperties: false

examples:
  - |
    axi_iic_0: i2c@40800000 {
      compatible = "xlnx,xps-iic-2.00.a";
      clocks = <&clkc 15>;
      interrupts = < 1 2 >;
      reg = < 0x40800000 0x10000 >;

      #size-cells = <0>;
      #address-cells = <1>;
    };
