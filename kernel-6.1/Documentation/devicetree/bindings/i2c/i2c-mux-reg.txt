Register-based I2C Bus Mux

This binding describes an I2C bus multiplexer that uses a single register
to route the I2C signals.

Required properties:
- compatible: i2c-mux-reg
- i2c-parent: The phandle of the I2C bus that this multiplexer's master-side
  port is connected to.
* Standard I2C mux properties. See i2c-mux.yaml in this directory.
* I2C child bus nodes. See i2c-mux.yaml in this directory.

Optional properties:
- reg: this pair of <offset size> specifies the register to control the mux.
  The <offset size> depends on its parent node. It can be any memory-mapped
  address. The size must be either 1, 2, or 4 bytes. If reg is omitted, the
  resource of this device will be used.
- little-endian: The existence indicates the register is in little endian.
- big-endian: The existence indicates the register is in big endian.
  If both little-endian and big-endian are omitted, the endianness of the
  CPU will be used.
- write-only: The existence indicates the register is write-only.
- idle-state: value to set the muxer to when idle. When no value is
  given, it defaults to the last value used.

Whenever an access is made to a device on a child bus, the value set
in the relevant node's reg property will be output to the register.

If an idle state is defined, using the idle-state (optional) property,
whenever an access is not being made to a device on a child bus, the
register will be set according to the idle value.

If an idle state is not defined, the most recently used value will be
left programmed into the register.

Example of a mux on PCIe card, the host is a powerpc SoC (big endian):

	i2c-mux {
		/* the <offset size> depends on the address translation
		 * of the parent device. If omitted, device resource
		 * will be used instead. The size is to determine
		 * whether iowrite32, iowrite16, or iowrite8 will be used.
		 */
		reg = <0x6028 0x4>;
		little-endian;		/* little endian register on PCIe */
		compatible = "i2c-mux-reg";
		#address-cells = <1>;
		#size-cells = <0>;
		i2c-parent = <&i2c1>;
		i2c@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;

			si5338: clock-generator@70 {
				compatible = "silabs,si5338";
				reg = <0x70>;
				/* other stuff */
			};
		};

		i2c@1 {
			/* data is written using iowrite32 */
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;

			si5338: clock-generator@70 {
				compatible = "silabs,si5338";
				reg = <0x70>;
				/* other stuff */
			};
		};
	};
