# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/i2c/samsung,s3c2410-i2c.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Samsung S3C/S5P/Exynos SoC I2C Controller

maintainers:
  - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - samsung,s3c2410-i2c
      - samsung,s3c2440-i2c
        # For s3c2440-like I2C used inside HDMIPHY block found on several SoCs:
      - samsung,s3c2440-hdmiphy-i2c
        # For s3c2440-like I2C used as a host to SATA PHY controller on an
        # internal bus:
      - samsung,exynos5-sata-phy-i2c

  '#address-cells':
    const: 1

  clocks:
    maxItems: 1

  clock-names:
    items:
      - const: i2c

  gpios:
    description: |
      The order of the GPIOs should be the following:: <SDA, SCL>.  The GPIO
      specifier depends on the gpio controller. Required in all cases except
      for "samsung,s3c2440-hdmiphy-i2c" whose input/output lines are
      permanently wired to the respective client.
      This property is deprecated. Use "pinctrl-0" and "pinctrl-names" instead.
    deprecated: yes

  interrupts:
    maxItems: 1

  reg:
    maxItems: 1

  samsung,i2c-max-bus-freq:
    $ref: /schemas/types.yaml#/definitions/uint32
    description:
      Desired frequency in Hz of the bus.
    default: 100000

  samsung,i2c-sda-delay:
    $ref: /schemas/types.yaml#/definitions/uint32
    description:
      Delay (in ns) applied to data line (SDA) edges.
    default: 0

  samsung,i2c-slave-addr:
    $ref: /schemas/types.yaml#/definitions/uint32
    description:
      Slave address in multi-master environment.
    default: 0

  samsung,sysreg-phandle:
    $ref: /schemas/types.yaml#/definitions/phandle
    description: Pandle to syscon used to control the system registers.

  '#size-cells':
    const: 0

required:
  - compatible
  - reg

allOf:
  - $ref: /schemas/i2c/i2c-controller.yaml#
  - if:
      properties:
        compatible:
          contains:
            enum:
              - samsung,s3c2440-hdmiphy-i2c
              - samsung,exynos5-sata-phy-i2c
    then:
      properties:
        gpios: false

  - if:
      properties:
        compatible:
          contains:
            enum:
              - samsung,s3c2410-i2c
              - samsung,s3c2440-i2c
              - samsung,s3c2440-hdmiphy-i2c
    then:
      required:
        - interrupts

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/exynos5250.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>

    i2c@12c60000 {
        compatible = "samsung,s3c2440-i2c";
        reg = <0x12C60000 0x100>;
        interrupts = <GIC_SPI 56 IRQ_TYPE_LEVEL_HIGH>;
        #address-cells = <1>;
        #size-cells = <0>;
        clocks = <&clock CLK_I2C0>;
        clock-names = "i2c";
        pinctrl-names = "default";
        pinctrl-0 = <&i2c0_bus>;

        samsung,sysreg-phandle = <&sysreg_system_controller>;
        samsung,i2c-sda-delay = <100>;
        samsung,i2c-max-bus-freq = <20000>;
        samsung,i2c-slave-addr = <0x66>;

        eeprom@50 {
            compatible = "samsung,s524ad0xd1", "atmel,24c128";
            reg = <0x50>;
        };
    };

    i2c@12ce0000 {
        compatible = "samsung,s3c2440-hdmiphy-i2c";
        reg = <0x12CE0000 0x1000>;
        interrupts = <GIC_SPI 64 IRQ_TYPE_LEVEL_HIGH>;
        #address-cells = <1>;
        #size-cells = <0>;
        clocks = <&clock CLK_I2C_HDMI>;
        clock-names = "i2c";

        samsung,i2c-sda-delay = <100>;
        samsung,i2c-max-bus-freq = <66000>;

        phy-i2c@38 {
            compatible = "samsung,exynos4212-hdmiphy";
            reg = <0x38>;
        };
    };

    i2c@121d0000 {
        compatible = "samsung,exynos5-sata-phy-i2c";
        reg = <0x121D0000 0x100>;
        #address-cells = <1>;
        #size-cells = <0>;
        clocks = <&clock CLK_SATA_PHYI2C>;
        clock-names = "i2c";

        samsung,i2c-sda-delay = <100>;
        samsung,i2c-max-bus-freq = <40000>;

        phy-i2c@38 {
            compatible = "samsung,exynos-sataphy-i2c";
            reg = <0x38>;
        };
    };
