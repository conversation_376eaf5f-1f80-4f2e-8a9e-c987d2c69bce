# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/i2c/st,stm32-i2c.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: I2C controller embedded in STMicroelectronics STM32 I2C platform

maintainers:
  - <PERSON><PERSON>OR<PERSON> <<EMAIL>>

allOf:
  - $ref: /schemas/i2c/i2c-controller.yaml#
  - if:
      properties:
        compatible:
          contains:
            enum:
              - st,stm32f7-i2c
              - st,stm32mp13-i2c
              - st,stm32mp15-i2c
    then:
      properties:
        i2c-scl-rising-time-ns:
          default: 25

        i2c-scl-falling-time-ns:
          default: 10
    else:
      properties:
        st,syscfg-fmp: false

  - if:
      properties:
        compatible:
          contains:
            enum:
              - st,stm32f4-i2c
    then:
      properties:
        clock-frequency:
          enum: [100000, 400000]

properties:
  compatible:
    enum:
      - st,stm32f4-i2c
      - st,stm32f7-i2c
      - st,stm32mp13-i2c
      - st,stm32mp15-i2c

  reg:
    maxItems: 1

  interrupts:
    items:
      - description: interrupt ID for I2C event
      - description: interrupt ID for I2C error

  interrupt-names:
    items:
      - const: event
      - const: error

  resets:
    maxItems: 1

  clocks:
    maxItems: 1

  dmas:
    items:
      - description: RX DMA Channel phandle
      - description: TX DMA Channel phandle

  dma-names:
    items:
      - const: rx
      - const: tx

  clock-frequency:
    description: Desired I2C bus clock frequency in Hz. If not specified,
                 the default 100 kHz frequency will be used.
                 For STM32F7, STM32H7 and STM32MP1 SoCs, if timing parameters
                 match, the bus clock frequency can be from 1Hz to 1MHz.
    default: 100000
    minimum: 1
    maximum: 1000000

  st,syscfg-fmp:
    description: Use to set Fast Mode Plus bit within SYSCFG when Fast Mode
      Plus speed is selected by slave.
    $ref: /schemas/types.yaml#/definitions/phandle-array
    items:
      - items:
          - description: phandle to syscfg
          - description: register offset within syscfg
          - description: register bitmask for FMP bit

  wakeup-source: true

required:
  - compatible
  - reg
  - interrupts
  - resets
  - clocks

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/mfd/stm32f7-rcc.h>
    #include <dt-bindings/clock/stm32fx-clock.h>
    //Example 1 (with st,stm32f4-i2c compatible)
      i2c@40005400 {
          compatible = "st,stm32f4-i2c";
          #address-cells = <1>;
          #size-cells = <0>;
          reg = <0x40005400 0x400>;
          interrupts = <31>,
                       <32>;
          resets = <&rcc 277>;
          clocks = <&rcc 0 149>;
      };

  - |
    #include <dt-bindings/mfd/stm32f7-rcc.h>
    #include <dt-bindings/clock/stm32fx-clock.h>
    //Example 2 (with st,stm32f7-i2c compatible)
      i2c@40005800 {
          compatible = "st,stm32f7-i2c";
          #address-cells = <1>;
          #size-cells = <0>;
          reg = <0x40005800 0x400>;
          interrupts = <31>,
                       <32>;
          resets = <&rcc STM32F7_APB1_RESET(I2C1)>;
          clocks = <&rcc 1 CLK_I2C1>;
      };

  - |
    #include <dt-bindings/mfd/stm32f7-rcc.h>
    #include <dt-bindings/clock/stm32fx-clock.h>
    //Example 3 (with st,stm32mp15-i2c compatible on stm32mp)
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/clock/stm32mp1-clks.h>
    #include <dt-bindings/reset/stm32mp1-resets.h>
      i2c@40013000 {
          compatible = "st,stm32mp15-i2c";
          #address-cells = <1>;
          #size-cells = <0>;
          reg = <0x40013000 0x400>;
          interrupts = <GIC_SPI 33 IRQ_TYPE_LEVEL_HIGH>,
                       <GIC_SPI 34 IRQ_TYPE_LEVEL_HIGH>;
          clocks = <&rcc I2C2_K>;
          resets = <&rcc I2C2_R>;
          i2c-scl-rising-time-ns = <185>;
          i2c-scl-falling-time-ns = <20>;
          st,syscfg-fmp = <&syscfg 0x4 0x2>;
      };
