# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/i2c/nvidia,tegra186-bpmp-i2c.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: NVIDIA Tegra186 (and later) BPMP I2C controller

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

description: |
  In Tegra186 and later, the BPMP (Boot and Power Management Processor)
  owns certain HW devices, such as the I2C controller for the power
  management I2C bus. Software running on other CPUs must perform IPC to
  the BPMP in order to execute transactions on that I2C bus. This
  binding describes an I2C bus that is accessed in such a fashion.

  The BPMP I2C node must be located directly inside the main BPMP node.
  See ../firmware/nvidia,tegra186-bpmp.yaml for details of the BPMP
  binding.

  This node represents an I2C controller. See ../i2c/i2c.txt for details
  of the core I2C binding.

properties:
  compatible:
    const: nvidia,tegra186-bpmp-i2c

  nvidia,bpmp-bus-id:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: Indicates the I2C bus number this DT node represents,
      as defined by the BPMP firmware.

allOf:
  - $ref: /schemas/i2c/i2c-controller.yaml

unevaluatedProperties: false

required:
  - compatible
  - "#address-cells"
  - "#size-cells"
  - nvidia,bpmp-bus-id
