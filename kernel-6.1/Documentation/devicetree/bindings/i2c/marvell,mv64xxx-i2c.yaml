# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/i2c/marvell,mv64xxx-i2c.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Marvell MV64XXX I2C Controller

maintainers:
  - <PERSON>LEMENT <<EMAIL>>

properties:
  compatible:
    oneOf:
      - const: allwinner,sun4i-a10-i2c
      - items:
          - const: allwinner,sun7i-a20-i2c
          - const: allwinner,sun4i-a10-i2c
      - const: allwinner,sun6i-a31-i2c
      - items:
          - enum:
              - allwinner,sun8i-a23-i2c
              - allwinner,sun8i-a83t-i2c
              - allwinner,sun8i-v536-i2c
              - allwinner,sun50i-a64-i2c
              - allwinner,sun50i-h6-i2c
          - const: allwinner,sun6i-a31-i2c
      - description: Allwinner SoCs with offload support
        items:
          - enum:
              - allwinner,sun20i-d1-i2c
              - allwinner,sun50i-a100-i2c
              - allwinner,sun50i-h616-i2c
              - allwinner,sun50i-r329-i2c
          - const: allwinner,sun8i-v536-i2c
          - const: allwinner,sun6i-a31-i2c
      - const: marvell,mv64xxx-i2c
      - const: marvell,mv78230-i2c
      - const: marvell,mv78230-a0-i2c

    description:
      Only use "marvell,mv78230-a0-i2c" for a very rare, initial
      version of the SoC which had broken offload support. Linux
      auto-detects this and sets it appropriately.

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks:
    minItems: 1
    items:
      - description: Reference clock for the I2C bus
      - description: Bus clock (Only for Armada 7K/8K)

  clock-names:
    minItems: 1
    items:
      - const: core
      - const: reg
    description:
      Mandatory if two clocks are used (only for Armada 7k and 8k).

  resets:
    maxItems: 1

  dmas:
    items:
      - description: RX DMA Channel
      - description: TX DMA Channel

  dma-names:
    items:
      - const: rx
      - const: tx

dependencies:
  dmas: [ dma-names ]

required:
  - compatible
  - reg
  - interrupts

allOf:
  - $ref: /schemas/i2c/i2c-controller.yaml#
  - if:
      properties:
        compatible:
          contains:
            enum:
              - allwinner,sun4i-a10-i2c
              - allwinner,sun6i-a31-i2c

    then:
      required:
        - clocks

  - if:
      properties:
        compatible:
          contains:
            const: allwinner,sun6i-a31-i2c

    then:
      required:
        - resets

unevaluatedProperties: false

examples:
  - |
    i2c@11000 {
        compatible = "marvell,mv64xxx-i2c";
        reg = <0x11000 0x20>;
        interrupts = <29>;
        clock-frequency = <100000>;
    };

  - |
    i2c@11000 {
        compatible = "marvell,mv78230-i2c";
        reg = <0x11000 0x100>;
        interrupts = <29>;
        clock-frequency = <100000>;
    };

  - |
    i2c@701000 {
        compatible = "marvell,mv78230-i2c";
        reg = <0x701000 0x20>;
        interrupts = <29>;
        clock-frequency = <100000>;
        clock-names = "core", "reg";
        clocks = <&core_clock>, <&reg_clock>;
    };

...
