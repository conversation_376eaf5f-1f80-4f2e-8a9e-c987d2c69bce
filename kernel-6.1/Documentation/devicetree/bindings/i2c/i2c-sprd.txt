I2C for Spreadtrum platforms

Required properties:
- compatible: Should be "sprd,sc9860-i2c".
- reg: Specify the physical base address of the controller and length
  of memory mapped region.
- interrupts: Should contain I2C interrupt.
- clock-names: Should contain following entries:
  "i2c" for I2C clock,
  "source" for I2C source (parent) clock,
  "enable" for I2C module enable clock.
- clocks: Should contain a clock specifier for each entry in clock-names.
- clock-frequency: Constains desired I2C bus clock frequency in Hz.
- #address-cells: Should be 1 to describe address cells for I2C device address.
- #size-cells: Should be 0 means no size cell for I2C device address.

Optional properties:
- Child nodes conforming to I2C bus binding

Examples:
i2c0: i2c@70500000 {
	compatible = "sprd,sc9860-i2c";
	reg = <0 0x70500000 0 0x1000>;
	interrupts = <GIC_SPI 11 IRQ_TYPE_LEVEL_HIGH>;
	clock-names = "i2c", "source", "enable";
	clocks = <&clk_i2c3>, <&ext_26m>, <&clk_ap_apb_gates 11>;
	clock-frequency = <400000>;
	#address-cells = <1>;
	#size-cells = <0>;
};

