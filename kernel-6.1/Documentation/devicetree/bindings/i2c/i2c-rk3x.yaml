# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/i2c/i2c-rk3x.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Rockchip RK3xxx I2C controller

description:
  This driver interfaces with the native I2C controller present in Rockchip
  RK3xxx SoCs.

allOf:
  - $ref: /schemas/i2c/i2c-controller.yaml#

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

# Everything else is described in the common file
properties:
  compatible:
    oneOf:
      - const: rockchip,rv1108-i2c
      - const: rockchip,rk3066-i2c
      - const: rockchip,rk3188-i2c
      - const: rockchip,rk3228-i2c
      - const: rockchip,rk3288-i2c
      - const: rockchip,rk3399-i2c
      - items:
          - enum:
              - rockchip,rk3036-i2c
              - rockchip,rk3128-i2c
              - rockchip,rk3368-i2c
          - const: rockchip,rk3288-i2c
      - items:
          - enum:
              - rockchip,px30-i2c
              - rockchip,rk3308-i2c
              - rockchip,rk3328-i2c
              - rockchip,rk3568-i2c
              - rockchip,rk3588-i2c
              - rockchip,rv1126-i2c
          - const: rockchip,rk3399-i2c

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks:
    minItems: 1
    items:
      - description:
          For older hardware (rk3066, rk3188, rk3228, rk3288)
          there is one clock that is used both to derive the functional clock
          for the device and as the bus clock.
          For newer hardware (rk3399) this clock is used to derive
          the functional clock
      - description:
          For newer hardware (rk3399) this is the bus clock

  clock-names:
    minItems: 1
    items:
      - const: i2c
      - const: pclk

  rockchip,grf:
    $ref: /schemas/types.yaml#/definitions/phandle
    description:
      Required on RK3066, RK3188 the phandle of the syscon node for
      the general register file (GRF)
      On those SoCs an alias with the correct I2C bus ID
      (bit offset in the GRF) is also required.

  clock-frequency:
    default: 100000
    description:
      SCL frequency to use (in Hz). If omitted, 100kHz is used.

  i2c-scl-rising-time-ns:
    default: 1000
    description:
      Number of nanoseconds the SCL signal takes to rise
      (t(r) in I2C specification). If not specified this is assumed to be
      the maximum the specification allows(1000 ns for Standard-mode,
      300 ns for Fast-mode) which might cause slightly slower communication.

  i2c-scl-falling-time-ns:
    default: 300
    description:
      Number of nanoseconds the SCL signal takes to fall
      (t(f) in the I2C specification). If not specified this is assumed to
      be the maximum the specification allows (300 ns) which might cause
      slightly slower communication.

  i2c-sda-falling-time-ns:
    default: 300
    description:
      Number of nanoseconds the SDA signal takes to fall
      (t(f) in the I2C specification). If not specified we will use the SCL
      value since they are the same in nearly all cases.

required:
  - compatible
  - reg
  - interrupts
  - clocks
  - clock-names

if:
  properties:
    compatible:
      contains:
        enum:
          - rockchip,rk3066-i2c
          - rockchip,rk3188-i2c

then:
  required:
    - rockchip,grf

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/rk3188-cru-common.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/interrupt-controller/irq.h>
    i2c0: i2c@2002d000 {
      compatible = "rockchip,rk3188-i2c";
      reg = <0x2002d000 0x1000>;
      interrupts = <GIC_SPI 40 IRQ_TYPE_LEVEL_HIGH>;
      clocks = <&cru PCLK_I2C0>;
      clock-names = "i2c";
      rockchip,grf = <&grf>;
      i2c-scl-falling-time-ns = <100>;
      i2c-scl-rising-time-ns = <800>;
      #address-cells = <1>;
      #size-cells = <0>;
    };
