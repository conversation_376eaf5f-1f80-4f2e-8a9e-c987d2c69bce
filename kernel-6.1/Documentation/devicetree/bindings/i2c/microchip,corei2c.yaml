# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/i2c/microchip,corei2c.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Microchip MPFS I2C Controller

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

allOf:
  - $ref: /schemas/i2c/i2c-controller.yaml#

properties:
  compatible:
    oneOf:
      - items:
          - const: microchip,mpfs-i2c # Microchip PolarFire SoC compatible SoCs
          - const: microchip,corei2c-rtl-v7 # Microchip Fabric based i2c IP core
      - const: microchip,corei2c-rtl-v7 # Microchip Fabric based i2c IP core

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks:
    maxItems: 1

  clock-frequency:
    description: |
      Desired I2C bus clock frequency in Hz. As only Standard and Fast
      modes are supported, possible values are 100000 and 400000.
    enum: [100000, 400000]

required:
  - compatible
  - reg
  - interrupts
  - clocks

unevaluatedProperties: false

examples:
  - |
    i2c@2010a000 {
      compatible = "microchip,mpfs-i2c", "microchip,corei2c-rtl-v7";
      reg = <0x2010a000 0x1000>;
      clocks = <&clkcfg 15>;
      interrupt-parent = <&plic>;
      interrupts = <58>;
      clock-frequency = <100000>;
    };
...
