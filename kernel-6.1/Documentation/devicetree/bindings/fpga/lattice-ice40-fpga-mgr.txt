Lattice iCE40 FPGA Manager

Required properties:
- compatible:		Should contain "lattice,ice40-fpga-mgr"
- reg:			SPI chip select
- spi-max-frequency:	Maximum SPI frequency (>=1000000, <=25000000)
- cdone-gpios:		GPIO input connected to CDONE pin
- reset-gpios:		Active-low GPIO output connected to CRESET_B pin. Note
			that unless the GPIO is held low during startup, the
			FPGA will enter Master SPI mode and drive SCK with a
			clock signal potentially jamming other devices on the
			bus until the firmware is loaded.

Example:
	fpga: fpga@0 {
		compatible = "lattice,ice40-fpga-mgr";
		reg = <0>;
		spi-max-frequency = <1000000>;
		cdone-gpios = <&gpio 24 GPIO_ACTIVE_HIGH>;
		reset-gpios = <&gpio 22 GPIO_ACTIVE_LOW>;
	};
