# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/fpga/xilinx-zynq-fpga-mgr.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Xilinx Zynq FPGA Manager

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    const: xlnx,zynq-devcfg-1.0

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks:
    maxItems: 1

  clock-names:
    items:
      - const: ref_clk

  syscon:
    $ref: /schemas/types.yaml#/definitions/phandle
    description:
      Phandle to syscon block which provide access to SLCR registers

required:
  - compatible
  - reg
  - clocks
  - clock-names
  - syscon

additionalProperties: false

examples:
  - |
    devcfg: devcfg@f8007000 {
      compatible = "xlnx,zynq-devcfg-1.0";
      reg = <0xf8007000 0x100>;
      interrupts = <0 8 4>;
      clocks = <&clkc 12>;
      clock-names = "ref_clk";
      syscon = <&slcr>;
    };
