Lattice MachXO2 Slave SPI FPGA Manager

<PERSON><PERSON><PERSON> MachXO2 FPGAs support a method of loading the bitstream over
'slave SPI' interface.

See 'MachXO2ProgrammingandConfigurationUsageGuide.pdf' on www.latticesemi.com

Required properties:
- compatible: should contain "lattice,machxo2-slave-spi"
- reg: spi chip select of the FPGA

Example for full FPGA configuration:

	fpga-region0 {
		compatible = "fpga-region";
		fpga-mgr = <&fpga_mgr_spi>;
		#address-cells = <0x1>;
		#size-cells = <0x1>;
	};

	spi1: spi@2000 {
        ...

		fpga_mgr_spi: fpga-mgr@0 {
			compatible = "lattice,machxo2-slave-spi";
			spi-max-frequency = <8000000>;
			reg = <0>;
		};
	};
