Altera SOCFPGA FPGA Manager

Required properties:
- compatible : should contain "altr,socfpga-fpga-mgr"
- reg        : base address and size for memory mapped io.
               - The first index is for FPGA manager register access.
               - The second index is for writing FPGA configuration data.
- interrupts : interrupt for the FPGA Manager device.

Example:

	hps_0_fpgamgr: fpgamgr@ff706000 {
		compatible = "altr,socfpga-fpga-mgr";
		reg = <0xFF706000 0x1000
		       0xFFB90000 0x1000>;
		interrupts = <0 175 4>;
	};
