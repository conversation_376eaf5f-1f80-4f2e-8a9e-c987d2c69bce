# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/fpga/xlnx,zynqmp-pcap-fpga.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Xilinx Zynq Ultrascale MPSoC FPGA Manager

maintainers:
  - <PERSON><PERSON> k<PERSON><PERSON> <<EMAIL>>

description: |
  Device Tree Bindings for Zynq Ultrascale MPSoC FPGA Manager.
  The ZynqMP SoC uses the PCAP (Processor Configuration Port) to
  configure the Programmable Logic (PL). The configuration uses the
  firmware interface.

properties:
  compatible:
    const: xlnx,zynqmp-pcap-fpga

required:
  - compatible

additionalProperties: false

examples:
  - |
    firmware {
      zynqmp_firmware: zynqmp-firmware {
        zynqmp_pcap: pcap {
          compatible = "xlnx,zynqmp-pcap-fpga";
        };
      };
    };
...
