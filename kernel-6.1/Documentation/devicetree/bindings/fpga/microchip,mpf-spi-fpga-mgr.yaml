# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/fpga/microchip,mpf-spi-fpga-mgr.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Microchip Polarfire FPGA manager.

maintainers:
  - <PERSON> <i.born<PERSON><PERSON>@metrotek.ru>

description:
  Device Tree Bindings for Microchip Polarfire FPGA Manager using slave SPI to
  load the bitstream in .dat format.

properties:
  compatible:
    enum:
      - microchip,mpf-spi-fpga-mgr

  reg:
    description: SPI chip select
    maxItems: 1

required:
  - compatible
  - reg

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

unevaluatedProperties: false

examples:
  - |
    spi {
            #address-cells = <1>;
            #size-cells = <0>;

            fpga_mgr@0 {
                    compatible = "microchip,mpf-spi-fpga-mgr";
                    spi-max-frequency = <20000000>;
                    reg = <0>;
            };
    };
