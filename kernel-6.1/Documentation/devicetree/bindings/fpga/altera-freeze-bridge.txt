Altera Freeze Bridge Controller Driver

The Altera Freeze Bridge Controller manages one or more freeze bridges.
The controller can freeze/disable the bridges which prevents signal
changes from passing through the bridge.  The controller can also
unfreeze/enable the bridges which allows traffic to pass through the
bridge normally.

Required properties:
- compatible		: Should contain "altr,freeze-bridge-controller"
- regs			: base address and size for freeze bridge module

See Documentation/devicetree/bindings/fpga/fpga-bridge.txt for generic bindings.

Example:
	freeze-controller@100000450 {
		compatible = "altr,freeze-bridge-controller";
		regs = <0x1000 0x10>;
		bridge-enable = <0>;
	};
