Xilinx Slave Serial SPI FPGA Manager

Xilinx Spartan-6 and 7 Series FPGAs support a method of loading the
bitstream over what is referred to as "slave serial" interface.
The slave serial link is not technically SPI, and might require extra
circuits in order to play nicely with other SPI slaves on the same bus.

See:
- https://www.xilinx.com/support/documentation/user_guides/ug380.pdf
- https://www.xilinx.com/support/documentation/user_guides/ug470_7Series_Config.pdf
- https://www.xilinx.com/support/documentation/application_notes/xapp583-fpga-configuration.pdf

Required properties:
- compatible: should contain "xlnx,fpga-slave-serial"
- reg: spi chip select of the FPGA
- prog_b-gpios: config pin (referred to as PROGRAM_B in the manual)
- done-gpios: config status pin (referred to as DONE in the manual)

Optional properties:
- init-b-gpios: initialization status and configuration error pin
                (referred to as INIT_B in the manual)

Example for full FPGA configuration:

	fpga-region0 {
		compatible = "fpga-region";
		fpga-mgr = <&fpga_mgr_spi>;
		#address-cells = <0x1>;
		#size-cells = <0x1>;
	};

	spi1: spi@10680 {
		compatible = "marvell,armada-xp-spi", "marvell,orion-spi";
		pinctrl-0 = <&spi0_pins>;
		pinctrl-names = "default";
		#address-cells = <1>;
		#size-cells = <0>;
		cell-index = <1>;
		interrupts = <92>;
		clocks = <&coreclk 0>;

		fpga_mgr_spi: fpga-mgr@0 {
			compatible = "xlnx,fpga-slave-serial";
			spi-max-frequency = <60000000>;
			spi-cpha;
			reg = <0>;
			prog_b-gpios = <&gpio0 29 GPIO_ACTIVE_LOW>;
			init-b-gpios = <&gpio0 28 GPIO_ACTIVE_LOW>;
			done-gpios = <&gpio0 9 GPIO_ACTIVE_HIGH>;
		};
	};
