Altera Passive Serial SPI FPGA Manager

Altera FPGAs support a method of loading the bitstream over what is
referred to as "passive serial".
The passive serial link is not technically SPI, and might require extra
circuits in order to play nicely with other SPI slaves on the same bus.

See https://www.altera.com/literature/hb/cyc/cyc_c51013.pdf

Required properties:
- compatible: Must be one of the following:
	"altr,fpga-passive-serial",
	"altr,fpga-arria10-passive-serial"
- reg: SPI chip select of the FPGA
- nconfig-gpios: config pin (referred to as nCONFIG in the manual)
- nstat-gpios: status pin (referred to as nSTATUS in the manual)

Optional properties:
- confd-gpios: confd pin (referred to as CONF_DONE in the manual)

Example:
	fpga: fpga@0 {
		compatible = "altr,fpga-passive-serial";
		spi-max-frequency = <20000000>;
		reg = <0>;
		nconfig-gpios = <&gpio4 9 GPIO_ACTIVE_LOW>;
		nstat-gpios = <&gpio4 11 GPIO_ACTIVE_LOW>;
		confd-gpios = <&gpio4 12 GPIO_ACTIVE_LOW>;
	};
