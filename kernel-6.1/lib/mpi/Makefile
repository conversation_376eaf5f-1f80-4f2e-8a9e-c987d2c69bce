# SPDX-License-Identifier: GPL-2.0
#
# MPI multiprecision maths library (from gpg)
#

obj-$(CONFIG_MPILIB) = mpi.o

mpi-y = \
	generic_mpih-lshift.o		\
	generic_mpih-mul1.o		\
	generic_mpih-mul2.o		\
	generic_mpih-mul3.o		\
	generic_mpih-rshift.o		\
	generic_mpih-sub1.o		\
	generic_mpih-add1.o		\
	ec.o				\
	mpicoder.o			\
	mpi-add.o			\
	mpi-bit.o			\
	mpi-cmp.o			\
	mpi-sub-ui.o			\
	mpi-div.o			\
	mpi-inv.o			\
	mpi-mod.o			\
	mpi-mul.o			\
	mpih-cmp.o			\
	mpih-div.o			\
	mpih-mul.o			\
	mpi-pow.o			\
	mpiutil.o
