#
# KUnit base configuration
#

menuconfig KUNIT
	tristate "KUnit - Enable support for unit tests"
	select GLOB if KUNIT=y
	help
	  Enables support for kernel unit tests (KUnit), a lightweight unit
	  testing and mocking framework for the Linux kernel. These tests are
	  able to be run locally on a developer's workstation without a VM or
	  special hardware when using UML. Can also be used on most other
	  architectures. For more information, please see
	  Documentation/dev-tools/kunit/.

if KUNIT

config KUNIT_DEBUGFS
	bool "KUnit - Enable /sys/kernel/debug/kunit debugfs representation" if !KUNIT_ALL_TESTS
	default KUNIT_ALL_TESTS
	help
	  Enable debugfs representation for kunit.  Currently this consists
	  of /sys/kernel/debug/kunit/<test_suite>/results files for each
	  test suite, which allow users to see results of the last test suite
	  run that occurred.

config KUNIT_TEST
	tristate "KUnit test for KUnit" if !KUNIT_ALL_TESTS
	default KUNIT_ALL_TESTS
	help
	  Enables the unit tests for the KUnit test framework. These tests test
	  the KUnit test framework itself; the tests are both written using
	  KUnit and test KUnit. This option should only be enabled for testing
	  purposes by developers interested in testing that KUnit works as
	  expected.

config KUNIT_EXAMPLE_TEST
	tristate "Example test for KUnit" if !KUNIT_ALL_TESTS
	default KUNIT_ALL_TESTS
	help
	  Enables an example unit test that illustrates some of the basic
	  features of KUnit. This test only exists to help new users understand
	  what KUnit is and how it is used. Please refer to the example test
	  itself, lib/kunit/example-test.c, for more information. This option
	  is intended for curious hackers who would like to understand how to
	  use KUnit for kernel development.

config KUNIT_ALL_TESTS
	tristate "All KUnit tests with satisfied dependencies"
	help
	  Enables all KUnit tests, if they can be enabled.
	  KUnit tests run during boot and output the results to the debug log
	  in TAP format (http://testanything.org/). Only useful for kernel devs
	  running the KUnit test harness, and not intended for inclusion into a
	  production build.

	  For more information on KUnit and unit tests in general please refer
	  to the KUnit documentation in Documentation/dev-tools/kunit/.

	  If unsure, say N.

config KUNIT_DEFAULT_ENABLED
	bool "Default value of kunit.enable"
	default y
	help
	  Sets the default value of kunit.enable. If set to N then KUnit
	  tests will not execute unless kunit.enable=1 is passed to the
	  kernel command line.

	  In most cases this should be left as Y. Only if additional opt-in
	  behavior is needed should this be set to N.

endif # KUNIT
