# SPDX-License-Identifier: GPL-2.0

menu "Crypto library routines"

config CRYPTO_LIB_UTILS
	tristate

config CRYPTO_LIB_AES
	tristate

config CRYPTO_LIB_ARC4
	tristate

config CRYPTO_ARCH_HAVE_LIB_BLAKE2S
	bool
	help
	  Declares whether the architecture provides an arch-specific
	  accelerated implementation of the Blake2s library interface,
	  either builtin or as a module.

config CRYPTO_LIB_BLAKE2S_GENERIC
	def_bool !CRYPTO_ARCH_HAVE_LIB_BLAKE2S
	help
	  This symbol can be depended upon by arch implementations of the
	  Blake2s library interface that require the generic code as a
	  fallback, e.g., for SIMD implementations. If no arch specific
	  implementation is enabled, this implementation serves the users
	  of CRYPTO_LIB_BLAKE2S.

config CRYPTO_ARCH_HAVE_LIB_CHACHA
	tristate
	help
	  Declares whether the architecture provides an arch-specific
	  accelerated implementation of the ChaCha library interface,
	  either builtin or as a module.

config CRYPTO_LIB_CHACHA_GENERIC
	tristate
	select CRYPTO_LIB_UTILS
	help
	  This symbol can be depended upon by arch implementations of the
	  ChaCha library interface that require the generic code as a
	  fallback, e.g., for SIMD implementations. If no arch specific
	  implementation is enabled, this implementation serves the users
	  of CRYPTO_LIB_CHACHA.

config CRYPTO_LIB_CHACHA
	tristate "ChaCha library interface"
	depends on CRYPTO_ARCH_HAVE_LIB_CHACHA || !CRYPTO_ARCH_HAVE_LIB_CHACHA
	select CRYPTO_LIB_CHACHA_GENERIC if CRYPTO_ARCH_HAVE_LIB_CHACHA=n
	help
	  Enable the ChaCha library interface. This interface may be fulfilled
	  by either the generic implementation or an arch-specific one, if one
	  is available and enabled.

config CRYPTO_ARCH_HAVE_LIB_CURVE25519
	tristate
	help
	  Declares whether the architecture provides an arch-specific
	  accelerated implementation of the Curve25519 library interface,
	  either builtin or as a module.

config CRYPTO_LIB_CURVE25519_GENERIC
	tristate
	help
	  This symbol can be depended upon by arch implementations of the
	  Curve25519 library interface that require the generic code as a
	  fallback, e.g., for SIMD implementations. If no arch specific
	  implementation is enabled, this implementation serves the users
	  of CRYPTO_LIB_CURVE25519.

config CRYPTO_LIB_CURVE25519
	tristate "Curve25519 scalar multiplication library"
	depends on CRYPTO_ARCH_HAVE_LIB_CURVE25519 || !CRYPTO_ARCH_HAVE_LIB_CURVE25519
	select CRYPTO_LIB_CURVE25519_GENERIC if CRYPTO_ARCH_HAVE_LIB_CURVE25519=n
	select CRYPTO_LIB_UTILS
	help
	  Enable the Curve25519 library interface. This interface may be
	  fulfilled by either the generic implementation or an arch-specific
	  one, if one is available and enabled.

config CRYPTO_LIB_DES
	tristate

config CRYPTO_LIB_POLY1305_RSIZE
	int
	default 2 if MIPS
	default 11 if X86_64
	default 9 if ARM || ARM64
	default 1

config CRYPTO_ARCH_HAVE_LIB_POLY1305
	tristate
	help
	  Declares whether the architecture provides an arch-specific
	  accelerated implementation of the Poly1305 library interface,
	  either builtin or as a module.

config CRYPTO_LIB_POLY1305_GENERIC
	tristate
	help
	  This symbol can be depended upon by arch implementations of the
	  Poly1305 library interface that require the generic code as a
	  fallback, e.g., for SIMD implementations. If no arch specific
	  implementation is enabled, this implementation serves the users
	  of CRYPTO_LIB_POLY1305.

config CRYPTO_LIB_POLY1305
	tristate "Poly1305 library interface"
	depends on CRYPTO_ARCH_HAVE_LIB_POLY1305 || !CRYPTO_ARCH_HAVE_LIB_POLY1305
	select CRYPTO_LIB_POLY1305_GENERIC if CRYPTO_ARCH_HAVE_LIB_POLY1305=n
	help
	  Enable the Poly1305 library interface. This interface may be fulfilled
	  by either the generic implementation or an arch-specific one, if one
	  is available and enabled.

config CRYPTO_LIB_CHACHA20POLY1305
	tristate "ChaCha20-Poly1305 AEAD support (8-byte nonce library version)"
	depends on CRYPTO_ARCH_HAVE_LIB_CHACHA || !CRYPTO_ARCH_HAVE_LIB_CHACHA
	depends on CRYPTO_ARCH_HAVE_LIB_POLY1305 || !CRYPTO_ARCH_HAVE_LIB_POLY1305
	depends on CRYPTO
	select CRYPTO_LIB_CHACHA
	select CRYPTO_LIB_POLY1305
	select CRYPTO_ALGAPI

config CRYPTO_LIB_SHA1
	tristate

config CRYPTO_LIB_SHA256
	tristate

endmenu
