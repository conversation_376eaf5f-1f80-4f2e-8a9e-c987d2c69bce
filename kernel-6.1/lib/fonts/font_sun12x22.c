// SPDX-License-Identifier: GPL-2.0
#include <linux/font.h>

#define FONTDATAMAX 11264

static const struct font_data fontdata_sun12x22 = {
	{ 0, 0, FONTDATAMAX, 0 }, {
	/* 0 0x00 '^@' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 1 0x01 '^A' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x1f, 0xc0, /* 000111111100 */
	0x30, 0x60, /* 001100000110 */
	0x65, 0x30, /* 011001010011 */
	0x6d, 0xb0, /* 011011011011 */
	0x60, 0x30, /* 011000000011 */
	0x62, 0x30, /* 011000100011 */
	0x62, 0x30, /* 011000100011 */
	0x60, 0x30, /* 011000000011 */
	0x6f, 0xb0, /* 011011111011 */
	0x67, 0x30, /* 011001110011 */
	0x30, 0x60, /* 001100000110 */
	0x1f, 0xc0, /* 000111111100 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 2 0x02 '^B' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x1f, 0xc0, /* 000111111100 */
	0x3f, 0xe0, /* 001111111110 */
	0x7a, 0xf0, /* 011110101111 */
	0x72, 0x70, /* 011100100111 */
	0x7f, 0xf0, /* 011111111111 */
	0x7d, 0xf0, /* 011111011111 */
	0x7d, 0xf0, /* 011111011111 */
	0x7f, 0xf0, /* 011111111111 */
	0x70, 0x70, /* 011100000111 */
	0x78, 0xf0, /* 011110001111 */
	0x3f, 0xe0, /* 001111111110 */
	0x1f, 0xc0, /* 000111111100 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 3 0x03 '^C' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x19, 0x80, /* 000110011000 */
	0x3f, 0xc0, /* 001111111100 */
	0x7f, 0xe0, /* 011111111110 */
	0x3f, 0xc0, /* 001111111100 */
	0x3f, 0xc0, /* 001111111100 */
	0x1f, 0x80, /* 000111111000 */
	0x1f, 0x80, /* 000111111000 */
	0x0f, 0x00, /* 000011110000 */
	0x0f, 0x00, /* 000011110000 */
	0x06, 0x00, /* 000001100000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 4 0x04 '^D' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x02, 0x00, /* 000000100000 */
	0x07, 0x00, /* 000001110000 */
	0x0f, 0x80, /* 000011111000 */
	0x0f, 0x80, /* 000011111000 */
	0x1f, 0xc0, /* 000111111100 */
	0x1f, 0xc0, /* 000111111100 */
	0x3f, 0xe0, /* 001111111110 */
	0x1f, 0xc0, /* 000111111100 */
	0x1f, 0xc0, /* 000111111100 */
	0x0f, 0x80, /* 000011111000 */
	0x0f, 0x80, /* 000011111000 */
	0x07, 0x00, /* 000001110000 */
	0x02, 0x00, /* 000000100000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 5 0x05 '^E' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x02, 0x00, /* 000000100000 */
	0x07, 0x00, /* 000001110000 */
	0x07, 0x00, /* 000001110000 */
	0x02, 0x00, /* 000000100000 */
	0x18, 0xc0, /* 000110001100 */
	0x3d, 0xe0, /* 001111011110 */
	0x3d, 0xe0, /* 001111011110 */
	0x1a, 0xc0, /* 000110101100 */
	0x02, 0x00, /* 000000100000 */
	0x07, 0x00, /* 000001110000 */
	0x0f, 0x80, /* 000011111000 */
	0x1f, 0xc0, /* 000111111100 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 6 0x06 '^F' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x06, 0x00, /* 000001100000 */
	0x0f, 0x00, /* 000011110000 */
	0x1f, 0x80, /* 000111111000 */
	0x1f, 0x80, /* 000111111000 */
	0x3f, 0xc0, /* 001111111100 */
	0x7f, 0xe0, /* 011111111110 */
	0x7f, 0xe0, /* 011111111110 */
	0x36, 0xc0, /* 001101101100 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x0f, 0x00, /* 000011110000 */
	0x1f, 0x80, /* 000111111000 */
	0x3f, 0xc0, /* 001111111100 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 7 0x07 '^G' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x06, 0x00, /* 000001100000 */
	0x0f, 0x00, /* 000011110000 */
	0x0f, 0x00, /* 000011110000 */
	0x1f, 0x80, /* 000111111000 */
	0x1f, 0x80, /* 000111111000 */
	0x3f, 0xc0, /* 001111111100 */
	0x3f, 0xc0, /* 001111111100 */
	0x1f, 0x80, /* 000111111000 */
	0x1f, 0x80, /* 000111111000 */
	0x0f, 0x00, /* 000011110000 */
	0x0f, 0x00, /* 000011110000 */
	0x06, 0x00, /* 000001100000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 8 0x08 '^H' */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0xf9, 0xf0, /* 111110011111 */
	0xf0, 0xf0, /* 111100001111 */
	0xf0, 0xf0, /* 111100001111 */
	0xe0, 0x70, /* 111000000111 */
	0xe0, 0x70, /* 111000000111 */
	0xc0, 0x30, /* 110000000011 */
	0xc0, 0x30, /* 110000000011 */
	0xe0, 0x70, /* 111000000111 */
	0xe0, 0x70, /* 111000000111 */
	0xf0, 0xf0, /* 111100001111 */
	0xf0, 0xf0, /* 111100001111 */
	0xf9, 0xf0, /* 111110011111 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */

	/* 9 0x09 '^I' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x06, 0x00, /* 000001100000 */
	0x0f, 0x00, /* 000011110000 */
	0x0f, 0x00, /* 000011110000 */
	0x19, 0x80, /* 000110011000 */
	0x19, 0x80, /* 000110011000 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x19, 0x80, /* 000110011000 */
	0x19, 0x80, /* 000110011000 */
	0x0f, 0x00, /* 000011110000 */
	0x0f, 0x00, /* 000011110000 */
	0x06, 0x00, /* 000001100000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 10 0x0a '^J' */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0xf9, 0xf0, /* 111110011111 */
	0xf0, 0xf0, /* 111100001111 */
	0xf0, 0xf0, /* 111100001111 */
	0xe6, 0x70, /* 111001100111 */
	0xe6, 0x70, /* 111001100111 */
	0xcf, 0x30, /* 110011110011 */
	0xcf, 0x30, /* 110011110011 */
	0xe6, 0x70, /* 111001100111 */
	0xe6, 0x70, /* 111001100111 */
	0xf0, 0xf0, /* 111100001111 */
	0xf0, 0xf0, /* 111100001111 */
	0xf9, 0xf0, /* 111110011111 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */

	/* 11 0x0b '^K' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x0f, 0xe0, /* 000011111110 */
	0x0f, 0xe0, /* 000011111110 */
	0x01, 0xe0, /* 000000011110 */
	0x03, 0x60, /* 000000110110 */
	0x06, 0x60, /* 000001100110 */
	0x1e, 0x00, /* 000111100000 */
	0x33, 0x00, /* 001100110000 */
	0x33, 0x00, /* 001100110000 */
	0x61, 0x80, /* 011000011000 */
	0x61, 0x80, /* 011000011000 */
	0x33, 0x00, /* 001100110000 */
	0x33, 0x00, /* 001100110000 */
	0x1e, 0x00, /* 000111100000 */
	0x0c, 0x00, /* 000011000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 12 0x0c '^L' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x06, 0x00, /* 000001100000 */
	0x0f, 0x00, /* 000011110000 */
	0x19, 0x80, /* 000110011000 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x19, 0x80, /* 000110011000 */
	0x0f, 0x00, /* 000011110000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x3f, 0xc0, /* 001111111100 */
	0x3f, 0xc0, /* 001111111100 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 13 0x0d '^M' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x0f, 0xe0, /* 000011111110 */
	0x0c, 0x60, /* 000011000110 */
	0x0c, 0x60, /* 000011000110 */
	0x0f, 0xe0, /* 000011111110 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x3c, 0x00, /* 001111000000 */
	0x7c, 0x00, /* 011111000000 */
	0x78, 0x00, /* 011110000000 */
	0x30, 0x00, /* 001100000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 14 0x0e '^N' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x1f, 0xe0, /* 000111111110 */
	0x18, 0x60, /* 000110000110 */
	0x18, 0x60, /* 000110000110 */
	0x1f, 0xe0, /* 000111111110 */
	0x18, 0x60, /* 000110000110 */
	0x18, 0x60, /* 000110000110 */
	0x18, 0x60, /* 000110000110 */
	0x18, 0x60, /* 000110000110 */
	0x18, 0x60, /* 000110000110 */
	0x18, 0x60, /* 000110000110 */
	0x19, 0xe0, /* 000110011110 */
	0x1b, 0xe0, /* 000110111110 */
	0x1b, 0xc0, /* 000110111100 */
	0x79, 0x80, /* 011110011000 */
	0xf8, 0x00, /* 111110000000 */
	0xf0, 0x00, /* 111100000000 */
	0x60, 0x00, /* 011000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 15 0x0f '^O' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x18, 0xc0, /* 000110001100 */
	0x0d, 0x80, /* 000011011000 */
	0x6d, 0xb0, /* 011011011011 */
	0x3d, 0xe0, /* 001111011110 */
	0x00, 0x00, /* 000000000000 */
	0x3d, 0xe0, /* 001111011110 */
	0x6d, 0xb0, /* 011011011011 */
	0x0d, 0x80, /* 000011011000 */
	0x18, 0xc0, /* 000110001100 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 16 0x10 '^P' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x20, /* 000000000010 */
	0x00, 0x60, /* 000000000110 */
	0x00, 0xe0, /* 000000001110 */
	0x01, 0xe0, /* 000000011110 */
	0x03, 0xe0, /* 000000111110 */
	0x07, 0xe0, /* 000001111110 */
	0x0f, 0xe0, /* 000011111110 */
	0x1f, 0xe0, /* 000111111110 */
	0x3f, 0xe0, /* 001111111110 */
	0x7f, 0xe0, /* 011111111110 */
	0x3f, 0xe0, /* 001111111110 */
	0x1f, 0xe0, /* 000111111110 */
	0x0f, 0xe0, /* 000011111110 */
	0x07, 0xe0, /* 000001111110 */
	0x03, 0xe0, /* 000000111110 */
	0x01, 0xe0, /* 000000011110 */
	0x00, 0xe0, /* 000000001110 */
	0x00, 0x60, /* 000000000110 */
	0x00, 0x20, /* 000000000010 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 17 0x11 '^Q' */
	0x00, 0x00, /* 000000000000 */
	0x40, 0x00, /* 010000000000 */
	0x60, 0x00, /* 011000000000 */
	0x70, 0x00, /* 011100000000 */
	0x78, 0x00, /* 011110000000 */
	0x7c, 0x00, /* 011111000000 */
	0x7e, 0x00, /* 011111100000 */
	0x7f, 0x00, /* 011111110000 */
	0x7f, 0x80, /* 011111111000 */
	0x7f, 0xc0, /* 011111111100 */
	0x7f, 0xe0, /* 011111111110 */
	0x7f, 0xc0, /* 011111111100 */
	0x7f, 0x80, /* 011111111000 */
	0x7f, 0x00, /* 011111110000 */
	0x7e, 0x00, /* 011111100000 */
	0x7c, 0x00, /* 011111000000 */
	0x78, 0x00, /* 011110000000 */
	0x70, 0x00, /* 011100000000 */
	0x60, 0x00, /* 011000000000 */
	0x40, 0x00, /* 010000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 18 0x12 '^R' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x04, 0x00, /* 000001000000 */
	0x0e, 0x00, /* 000011100000 */
	0x1f, 0x00, /* 000111110000 */
	0x3f, 0x80, /* 001111111000 */
	0x7f, 0xc0, /* 011111111100 */
	0x0e, 0x00, /* 000011100000 */
	0x0e, 0x00, /* 000011100000 */
	0x7f, 0xc0, /* 011111111100 */
	0x3f, 0x80, /* 001111111000 */
	0x1f, 0x00, /* 000111110000 */
	0x0e, 0x00, /* 000011100000 */
	0x04, 0x00, /* 000001000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 19 0x13 '^S' */
	0x00, 0x00, /* 000000000000 */
	0x31, 0x80, /* 001100011000 */
	0x31, 0x80, /* 001100011000 */
	0x31, 0x80, /* 001100011000 */
	0x31, 0x80, /* 001100011000 */
	0x31, 0x80, /* 001100011000 */
	0x31, 0x80, /* 001100011000 */
	0x31, 0x80, /* 001100011000 */
	0x31, 0x80, /* 001100011000 */
	0x31, 0x80, /* 001100011000 */
	0x31, 0x80, /* 001100011000 */
	0x31, 0x80, /* 001100011000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x31, 0x80, /* 001100011000 */
	0x31, 0x80, /* 001100011000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 20 0x14 '^T' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x1f, 0xf0, /* 000111111111 */
	0x3c, 0xc0, /* 001111001100 */
	0x7c, 0xc0, /* 011111001100 */
	0x7c, 0xc0, /* 011111001100 */
	0x7c, 0xc0, /* 011111001100 */
	0x3c, 0xc0, /* 001111001100 */
	0x1c, 0xc0, /* 000111001100 */
	0x0c, 0xc0, /* 000011001100 */
	0x0c, 0xc0, /* 000011001100 */
	0x0c, 0xc0, /* 000011001100 */
	0x0c, 0xc0, /* 000011001100 */
	0x0c, 0xc0, /* 000011001100 */
	0x0c, 0xc0, /* 000011001100 */
	0x1c, 0xe0, /* 000111001110 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 21 0x15 '^U' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x1f, 0x00, /* 000111110000 */
	0x31, 0x80, /* 001100011000 */
	0x31, 0x80, /* 001100011000 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x00, /* 001100000000 */
	0x1f, 0x00, /* 000111110000 */
	0x31, 0x80, /* 001100011000 */
	0x31, 0x80, /* 001100011000 */
	0x1f, 0x00, /* 000111110000 */
	0x01, 0x80, /* 000000011000 */
	0x01, 0x80, /* 000000011000 */
	0x31, 0x80, /* 001100011000 */
	0x31, 0x80, /* 001100011000 */
	0x1f, 0x00, /* 000111110000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 22 0x16 '^V' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x7f, 0xe0, /* 011111111110 */
	0x7f, 0xe0, /* 011111111110 */
	0x7f, 0xe0, /* 011111111110 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 23 0x17 '^W' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x04, 0x00, /* 000001000000 */
	0x0e, 0x00, /* 000011100000 */
	0x1f, 0x00, /* 000111110000 */
	0x3f, 0x80, /* 001111111000 */
	0x7f, 0xc0, /* 011111111100 */
	0x0e, 0x00, /* 000011100000 */
	0x0e, 0x00, /* 000011100000 */
	0x7f, 0xc0, /* 011111111100 */
	0x3f, 0x80, /* 001111111000 */
	0x1f, 0x00, /* 000111110000 */
	0x0e, 0x00, /* 000011100000 */
	0x04, 0x00, /* 000001000000 */
	0x00, 0x00, /* 000000000000 */
	0x7f, 0xe0, /* 011111111110 */
	0x7f, 0xe0, /* 011111111110 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 24 0x18 '^X' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x04, 0x00, /* 000001000000 */
	0x0e, 0x00, /* 000011100000 */
	0x1f, 0x00, /* 000111110000 */
	0x3f, 0x80, /* 001111111000 */
	0x7f, 0xc0, /* 011111111100 */
	0x0e, 0x00, /* 000011100000 */
	0x0e, 0x00, /* 000011100000 */
	0x0e, 0x00, /* 000011100000 */
	0x0e, 0x00, /* 000011100000 */
	0x0e, 0x00, /* 000011100000 */
	0x0e, 0x00, /* 000011100000 */
	0x0e, 0x00, /* 000011100000 */
	0x0e, 0x00, /* 000011100000 */
	0x0e, 0x00, /* 000011100000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 25 0x19 '^Y' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x0e, 0x00, /* 000011100000 */
	0x0e, 0x00, /* 000011100000 */
	0x0e, 0x00, /* 000011100000 */
	0x0e, 0x00, /* 000011100000 */
	0x0e, 0x00, /* 000011100000 */
	0x0e, 0x00, /* 000011100000 */
	0x0e, 0x00, /* 000011100000 */
	0x0e, 0x00, /* 000011100000 */
	0x0e, 0x00, /* 000011100000 */
	0x0e, 0x00, /* 000011100000 */
	0x7f, 0xc0, /* 011111111100 */
	0x3f, 0x80, /* 001111111000 */
	0x1f, 0x00, /* 000111110000 */
	0x0e, 0x00, /* 000011100000 */
	0x04, 0x00, /* 000001000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 26 0x1a '^Z' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x08, 0x00, /* 000010000000 */
	0x18, 0x00, /* 000110000000 */
	0x38, 0x00, /* 001110000000 */
	0x7f, 0xe0, /* 011111111110 */
	0xff, 0xe0, /* 111111111110 */
	0x7f, 0xe0, /* 011111111110 */
	0x38, 0x00, /* 001110000000 */
	0x18, 0x00, /* 000110000000 */
	0x08, 0x00, /* 000010000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 27 0x1b '^[' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x01, 0x00, /* 000000010000 */
	0x01, 0x80, /* 000000011000 */
	0x01, 0xc0, /* 000000011100 */
	0x7f, 0xe0, /* 011111111110 */
	0x7f, 0xf0, /* 011111111111 */
	0x7f, 0xe0, /* 011111111110 */
	0x01, 0xc0, /* 000000011100 */
	0x01, 0x80, /* 000000011000 */
	0x01, 0x00, /* 000000010000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 28 0x1c '^\' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x00, /* 001100000000 */
	0x3f, 0xe0, /* 001111111110 */
	0x3f, 0xe0, /* 001111111110 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 29 0x1d '^]' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x09, 0x00, /* 000010010000 */
	0x19, 0x80, /* 000110011000 */
	0x39, 0xc0, /* 001110011100 */
	0x7f, 0xe0, /* 011111111110 */
	0xff, 0xf0, /* 111111111111 */
	0x7f, 0xe0, /* 011111111110 */
	0x39, 0xc0, /* 001110011100 */
	0x19, 0x80, /* 000110011000 */
	0x09, 0x00, /* 000010010000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 30 0x1e '^^' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x04, 0x00, /* 000001000000 */
	0x04, 0x00, /* 000001000000 */
	0x0e, 0x00, /* 000011100000 */
	0x0e, 0x00, /* 000011100000 */
	0x1f, 0x00, /* 000111110000 */
	0x1f, 0x00, /* 000111110000 */
	0x3f, 0x80, /* 001111111000 */
	0x3f, 0x80, /* 001111111000 */
	0x7f, 0xc0, /* 011111111100 */
	0x7f, 0xc0, /* 011111111100 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 31 0x1f '^_' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x7f, 0xc0, /* 011111111100 */
	0x7f, 0xc0, /* 011111111100 */
	0x3f, 0x80, /* 001111111000 */
	0x3f, 0x80, /* 001111111000 */
	0x1f, 0x00, /* 000111110000 */
	0x1f, 0x00, /* 000111110000 */
	0x0e, 0x00, /* 000011100000 */
	0x0e, 0x00, /* 000011100000 */
	0x04, 0x00, /* 000001000000 */
	0x04, 0x00, /* 000001000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 32 0x20 ' ' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 33 0x21 '!' */
	0x00, 0x00, /* 000000000000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 34 0x22 '"' */
	0x00, 0x00, /* 000000000000 */
	0x19, 0x80, /* 000110011000 */
	0x19, 0x80, /* 000110011000 */
	0x19, 0x80, /* 000110011000 */
	0x19, 0x80, /* 000110011000 */
	0x19, 0x80, /* 000110011000 */
	0x19, 0x80, /* 000110011000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 35 0x23 '#' */
	0x00, 0x00, /* 000000000000 */
	0x03, 0x30, /* 000000110011 */
	0x03, 0x30, /* 000000110011 */
	0x03, 0x30, /* 000000110011 */
	0x06, 0x60, /* 000001100110 */
	0x1f, 0xf0, /* 000111111111 */
	0x1f, 0xf0, /* 000111111111 */
	0x0c, 0xc0, /* 000011001100 */
	0x0c, 0xc0, /* 000011001100 */
	0x19, 0x80, /* 000110011000 */
	0x19, 0x80, /* 000110011000 */
	0x7f, 0xc0, /* 011111111100 */
	0x7f, 0xc0, /* 011111111100 */
	0x33, 0x00, /* 001100110000 */
	0x66, 0x00, /* 011001100000 */
	0x66, 0x00, /* 011001100000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 36 0x24 '$' */
	0x00, 0x00, /* 000000000000 */
	0x06, 0x00, /* 000001100000 */
	0x1f, 0x80, /* 000111111000 */
	0x3f, 0xc0, /* 001111111100 */
	0x66, 0xe0, /* 011001101110 */
	0x66, 0x60, /* 011001100110 */
	0x66, 0x00, /* 011001100000 */
	0x3e, 0x00, /* 001111100000 */
	0x1f, 0x80, /* 000111111000 */
	0x07, 0xc0, /* 000001111100 */
	0x06, 0x60, /* 000001100110 */
	0x06, 0x60, /* 000001100110 */
	0x66, 0x60, /* 011001100110 */
	0x7f, 0xc0, /* 011111111100 */
	0x3f, 0x80, /* 001111111000 */
	0x06, 0x00, /* 000001100000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 37 0x25 '%' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x38, 0xc0, /* 001110001100 */
	0x4c, 0xc0, /* 010011001100 */
	0x45, 0x80, /* 010001011000 */
	0x65, 0x80, /* 011001011000 */
	0x3b, 0x00, /* 001110110000 */
	0x03, 0x00, /* 000000110000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x0c, 0x00, /* 000011000000 */
	0x0d, 0xc0, /* 000011011100 */
	0x1a, 0x60, /* 000110100110 */
	0x1a, 0x20, /* 000110100010 */
	0x33, 0x20, /* 001100110010 */
	0x31, 0xc0, /* 001100011100 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 38 0x26 '&' */
	0x00, 0x00, /* 000000000000 */
	0x07, 0x00, /* 000001110000 */
	0x0f, 0x80, /* 000011111000 */
	0x18, 0xc0, /* 000110001100 */
	0x18, 0xc0, /* 000110001100 */
	0x18, 0xc0, /* 000110001100 */
	0x0f, 0x80, /* 000011111000 */
	0x1e, 0x00, /* 000111100000 */
	0x3e, 0x00, /* 001111100000 */
	0x77, 0x00, /* 011101110000 */
	0x63, 0x60, /* 011000110110 */
	0x61, 0xe0, /* 011000011110 */
	0x61, 0xc0, /* 011000011100 */
	0x61, 0x80, /* 011000011000 */
	0x3f, 0xe0, /* 001111111110 */
	0x1e, 0x60, /* 000111100110 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 39 0x27 ''' */
	0x00, 0x00, /* 000000000000 */
	0x0c, 0x00, /* 000011000000 */
	0x1e, 0x00, /* 000111100000 */
	0x1e, 0x00, /* 000111100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x0c, 0x00, /* 000011000000 */
	0x18, 0x00, /* 000110000000 */
	0x10, 0x00, /* 000100000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 40 0x28 '(' */
	0x00, 0x00, /* 000000000000 */
	0x01, 0x80, /* 000000011000 */
	0x03, 0x00, /* 000000110000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x03, 0x00, /* 000000110000 */
	0x01, 0x80, /* 000000011000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 41 0x29 ')' */
	0x00, 0x00, /* 000000000000 */
	0x18, 0x00, /* 000110000000 */
	0x0c, 0x00, /* 000011000000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x03, 0x00, /* 000000110000 */
	0x03, 0x00, /* 000000110000 */
	0x03, 0x00, /* 000000110000 */
	0x03, 0x00, /* 000000110000 */
	0x03, 0x00, /* 000000110000 */
	0x03, 0x00, /* 000000110000 */
	0x03, 0x00, /* 000000110000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x0c, 0x00, /* 000011000000 */
	0x18, 0x00, /* 000110000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 42 0x2a '*' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x0f, 0x00, /* 000011110000 */
	0x06, 0x00, /* 000001100000 */
	0x66, 0x60, /* 011001100110 */
	0x76, 0xe0, /* 011101101110 */
	0x19, 0x80, /* 000110011000 */
	0x00, 0x00, /* 000000000000 */
	0x19, 0x80, /* 000110011000 */
	0x76, 0xe0, /* 011101101110 */
	0x66, 0x60, /* 011001100110 */
	0x06, 0x00, /* 000001100000 */
	0x0f, 0x00, /* 000011110000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 43 0x2b '+' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x7f, 0xe0, /* 011111111110 */
	0x7f, 0xe0, /* 011111111110 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 44 0x2c ',' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x0c, 0x00, /* 000011000000 */
	0x1e, 0x00, /* 000111100000 */
	0x1e, 0x00, /* 000111100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x0c, 0x00, /* 000011000000 */
	0x18, 0x00, /* 000110000000 */
	0x10, 0x00, /* 000100000000 */
	0x00, 0x00, /* 000000000000 */

	/* 45 0x2d '-' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x7f, 0xe0, /* 011111111110 */
	0x7f, 0xe0, /* 011111111110 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 46 0x2e '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x0c, 0x00, /* 000011000000 */
	0x1e, 0x00, /* 000111100000 */
	0x1e, 0x00, /* 000111100000 */
	0x0c, 0x00, /* 000011000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 47 0x2f '/' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x60, /* 000000000110 */
	0x00, 0xc0, /* 000000001100 */
	0x00, 0xc0, /* 000000001100 */
	0x01, 0x80, /* 000000011000 */
	0x01, 0x80, /* 000000011000 */
	0x03, 0x00, /* 000000110000 */
	0x03, 0x00, /* 000000110000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x18, 0x00, /* 000110000000 */
	0x18, 0x00, /* 000110000000 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x00, /* 001100000000 */
	0x60, 0x00, /* 011000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 48 0x30 '0' */
	0x00, 0x00, /* 000000000000 */
	0x07, 0x00, /* 000001110000 */
	0x0f, 0x80, /* 000011111000 */
	0x11, 0x80, /* 000100011000 */
	0x10, 0xc0, /* 000100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0x80, /* 001100001000 */
	0x18, 0x80, /* 000110001000 */
	0x1f, 0x00, /* 000111110000 */
	0x0e, 0x00, /* 000011100000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 49 0x31 '1' */
	0x00, 0x00, /* 000000000000 */
	0x02, 0x00, /* 000000100000 */
	0x06, 0x00, /* 000001100000 */
	0x0e, 0x00, /* 000011100000 */
	0x1e, 0x00, /* 000111100000 */
	0x36, 0x00, /* 001101100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x3f, 0xc0, /* 001111111100 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 50 0x32 '2' */
	0x00, 0x00, /* 000000000000 */
	0x1f, 0x00, /* 000111110000 */
	0x3f, 0x80, /* 001111111000 */
	0x61, 0xc0, /* 011000011100 */
	0x40, 0xc0, /* 010000001100 */
	0x00, 0xc0, /* 000000001100 */
	0x00, 0xc0, /* 000000001100 */
	0x00, 0xc0, /* 000000001100 */
	0x01, 0x80, /* 000000011000 */
	0x03, 0x00, /* 000000110000 */
	0x06, 0x00, /* 000001100000 */
	0x0c, 0x00, /* 000011000000 */
	0x18, 0x00, /* 000110000000 */
	0x30, 0x20, /* 001100000010 */
	0x7f, 0xe0, /* 011111111110 */
	0x7f, 0xe0, /* 011111111110 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 51 0x33 '3' */
	0x00, 0x00, /* 000000000000 */
	0x0f, 0x80, /* 000011111000 */
	0x1f, 0xc0, /* 000111111100 */
	0x20, 0xe0, /* 001000001110 */
	0x40, 0x60, /* 010000000110 */
	0x00, 0x60, /* 000000000110 */
	0x00, 0xe0, /* 000000001110 */
	0x07, 0xc0, /* 000001111100 */
	0x0f, 0xc0, /* 000011111100 */
	0x00, 0xe0, /* 000000001110 */
	0x00, 0x60, /* 000000000110 */
	0x00, 0x60, /* 000000000110 */
	0x40, 0x60, /* 010000000110 */
	0x60, 0x40, /* 011000000100 */
	0x3f, 0x80, /* 001111111000 */
	0x1f, 0x00, /* 000111110000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 52 0x34 '4' */
	0x00, 0x00, /* 000000000000 */
	0x01, 0x80, /* 000000011000 */
	0x03, 0x80, /* 000000111000 */
	0x03, 0x80, /* 000000111000 */
	0x05, 0x80, /* 000001011000 */
	0x05, 0x80, /* 000001011000 */
	0x09, 0x80, /* 000010011000 */
	0x09, 0x80, /* 000010011000 */
	0x11, 0x80, /* 000100011000 */
	0x11, 0x80, /* 000100011000 */
	0x21, 0x80, /* 001000011000 */
	0x3f, 0xe0, /* 001111111110 */
	0x7f, 0xe0, /* 011111111110 */
	0x01, 0x80, /* 000000011000 */
	0x01, 0x80, /* 000000011000 */
	0x01, 0x80, /* 000000011000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 53 0x35 '5' */
	0x00, 0x00, /* 000000000000 */
	0x0f, 0xc0, /* 000011111100 */
	0x0f, 0xc0, /* 000011111100 */
	0x10, 0x00, /* 000100000000 */
	0x10, 0x00, /* 000100000000 */
	0x20, 0x00, /* 001000000000 */
	0x3f, 0x80, /* 001111111000 */
	0x31, 0xc0, /* 001100011100 */
	0x00, 0xe0, /* 000000001110 */
	0x00, 0x60, /* 000000000110 */
	0x00, 0x60, /* 000000000110 */
	0x00, 0x60, /* 000000000110 */
	0x40, 0x60, /* 010000000110 */
	0x60, 0x60, /* 011000000110 */
	0x30, 0xc0, /* 001100001100 */
	0x1f, 0x80, /* 000111111000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 54 0x36 '6' */
	0x00, 0x00, /* 000000000000 */
	0x07, 0x00, /* 000001110000 */
	0x0c, 0x00, /* 000011000000 */
	0x18, 0x00, /* 000110000000 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x00, /* 001100000000 */
	0x60, 0x00, /* 011000000000 */
	0x67, 0x80, /* 011001111000 */
	0x6f, 0xc0, /* 011011111100 */
	0x70, 0xe0, /* 011100001110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x70, 0x40, /* 011100000100 */
	0x3f, 0x80, /* 001111111000 */
	0x1f, 0x00, /* 000111110000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 55 0x37 '7' */
	0x00, 0x00, /* 000000000000 */
	0x1f, 0xe0, /* 000111111110 */
	0x3f, 0xe0, /* 001111111110 */
	0x60, 0x40, /* 011000000100 */
	0x00, 0x40, /* 000000000100 */
	0x00, 0xc0, /* 000000001100 */
	0x00, 0x80, /* 000000001000 */
	0x00, 0x80, /* 000000001000 */
	0x01, 0x80, /* 000000011000 */
	0x01, 0x00, /* 000000010000 */
	0x01, 0x00, /* 000000010000 */
	0x03, 0x00, /* 000000110000 */
	0x02, 0x00, /* 000000100000 */
	0x02, 0x00, /* 000000100000 */
	0x06, 0x00, /* 000001100000 */
	0x04, 0x00, /* 000001000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 56 0x38 '8' */
	0x00, 0x00, /* 000000000000 */
	0x0f, 0x00, /* 000011110000 */
	0x11, 0x80, /* 000100011000 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x18, 0x80, /* 000110001000 */
	0x0d, 0x00, /* 000011010000 */
	0x06, 0x00, /* 000001100000 */
	0x0b, 0x00, /* 000010110000 */
	0x11, 0x80, /* 000100011000 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x18, 0x80, /* 000110001000 */
	0x0f, 0x00, /* 000011110000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 57 0x39 '9' */
	0x00, 0x00, /* 000000000000 */
	0x0f, 0x80, /* 000011111000 */
	0x11, 0xc0, /* 000100011100 */
	0x20, 0xe0, /* 001000001110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x70, 0xe0, /* 011100001110 */
	0x3f, 0x60, /* 001111110110 */
	0x1e, 0x60, /* 000111100110 */
	0x00, 0x60, /* 000000000110 */
	0x00, 0xc0, /* 000000001100 */
	0x00, 0xc0, /* 000000001100 */
	0x01, 0x80, /* 000000011000 */
	0x07, 0x00, /* 000001110000 */
	0x3c, 0x00, /* 001111000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 58 0x3a ':' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x0c, 0x00, /* 000011000000 */
	0x1e, 0x00, /* 000111100000 */
	0x1e, 0x00, /* 000111100000 */
	0x0c, 0x00, /* 000011000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x0c, 0x00, /* 000011000000 */
	0x1e, 0x00, /* 000111100000 */
	0x1e, 0x00, /* 000111100000 */
	0x0c, 0x00, /* 000011000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 59 0x3b ';' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x0c, 0x00, /* 000011000000 */
	0x1e, 0x00, /* 000111100000 */
	0x1e, 0x00, /* 000111100000 */
	0x0c, 0x00, /* 000011000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x0c, 0x00, /* 000011000000 */
	0x1e, 0x00, /* 000111100000 */
	0x1e, 0x00, /* 000111100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x0c, 0x00, /* 000011000000 */
	0x18, 0x00, /* 000110000000 */
	0x10, 0x00, /* 000100000000 */
	0x00, 0x00, /* 000000000000 */

	/* 60 0x3c '<' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x60, /* 000000000110 */
	0x01, 0xc0, /* 000000011100 */
	0x07, 0x00, /* 000001110000 */
	0x1c, 0x00, /* 000111000000 */
	0x70, 0x00, /* 011100000000 */
	0x70, 0x00, /* 011100000000 */
	0x1c, 0x00, /* 000111000000 */
	0x07, 0x00, /* 000001110000 */
	0x01, 0xc0, /* 000000011100 */
	0x00, 0x60, /* 000000000110 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 61 0x3d '=' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x7f, 0xe0, /* 011111111110 */
	0x7f, 0xe0, /* 011111111110 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x7f, 0xe0, /* 011111111110 */
	0x7f, 0xe0, /* 011111111110 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 62 0x3e '>' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x60, 0x00, /* 011000000000 */
	0x38, 0x00, /* 001110000000 */
	0x0e, 0x00, /* 000011100000 */
	0x03, 0x80, /* 000000111000 */
	0x00, 0xe0, /* 000000001110 */
	0x00, 0xe0, /* 000000001110 */
	0x03, 0x80, /* 000000111000 */
	0x0e, 0x00, /* 000011100000 */
	0x38, 0x00, /* 001110000000 */
	0x60, 0x00, /* 011000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 63 0x3f '?' */
	0x00, 0x00, /* 000000000000 */
	0x0f, 0x00, /* 000011110000 */
	0x1f, 0x80, /* 000111111000 */
	0x39, 0xc0, /* 001110011100 */
	0x20, 0xc0, /* 001000001100 */
	0x00, 0xc0, /* 000000001100 */
	0x00, 0xc0, /* 000000001100 */
	0x01, 0x80, /* 000000011000 */
	0x03, 0x00, /* 000000110000 */
	0x06, 0x00, /* 000001100000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 64 0x40 '@' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x0f, 0x80, /* 000011111000 */
	0x3f, 0xc0, /* 001111111100 */
	0x30, 0x60, /* 001100000110 */
	0x60, 0x60, /* 011000000110 */
	0x67, 0x20, /* 011001110010 */
	0x6f, 0xa0, /* 011011111010 */
	0x6c, 0xa0, /* 011011001010 */
	0x6c, 0xa0, /* 011011001010 */
	0x67, 0xe0, /* 011001111110 */
	0x60, 0x00, /* 011000000000 */
	0x30, 0x00, /* 001100000000 */
	0x3f, 0xe0, /* 001111111110 */
	0x0f, 0xe0, /* 000011111110 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 65 0x41 'A' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x0b, 0x00, /* 000010110000 */
	0x0b, 0x00, /* 000010110000 */
	0x09, 0x00, /* 000010010000 */
	0x11, 0x80, /* 000100011000 */
	0x11, 0x80, /* 000100011000 */
	0x10, 0x80, /* 000100001000 */
	0x3f, 0xc0, /* 001111111100 */
	0x20, 0xc0, /* 001000001100 */
	0x20, 0x40, /* 001000000100 */
	0x40, 0x60, /* 010000000110 */
	0x40, 0x60, /* 010000000110 */
	0xe0, 0xf0, /* 111000001111 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 66 0x42 'B' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0xff, 0x00, /* 111111110000 */
	0x60, 0x80, /* 011000001000 */
	0x60, 0xc0, /* 011000001100 */
	0x60, 0xc0, /* 011000001100 */
	0x60, 0xc0, /* 011000001100 */
	0x61, 0x80, /* 011000011000 */
	0x7f, 0x80, /* 011111111000 */
	0x60, 0xc0, /* 011000001100 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0xc0, /* 011000001100 */
	0xff, 0x80, /* 111111111000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 67 0x43 'C' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x0f, 0xc0, /* 000011111100 */
	0x10, 0x60, /* 000100000110 */
	0x20, 0x20, /* 001000000010 */
	0x20, 0x00, /* 001000000000 */
	0x60, 0x00, /* 011000000000 */
	0x60, 0x00, /* 011000000000 */
	0x60, 0x00, /* 011000000000 */
	0x60, 0x00, /* 011000000000 */
	0x60, 0x00, /* 011000000000 */
	0x60, 0x00, /* 011000000000 */
	0x20, 0x00, /* 001000000000 */
	0x30, 0x20, /* 001100000010 */
	0x18, 0x40, /* 000110000100 */
	0x0f, 0x80, /* 000011111000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 68 0x44 'D' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0xff, 0x00, /* 111111110000 */
	0x61, 0xc0, /* 011000011100 */
	0x60, 0xc0, /* 011000001100 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x40, /* 011000000100 */
	0x61, 0x80, /* 011000011000 */
	0xfe, 0x00, /* 111111100000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 69 0x45 'E' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x7f, 0xc0, /* 011111111100 */
	0x30, 0x40, /* 001100000100 */
	0x30, 0x40, /* 001100000100 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x80, /* 001100001000 */
	0x3f, 0x80, /* 001111111000 */
	0x30, 0x80, /* 001100001000 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x20, /* 001100000010 */
	0x30, 0x20, /* 001100000010 */
	0x7f, 0xe0, /* 011111111110 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 70 0x46 'F' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x7f, 0xc0, /* 011111111100 */
	0x30, 0x40, /* 001100000100 */
	0x30, 0x40, /* 001100000100 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x80, /* 001100001000 */
	0x3f, 0x80, /* 001111111000 */
	0x30, 0x80, /* 001100001000 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x00, /* 001100000000 */
	0x78, 0x00, /* 011110000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 71 0x47 'G' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x0f, 0xc0, /* 000011111100 */
	0x10, 0x60, /* 000100000110 */
	0x20, 0x20, /* 001000000010 */
	0x20, 0x00, /* 001000000000 */
	0x60, 0x00, /* 011000000000 */
	0x60, 0x00, /* 011000000000 */
	0x60, 0x00, /* 011000000000 */
	0x60, 0x00, /* 011000000000 */
	0x61, 0xf0, /* 011000011111 */
	0x60, 0x60, /* 011000000110 */
	0x20, 0x60, /* 001000000110 */
	0x30, 0x60, /* 001100000110 */
	0x18, 0x60, /* 000110000110 */
	0x0f, 0x80, /* 000011111000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 72 0x48 'H' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0xf0, 0xf0, /* 111100001111 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x7f, 0xe0, /* 011111111110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0xf0, 0xf0, /* 111100001111 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 73 0x49 'I' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x1f, 0x80, /* 000111111000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x1f, 0x80, /* 000111111000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 74 0x4a 'J' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x1f, 0x80, /* 000111111000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x04, 0x00, /* 000001000000 */
	0x38, 0x00, /* 001110000000 */
	0x30, 0x00, /* 001100000000 */
	0x00, 0x00, /* 000000000000 */

	/* 75 0x4b 'K' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0xf0, 0xe0, /* 111100001110 */
	0x61, 0x80, /* 011000011000 */
	0x63, 0x00, /* 011000110000 */
	0x66, 0x00, /* 011001100000 */
	0x6c, 0x00, /* 011011000000 */
	0x78, 0x00, /* 011110000000 */
	0x78, 0x00, /* 011110000000 */
	0x7c, 0x00, /* 011111000000 */
	0x6e, 0x00, /* 011011100000 */
	0x67, 0x00, /* 011001110000 */
	0x63, 0x80, /* 011000111000 */
	0x61, 0xc0, /* 011000011100 */
	0x60, 0xe0, /* 011000001110 */
	0xf0, 0x70, /* 111100000111 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 76 0x4c 'L' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x78, 0x00, /* 011110000000 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x20, /* 001100000010 */
	0x30, 0x20, /* 001100000010 */
	0x7f, 0xe0, /* 011111111110 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 77 0x4d 'M' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0xe0, 0x70, /* 111000000111 */
	0x60, 0xe0, /* 011000001110 */
	0x70, 0xe0, /* 011100001110 */
	0x70, 0xe0, /* 011100001110 */
	0x70, 0xe0, /* 011100001110 */
	0x59, 0x60, /* 010110010110 */
	0x59, 0x60, /* 010110010110 */
	0x59, 0x60, /* 010110010110 */
	0x4d, 0x60, /* 010011010110 */
	0x4e, 0x60, /* 010011100110 */
	0x4e, 0x60, /* 010011100110 */
	0x44, 0x60, /* 010001000110 */
	0x44, 0x60, /* 010001000110 */
	0xe4, 0xf0, /* 111001001111 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 78 0x4e 'N' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0xc0, 0x70, /* 110000000111 */
	0x60, 0x20, /* 011000000010 */
	0x70, 0x20, /* 011100000010 */
	0x78, 0x20, /* 011110000010 */
	0x58, 0x20, /* 010110000010 */
	0x4c, 0x20, /* 010011000010 */
	0x46, 0x20, /* 010001100010 */
	0x47, 0x20, /* 010001110010 */
	0x43, 0x20, /* 010000110010 */
	0x41, 0xa0, /* 010000011010 */
	0x40, 0xe0, /* 010000001110 */
	0x40, 0xe0, /* 010000001110 */
	0x40, 0x60, /* 010000000110 */
	0xe0, 0x30, /* 111000000011 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 79 0x4f 'O' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x0f, 0x00, /* 000011110000 */
	0x11, 0xc0, /* 000100011100 */
	0x20, 0xc0, /* 001000001100 */
	0x20, 0x60, /* 001000000110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x20, 0x40, /* 001000000100 */
	0x30, 0x40, /* 001100000100 */
	0x18, 0x80, /* 000110001000 */
	0x0f, 0x00, /* 000011110000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 80 0x50 'P' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x7f, 0x80, /* 011111111000 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0x60, /* 001100000110 */
	0x30, 0x60, /* 001100000110 */
	0x30, 0x60, /* 001100000110 */
	0x30, 0xc0, /* 001100001100 */
	0x37, 0x80, /* 001101111000 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x00, /* 001100000000 */
	0x78, 0x00, /* 011110000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 81 0x51 'Q' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x0f, 0x00, /* 000011110000 */
	0x11, 0xc0, /* 000100011100 */
	0x20, 0xc0, /* 001000001100 */
	0x20, 0x60, /* 001000000110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x30, 0x40, /* 001100000100 */
	0x38, 0x40, /* 001110000100 */
	0x1f, 0x80, /* 000111111000 */
	0x0e, 0x00, /* 000011100000 */
	0x1f, 0x00, /* 000111110000 */
	0x23, 0x90, /* 001000111001 */
	0x01, 0xe0, /* 000000011110 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 82 0x52 'R' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0xff, 0x00, /* 111111110000 */
	0x61, 0x80, /* 011000011000 */
	0x60, 0xc0, /* 011000001100 */
	0x60, 0xc0, /* 011000001100 */
	0x60, 0xc0, /* 011000001100 */
	0x60, 0x80, /* 011000001000 */
	0x7f, 0x00, /* 011111110000 */
	0x7c, 0x00, /* 011111000000 */
	0x6e, 0x00, /* 011011100000 */
	0x67, 0x00, /* 011001110000 */
	0x63, 0x80, /* 011000111000 */
	0x61, 0xc0, /* 011000011100 */
	0x60, 0xe0, /* 011000001110 */
	0xf0, 0x70, /* 111100000111 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 83 0x53 'S' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x1f, 0xe0, /* 000111111110 */
	0x30, 0x60, /* 001100000110 */
	0x60, 0x20, /* 011000000010 */
	0x60, 0x20, /* 011000000010 */
	0x70, 0x00, /* 011100000000 */
	0x3c, 0x00, /* 001111000000 */
	0x1e, 0x00, /* 000111100000 */
	0x07, 0x80, /* 000001111000 */
	0x01, 0xc0, /* 000000011100 */
	0x00, 0xe0, /* 000000001110 */
	0x40, 0x60, /* 010000000110 */
	0x40, 0x60, /* 010000000110 */
	0x60, 0xc0, /* 011000001100 */
	0x7f, 0x80, /* 011111111000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 84 0x54 'T' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x7f, 0xe0, /* 011111111110 */
	0x46, 0x20, /* 010001100010 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x1f, 0x80, /* 000111111000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 85 0x55 'U' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0xf0, 0x70, /* 111100000111 */
	0x60, 0x20, /* 011000000010 */
	0x60, 0x20, /* 011000000010 */
	0x60, 0x20, /* 011000000010 */
	0x60, 0x20, /* 011000000010 */
	0x60, 0x20, /* 011000000010 */
	0x60, 0x20, /* 011000000010 */
	0x60, 0x20, /* 011000000010 */
	0x60, 0x20, /* 011000000010 */
	0x60, 0x20, /* 011000000010 */
	0x60, 0x20, /* 011000000010 */
	0x70, 0x40, /* 011100000100 */
	0x3f, 0xc0, /* 001111111100 */
	0x1f, 0x80, /* 000111111000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 86 0x56 'V' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0xe0, 0xe0, /* 111000001110 */
	0x60, 0x40, /* 011000000100 */
	0x30, 0x80, /* 001100001000 */
	0x30, 0x80, /* 001100001000 */
	0x30, 0x80, /* 001100001000 */
	0x19, 0x00, /* 000110010000 */
	0x19, 0x00, /* 000110010000 */
	0x19, 0x00, /* 000110010000 */
	0x0a, 0x00, /* 000010100000 */
	0x0e, 0x00, /* 000011100000 */
	0x0e, 0x00, /* 000011100000 */
	0x04, 0x00, /* 000001000000 */
	0x04, 0x00, /* 000001000000 */
	0x04, 0x00, /* 000001000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 87 0x57 'W' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0xfe, 0xf0, /* 111111101111 */
	0x66, 0x20, /* 011001100010 */
	0x66, 0x20, /* 011001100010 */
	0x66, 0x20, /* 011001100010 */
	0x76, 0x20, /* 011101100010 */
	0x77, 0x40, /* 011101110100 */
	0x33, 0x40, /* 001100110100 */
	0x37, 0x40, /* 001101110100 */
	0x3b, 0xc0, /* 001110111100 */
	0x3b, 0x80, /* 001110111000 */
	0x19, 0x80, /* 000110011000 */
	0x19, 0x80, /* 000110011000 */
	0x19, 0x80, /* 000110011000 */
	0x19, 0x80, /* 000110011000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 88 0x58 'X' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0xf0, 0x70, /* 111100000111 */
	0x60, 0x20, /* 011000000010 */
	0x30, 0x40, /* 001100000100 */
	0x38, 0x80, /* 001110001000 */
	0x18, 0x80, /* 000110001000 */
	0x0d, 0x00, /* 000011010000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x0b, 0x00, /* 000010110000 */
	0x11, 0x80, /* 000100011000 */
	0x11, 0xc0, /* 000100011100 */
	0x20, 0xc0, /* 001000001100 */
	0x40, 0x60, /* 010000000110 */
	0xe0, 0xf0, /* 111000001111 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 89 0x59 'Y' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0xf0, 0x70, /* 111100000111 */
	0x60, 0x20, /* 011000000010 */
	0x30, 0x40, /* 001100000100 */
	0x18, 0x80, /* 000110001000 */
	0x18, 0x80, /* 000110001000 */
	0x0d, 0x00, /* 000011010000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x0f, 0x00, /* 000011110000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 90 0x5a 'Z' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x3f, 0xe0, /* 001111111110 */
	0x20, 0xc0, /* 001000001100 */
	0x00, 0xc0, /* 000000001100 */
	0x01, 0x80, /* 000000011000 */
	0x01, 0x80, /* 000000011000 */
	0x03, 0x00, /* 000000110000 */
	0x03, 0x00, /* 000000110000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x18, 0x00, /* 000110000000 */
	0x18, 0x20, /* 000110000010 */
	0x3f, 0xe0, /* 001111111110 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 91 0x5b '[' */
	0x00, 0x00, /* 000000000000 */
	0x0f, 0x80, /* 000011111000 */
	0x0f, 0x80, /* 000011111000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x0f, 0x80, /* 000011111000 */
	0x0f, 0x80, /* 000011111000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 92 0x5c '\' */
	0x00, 0x00, /* 000000000000 */
	0x60, 0x00, /* 011000000000 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x00, /* 001100000000 */
	0x18, 0x00, /* 000110000000 */
	0x18, 0x00, /* 000110000000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x03, 0x00, /* 000000110000 */
	0x03, 0x00, /* 000000110000 */
	0x01, 0x80, /* 000000011000 */
	0x01, 0x80, /* 000000011000 */
	0x00, 0xc0, /* 000000001100 */
	0x00, 0xc0, /* 000000001100 */
	0x00, 0x60, /* 000000000110 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 93 0x5d ']' */
	0x00, 0x00, /* 000000000000 */
	0x1f, 0x00, /* 000111110000 */
	0x1f, 0x00, /* 000111110000 */
	0x03, 0x00, /* 000000110000 */
	0x03, 0x00, /* 000000110000 */
	0x03, 0x00, /* 000000110000 */
	0x03, 0x00, /* 000000110000 */
	0x03, 0x00, /* 000000110000 */
	0x03, 0x00, /* 000000110000 */
	0x03, 0x00, /* 000000110000 */
	0x03, 0x00, /* 000000110000 */
	0x03, 0x00, /* 000000110000 */
	0x03, 0x00, /* 000000110000 */
	0x03, 0x00, /* 000000110000 */
	0x1f, 0x00, /* 000111110000 */
	0x1f, 0x00, /* 000111110000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 94 0x5e '^' */
	0x00, 0x00, /* 000000000000 */
	0x04, 0x00, /* 000001000000 */
	0x0e, 0x00, /* 000011100000 */
	0x1b, 0x00, /* 000110110000 */
	0x31, 0x80, /* 001100011000 */
	0x60, 0xc0, /* 011000001100 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 95 0x5f '_' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 96 0x60 '`' */
	0x00, 0x00, /* 000000000000 */
	0x01, 0x00, /* 000000010000 */
	0x03, 0x00, /* 000000110000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x07, 0x80, /* 000001111000 */
	0x07, 0x80, /* 000001111000 */
	0x03, 0x00, /* 000000110000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 97 0x61 'a' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x0f, 0x80, /* 000011111000 */
	0x18, 0xc0, /* 000110001100 */
	0x10, 0xc0, /* 000100001100 */
	0x03, 0xc0, /* 000000111100 */
	0x1c, 0xc0, /* 000111001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x39, 0xc0, /* 001110011100 */
	0x1e, 0xe0, /* 000111101110 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 98 0x62 'b' */
	0x00, 0x00, /* 000000000000 */
	0x20, 0x00, /* 001000000000 */
	0x60, 0x00, /* 011000000000 */
	0xe0, 0x00, /* 111000000000 */
	0x60, 0x00, /* 011000000000 */
	0x60, 0x00, /* 011000000000 */
	0x67, 0x80, /* 011001111000 */
	0x6f, 0xc0, /* 011011111100 */
	0x70, 0xe0, /* 011100001110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x70, 0x60, /* 011100000110 */
	0x78, 0xc0, /* 011110001100 */
	0x4f, 0x80, /* 010011111000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 99 0x63 'c' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x1f, 0x80, /* 000111111000 */
	0x31, 0xc0, /* 001100011100 */
	0x20, 0xc0, /* 001000001100 */
	0x60, 0x00, /* 011000000000 */
	0x60, 0x00, /* 011000000000 */
	0x60, 0x00, /* 011000000000 */
	0x60, 0x00, /* 011000000000 */
	0x70, 0x40, /* 011100000100 */
	0x30, 0xc0, /* 001100001100 */
	0x1f, 0x80, /* 000111111000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 100 0x64 'd' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x60, /* 000000000110 */
	0x00, 0xe0, /* 000000001110 */
	0x00, 0x60, /* 000000000110 */
	0x00, 0x60, /* 000000000110 */
	0x00, 0x60, /* 000000000110 */
	0x0f, 0x60, /* 000011110110 */
	0x31, 0xe0, /* 001100011110 */
	0x20, 0xe0, /* 001000001110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x70, 0xe0, /* 011100001110 */
	0x39, 0x60, /* 001110010110 */
	0x1e, 0x70, /* 000111100111 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 101 0x65 'e' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x0f, 0x00, /* 000011110000 */
	0x30, 0xc0, /* 001100001100 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x7f, 0xe0, /* 011111111110 */
	0x60, 0x00, /* 011000000000 */
	0x60, 0x00, /* 011000000000 */
	0x30, 0x00, /* 001100000000 */
	0x18, 0x60, /* 000110000110 */
	0x0f, 0x80, /* 000011111000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 102 0x66 'f' */
	0x00, 0x00, /* 000000000000 */
	0x03, 0x80, /* 000000111000 */
	0x04, 0xc0, /* 000001001100 */
	0x04, 0xc0, /* 000001001100 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x3f, 0x80, /* 001111111000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x1e, 0x00, /* 000111100000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 103 0x67 'g' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x1f, 0x20, /* 000111110010 */
	0x31, 0xe0, /* 001100011110 */
	0x60, 0xc0, /* 011000001100 */
	0x60, 0xc0, /* 011000001100 */
	0x60, 0xc0, /* 011000001100 */
	0x31, 0x80, /* 001100011000 */
	0x3f, 0x00, /* 001111110000 */
	0x60, 0x00, /* 011000000000 */
	0x7f, 0xc0, /* 011111111100 */
	0x3f, 0xe0, /* 001111111110 */
	0x20, 0x60, /* 001000000110 */
	0x40, 0x20, /* 010000000010 */
	0x40, 0x20, /* 010000000010 */
	0x7f, 0xc0, /* 011111111100 */
	0x3f, 0x80, /* 001111111000 */
	0x00, 0x00, /* 000000000000 */

	/* 104 0x68 'h' */
	0x00, 0x00, /* 000000000000 */
	0x10, 0x00, /* 000100000000 */
	0x30, 0x00, /* 001100000000 */
	0x70, 0x00, /* 011100000000 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x00, /* 001100000000 */
	0x37, 0x80, /* 001101111000 */
	0x39, 0xc0, /* 001110011100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x79, 0xe0, /* 011110011110 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 105 0x69 'i' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x1e, 0x00, /* 000111100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x1f, 0x80, /* 000111111000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 106 0x6a 'j' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0xc0, /* 000000001100 */
	0x00, 0xc0, /* 000000001100 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x03, 0xc0, /* 000000111100 */
	0x00, 0xc0, /* 000000001100 */
	0x00, 0xc0, /* 000000001100 */
	0x00, 0xc0, /* 000000001100 */
	0x00, 0xc0, /* 000000001100 */
	0x00, 0xc0, /* 000000001100 */
	0x00, 0xc0, /* 000000001100 */
	0x00, 0xc0, /* 000000001100 */
	0x00, 0xc0, /* 000000001100 */
	0x00, 0xc0, /* 000000001100 */
	0x20, 0xc0, /* 001000001100 */
	0x30, 0xc0, /* 001100001100 */
	0x38, 0x80, /* 001110001000 */
	0x1f, 0x00, /* 000111110000 */
	0x0e, 0x00, /* 000011100000 */
	0x00, 0x00, /* 000000000000 */

	/* 107 0x6b 'k' */
	0x00, 0x00, /* 000000000000 */
	0x60, 0x00, /* 011000000000 */
	0xe0, 0x00, /* 111000000000 */
	0x60, 0x00, /* 011000000000 */
	0x60, 0x00, /* 011000000000 */
	0x60, 0x00, /* 011000000000 */
	0x61, 0xc0, /* 011000011100 */
	0x63, 0x00, /* 011000110000 */
	0x66, 0x00, /* 011001100000 */
	0x7c, 0x00, /* 011111000000 */
	0x78, 0x00, /* 011110000000 */
	0x7c, 0x00, /* 011111000000 */
	0x6e, 0x00, /* 011011100000 */
	0x67, 0x00, /* 011001110000 */
	0x63, 0x80, /* 011000111000 */
	0xf1, 0xe0, /* 111100011110 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 108 0x6c 'l' */
	0x00, 0x00, /* 000000000000 */
	0x1e, 0x00, /* 000111100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x1f, 0x80, /* 000111111000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 109 0x6d 'm' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0xdd, 0xc0, /* 110111011100 */
	0x6e, 0xe0, /* 011011101110 */
	0x66, 0x60, /* 011001100110 */
	0x66, 0x60, /* 011001100110 */
	0x66, 0x60, /* 011001100110 */
	0x66, 0x60, /* 011001100110 */
	0x66, 0x60, /* 011001100110 */
	0x66, 0x60, /* 011001100110 */
	0x66, 0x60, /* 011001100110 */
	0xef, 0x70, /* 111011110111 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 110 0x6e 'n' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x27, 0x80, /* 001001111000 */
	0x79, 0xc0, /* 011110011100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x79, 0xe0, /* 011110011110 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 111 0x6f 'o' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x0f, 0x80, /* 000011111000 */
	0x11, 0xc0, /* 000100011100 */
	0x20, 0xe0, /* 001000001110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x70, 0x40, /* 011100000100 */
	0x38, 0x80, /* 001110001000 */
	0x1f, 0x00, /* 000111110000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 112 0x70 'p' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0xef, 0x80, /* 111011111000 */
	0x71, 0xc0, /* 011100011100 */
	0x60, 0xe0, /* 011000001110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x40, /* 011000000100 */
	0x70, 0x80, /* 011100001000 */
	0x7f, 0x00, /* 011111110000 */
	0x60, 0x00, /* 011000000000 */
	0x60, 0x00, /* 011000000000 */
	0x60, 0x00, /* 011000000000 */
	0x60, 0x00, /* 011000000000 */
	0xf0, 0x00, /* 111100000000 */
	0x00, 0x00, /* 000000000000 */

	/* 113 0x71 'q' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x0f, 0x20, /* 000011110010 */
	0x11, 0xe0, /* 000100011110 */
	0x20, 0xe0, /* 001000001110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x70, 0x60, /* 011100000110 */
	0x38, 0xe0, /* 001110001110 */
	0x1f, 0xe0, /* 000111111110 */
	0x00, 0x60, /* 000000000110 */
	0x00, 0x60, /* 000000000110 */
	0x00, 0x60, /* 000000000110 */
	0x00, 0x60, /* 000000000110 */
	0x00, 0xf0, /* 000000001111 */
	0x00, 0x00, /* 000000000000 */

	/* 114 0x72 'r' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x73, 0x80, /* 011100111000 */
	0x34, 0xc0, /* 001101001100 */
	0x38, 0xc0, /* 001110001100 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x00, /* 001100000000 */
	0x78, 0x00, /* 011110000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 115 0x73 's' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x1f, 0xc0, /* 000111111100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0x40, /* 001100000100 */
	0x38, 0x00, /* 001110000000 */
	0x1e, 0x00, /* 000111100000 */
	0x07, 0x80, /* 000001111000 */
	0x01, 0xc0, /* 000000011100 */
	0x20, 0xc0, /* 001000001100 */
	0x30, 0xc0, /* 001100001100 */
	0x3f, 0x80, /* 001111111000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 116 0x74 't' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x04, 0x00, /* 000001000000 */
	0x04, 0x00, /* 000001000000 */
	0x0c, 0x00, /* 000011000000 */
	0x7f, 0xc0, /* 011111111100 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x20, /* 000011000010 */
	0x0e, 0x40, /* 000011100100 */
	0x07, 0x80, /* 000001111000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 117 0x75 'u' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x79, 0xe0, /* 011110011110 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x39, 0xc0, /* 001110011100 */
	0x1e, 0x60, /* 000111100110 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 118 0x76 'v' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0xf0, 0x70, /* 111100000111 */
	0x60, 0x20, /* 011000000010 */
	0x30, 0x40, /* 001100000100 */
	0x30, 0x40, /* 001100000100 */
	0x18, 0x80, /* 000110001000 */
	0x18, 0x80, /* 000110001000 */
	0x0d, 0x00, /* 000011010000 */
	0x0d, 0x00, /* 000011010000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 119 0x77 'w' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0xff, 0x70, /* 111111110111 */
	0x66, 0x20, /* 011001100010 */
	0x66, 0x20, /* 011001100010 */
	0x66, 0x20, /* 011001100010 */
	0x37, 0x40, /* 001101110100 */
	0x3b, 0x40, /* 001110110100 */
	0x3b, 0x40, /* 001110110100 */
	0x19, 0x80, /* 000110011000 */
	0x19, 0x80, /* 000110011000 */
	0x19, 0x80, /* 000110011000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 120 0x78 'x' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0xf8, 0xf0, /* 111110001111 */
	0x70, 0x40, /* 011100000100 */
	0x38, 0x80, /* 001110001000 */
	0x1d, 0x00, /* 000111010000 */
	0x0e, 0x00, /* 000011100000 */
	0x07, 0x00, /* 000001110000 */
	0x0b, 0x80, /* 000010111000 */
	0x11, 0xc0, /* 000100011100 */
	0x20, 0xe0, /* 001000001110 */
	0xf1, 0xf0, /* 111100011111 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 121 0x79 'y' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0xf0, 0xf0, /* 111100001111 */
	0x60, 0x20, /* 011000000010 */
	0x30, 0x40, /* 001100000100 */
	0x30, 0x40, /* 001100000100 */
	0x18, 0x80, /* 000110001000 */
	0x18, 0x80, /* 000110001000 */
	0x0d, 0x00, /* 000011010000 */
	0x0d, 0x00, /* 000011010000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x04, 0x00, /* 000001000000 */
	0x0c, 0x00, /* 000011000000 */
	0x08, 0x00, /* 000010000000 */
	0x78, 0x00, /* 011110000000 */
	0x70, 0x00, /* 011100000000 */
	0x00, 0x00, /* 000000000000 */

	/* 122 0x7a 'z' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x7f, 0xe0, /* 011111111110 */
	0x60, 0xe0, /* 011000001110 */
	0x41, 0xc0, /* 010000011100 */
	0x03, 0x80, /* 000000111000 */
	0x07, 0x00, /* 000001110000 */
	0x0e, 0x00, /* 000011100000 */
	0x1c, 0x00, /* 000111000000 */
	0x38, 0x20, /* 001110000010 */
	0x70, 0x60, /* 011100000110 */
	0x7f, 0xe0, /* 011111111110 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 123 0x7b '{' */
	0x00, 0x00, /* 000000000000 */
	0x03, 0x80, /* 000000111000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x0c, 0x00, /* 000011000000 */
	0x38, 0x00, /* 001110000000 */
	0x0c, 0x00, /* 000011000000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x03, 0x80, /* 000000111000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 124 0x7c '|' */
	0x00, 0x00, /* 000000000000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x00, 0x00, /* 000000000000 */

	/* 125 0x7d '}' */
	0x00, 0x00, /* 000000000000 */
	0x1c, 0x00, /* 000111000000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x03, 0x00, /* 000000110000 */
	0x01, 0xc0, /* 000000011100 */
	0x03, 0x00, /* 000000110000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x1c, 0x00, /* 000111000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 126 0x7e '~' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x1c, 0x20, /* 000111000010 */
	0x3e, 0x60, /* 001111100110 */
	0x67, 0xc0, /* 011001111100 */
	0x43, 0x80, /* 010000111000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 127 0x7f '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0x00, 0x00, /* 000000000000 */

	/* 128 0x80 '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x0f, 0xc0, /* 000011111100 */
	0x10, 0x60, /* 000100000110 */
	0x20, 0x20, /* 001000000010 */
	0x20, 0x00, /* 001000000000 */
	0x60, 0x00, /* 011000000000 */
	0x60, 0x00, /* 011000000000 */
	0x60, 0x00, /* 011000000000 */
	0x60, 0x00, /* 011000000000 */
	0x60, 0x00, /* 011000000000 */
	0x60, 0x00, /* 011000000000 */
	0x20, 0x00, /* 001000000000 */
	0x30, 0x20, /* 001100000010 */
	0x18, 0x40, /* 000110000100 */
	0x0f, 0x80, /* 000011111000 */
	0x06, 0x00, /* 000001100000 */
	0x03, 0x00, /* 000000110000 */
	0x01, 0x80, /* 000000011000 */
	0x0f, 0x00, /* 000011110000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 129 0x81 '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x19, 0x80, /* 000110011000 */
	0x19, 0x80, /* 000110011000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x79, 0xe0, /* 011110011110 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x39, 0xc0, /* 001110011100 */
	0x1e, 0x60, /* 000111100110 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 130 0x82 '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x01, 0x80, /* 000000011000 */
	0x03, 0x00, /* 000000110000 */
	0x06, 0x00, /* 000001100000 */
	0x00, 0x00, /* 000000000000 */
	0x0f, 0x00, /* 000011110000 */
	0x30, 0xc0, /* 001100001100 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x7f, 0xe0, /* 011111111110 */
	0x60, 0x00, /* 011000000000 */
	0x60, 0x00, /* 011000000000 */
	0x30, 0x00, /* 001100000000 */
	0x18, 0x60, /* 000110000110 */
	0x0f, 0x80, /* 000011111000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 131 0x83 '.' */
	0x00, 0x00, /* 000000000000 */
	0x02, 0x00, /* 000000100000 */
	0x07, 0x00, /* 000001110000 */
	0x0d, 0x80, /* 000011011000 */
	0x18, 0xc0, /* 000110001100 */
	0x00, 0x00, /* 000000000000 */
	0x0f, 0x80, /* 000011111000 */
	0x18, 0xc0, /* 000110001100 */
	0x10, 0xc0, /* 000100001100 */
	0x03, 0xc0, /* 000000111100 */
	0x1c, 0xc0, /* 000111001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x39, 0xc0, /* 001110011100 */
	0x1e, 0xe0, /* 000111101110 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 132 0x84 '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x19, 0x80, /* 000110011000 */
	0x19, 0x80, /* 000110011000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x0f, 0x80, /* 000011111000 */
	0x18, 0xc0, /* 000110001100 */
	0x10, 0xc0, /* 000100001100 */
	0x03, 0xc0, /* 000000111100 */
	0x1c, 0xc0, /* 000111001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x39, 0xc0, /* 001110011100 */
	0x1e, 0xe0, /* 000111101110 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 133 0x85 '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x0c, 0x00, /* 000011000000 */
	0x06, 0x00, /* 000001100000 */
	0x03, 0x00, /* 000000110000 */
	0x00, 0x00, /* 000000000000 */
	0x0f, 0x80, /* 000011111000 */
	0x18, 0xc0, /* 000110001100 */
	0x10, 0xc0, /* 000100001100 */
	0x03, 0xc0, /* 000000111100 */
	0x1c, 0xc0, /* 000111001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x39, 0xc0, /* 001110011100 */
	0x1e, 0xe0, /* 000111101110 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 134 0x86 '.' */
	0x00, 0x00, /* 000000000000 */
	0x07, 0x00, /* 000001110000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x07, 0x00, /* 000001110000 */
	0x00, 0x00, /* 000000000000 */
	0x0f, 0x80, /* 000011111000 */
	0x18, 0xc0, /* 000110001100 */
	0x10, 0xc0, /* 000100001100 */
	0x03, 0xc0, /* 000000111100 */
	0x1c, 0xc0, /* 000111001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x39, 0xc0, /* 001110011100 */
	0x1e, 0xe0, /* 000111101110 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 135 0x87 '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x1f, 0x80, /* 000111111000 */
	0x31, 0xc0, /* 001100011100 */
	0x20, 0xc0, /* 001000001100 */
	0x60, 0x00, /* 011000000000 */
	0x60, 0x00, /* 011000000000 */
	0x60, 0x00, /* 011000000000 */
	0x60, 0x00, /* 011000000000 */
	0x70, 0x40, /* 011100000100 */
	0x30, 0xc0, /* 001100001100 */
	0x1f, 0x80, /* 000111111000 */
	0x06, 0x00, /* 000001100000 */
	0x03, 0x00, /* 000000110000 */
	0x01, 0x80, /* 000000011000 */
	0x0f, 0x00, /* 000011110000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 136 0x88 '.' */
	0x00, 0x00, /* 000000000000 */
	0x02, 0x00, /* 000000100000 */
	0x07, 0x00, /* 000001110000 */
	0x0d, 0x80, /* 000011011000 */
	0x18, 0xc0, /* 000110001100 */
	0x00, 0x00, /* 000000000000 */
	0x0f, 0x00, /* 000011110000 */
	0x30, 0xc0, /* 001100001100 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x7f, 0xe0, /* 011111111110 */
	0x60, 0x00, /* 011000000000 */
	0x60, 0x00, /* 011000000000 */
	0x30, 0x00, /* 001100000000 */
	0x18, 0x60, /* 000110000110 */
	0x0f, 0x80, /* 000011111000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 137 0x89 '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x19, 0x80, /* 000110011000 */
	0x19, 0x80, /* 000110011000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x0f, 0x00, /* 000011110000 */
	0x30, 0xc0, /* 001100001100 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x7f, 0xe0, /* 011111111110 */
	0x60, 0x00, /* 011000000000 */
	0x60, 0x00, /* 011000000000 */
	0x30, 0x00, /* 001100000000 */
	0x18, 0x60, /* 000110000110 */
	0x0f, 0x80, /* 000011111000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 138 0x8a '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x0c, 0x00, /* 000011000000 */
	0x06, 0x00, /* 000001100000 */
	0x03, 0x00, /* 000000110000 */
	0x00, 0x00, /* 000000000000 */
	0x0f, 0x00, /* 000011110000 */
	0x30, 0xc0, /* 001100001100 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x7f, 0xe0, /* 011111111110 */
	0x60, 0x00, /* 011000000000 */
	0x60, 0x00, /* 011000000000 */
	0x30, 0x00, /* 001100000000 */
	0x18, 0x60, /* 000110000110 */
	0x0f, 0x80, /* 000011111000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 139 0x8b '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x19, 0x80, /* 000110011000 */
	0x19, 0x80, /* 000110011000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x1e, 0x00, /* 000111100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x1f, 0x80, /* 000111111000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 140 0x8c '.' */
	0x00, 0x00, /* 000000000000 */
	0x04, 0x00, /* 000001000000 */
	0x0e, 0x00, /* 000011100000 */
	0x1b, 0x00, /* 000110110000 */
	0x31, 0x80, /* 001100011000 */
	0x00, 0x00, /* 000000000000 */
	0x1e, 0x00, /* 000111100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x1f, 0x80, /* 000111111000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 141 0x8d '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x18, 0x00, /* 000110000000 */
	0x0c, 0x00, /* 000011000000 */
	0x06, 0x00, /* 000001100000 */
	0x00, 0x00, /* 000000000000 */
	0x1e, 0x00, /* 000111100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x1f, 0x80, /* 000111111000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 142 0x8e '.' */
	0x00, 0x00, /* 000000000000 */
	0x19, 0x80, /* 000110011000 */
	0x19, 0x80, /* 000110011000 */
	0x00, 0x00, /* 000000000000 */
	0x04, 0x00, /* 000001000000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x0b, 0x00, /* 000010110000 */
	0x0b, 0x00, /* 000010110000 */
	0x19, 0x80, /* 000110011000 */
	0x11, 0x80, /* 000100011000 */
	0x3f, 0xc0, /* 001111111100 */
	0x20, 0xc0, /* 001000001100 */
	0x60, 0x60, /* 011000000110 */
	0x40, 0x60, /* 010000000110 */
	0xe0, 0xf0, /* 111000001111 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 143 0x8f '.' */
	0x00, 0x00, /* 000000000000 */
	0x0f, 0x00, /* 000011110000 */
	0x19, 0x80, /* 000110011000 */
	0x0f, 0x00, /* 000011110000 */
	0x04, 0x00, /* 000001000000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x0b, 0x00, /* 000010110000 */
	0x0b, 0x00, /* 000010110000 */
	0x19, 0x80, /* 000110011000 */
	0x11, 0x80, /* 000100011000 */
	0x3f, 0xc0, /* 001111111100 */
	0x20, 0xc0, /* 001000001100 */
	0x60, 0x60, /* 011000000110 */
	0x40, 0x60, /* 010000000110 */
	0xe0, 0xf0, /* 111000001111 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 144 0x90 '.' */
	0x00, 0x00, /* 000000000000 */
	0x03, 0x00, /* 000000110000 */
	0x06, 0x00, /* 000001100000 */
	0x08, 0x00, /* 000010000000 */
	0x7f, 0xe0, /* 011111111110 */
	0x30, 0x20, /* 001100000010 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x80, /* 001100001000 */
	0x3f, 0x80, /* 001111111000 */
	0x30, 0x80, /* 001100001000 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x20, /* 001100000010 */
	0x30, 0x20, /* 001100000010 */
	0x7f, 0xe0, /* 011111111110 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 145 0x91 '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x3d, 0xe0, /* 001111011110 */
	0x66, 0x30, /* 011001100011 */
	0x46, 0x30, /* 010001100011 */
	0x06, 0x30, /* 000001100011 */
	0x3f, 0xf0, /* 001111111111 */
	0x66, 0x00, /* 011001100000 */
	0xc6, 0x00, /* 110001100000 */
	0xc6, 0x00, /* 110001100000 */
	0xe7, 0x30, /* 111001110011 */
	0x7d, 0xe0, /* 011111011110 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 146 0x92 '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x03, 0xf0, /* 000000111111 */
	0x07, 0x10, /* 000001110001 */
	0x07, 0x10, /* 000001110001 */
	0x0b, 0x00, /* 000010110000 */
	0x0b, 0x00, /* 000010110000 */
	0x0b, 0x20, /* 000010110010 */
	0x13, 0xe0, /* 000100111110 */
	0x13, 0x20, /* 000100110010 */
	0x3f, 0x00, /* 001111110000 */
	0x23, 0x00, /* 001000110000 */
	0x23, 0x00, /* 001000110000 */
	0x43, 0x10, /* 010000110001 */
	0x43, 0x10, /* 010000110001 */
	0xe7, 0xf0, /* 111001111111 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 147 0x93 '.' */
	0x00, 0x00, /* 000000000000 */
	0x02, 0x00, /* 000000100000 */
	0x07, 0x00, /* 000001110000 */
	0x0d, 0x80, /* 000011011000 */
	0x18, 0xc0, /* 000110001100 */
	0x00, 0x00, /* 000000000000 */
	0x0f, 0x80, /* 000011111000 */
	0x11, 0xc0, /* 000100011100 */
	0x20, 0xe0, /* 001000001110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x70, 0x40, /* 011100000100 */
	0x38, 0x80, /* 001110001000 */
	0x1f, 0x00, /* 000111110000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 148 0x94 '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x19, 0x80, /* 000110011000 */
	0x19, 0x80, /* 000110011000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x0f, 0x80, /* 000011111000 */
	0x11, 0xc0, /* 000100011100 */
	0x20, 0xe0, /* 001000001110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x70, 0x40, /* 011100000100 */
	0x38, 0x80, /* 001110001000 */
	0x1f, 0x00, /* 000111110000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 149 0x95 '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x0c, 0x00, /* 000011000000 */
	0x06, 0x00, /* 000001100000 */
	0x03, 0x00, /* 000000110000 */
	0x00, 0x00, /* 000000000000 */
	0x0f, 0x80, /* 000011111000 */
	0x11, 0xc0, /* 000100011100 */
	0x20, 0xe0, /* 001000001110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x70, 0x40, /* 011100000100 */
	0x38, 0x80, /* 001110001000 */
	0x1f, 0x00, /* 000111110000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 150 0x96 '.' */
	0x00, 0x00, /* 000000000000 */
	0x02, 0x00, /* 000000100000 */
	0x07, 0x00, /* 000001110000 */
	0x0d, 0x80, /* 000011011000 */
	0x18, 0xc0, /* 000110001100 */
	0x00, 0x00, /* 000000000000 */
	0x79, 0xe0, /* 011110011110 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x39, 0xc0, /* 001110011100 */
	0x1e, 0x60, /* 000111100110 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 151 0x97 '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x18, 0x00, /* 000110000000 */
	0x0c, 0x00, /* 000011000000 */
	0x06, 0x00, /* 000001100000 */
	0x00, 0x00, /* 000000000000 */
	0x79, 0xe0, /* 011110011110 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x39, 0xc0, /* 001110011100 */
	0x1e, 0x60, /* 000111100110 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 152 0x98 '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x19, 0x80, /* 000110011000 */
	0x19, 0x80, /* 000110011000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0xf0, 0xf0, /* 111100001111 */
	0x60, 0x20, /* 011000000010 */
	0x30, 0x40, /* 001100000100 */
	0x30, 0x40, /* 001100000100 */
	0x18, 0x80, /* 000110001000 */
	0x18, 0x80, /* 000110001000 */
	0x0d, 0x00, /* 000011010000 */
	0x0d, 0x00, /* 000011010000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x04, 0x00, /* 000001000000 */
	0x0c, 0x00, /* 000011000000 */
	0x08, 0x00, /* 000010000000 */
	0x78, 0x00, /* 011110000000 */
	0x70, 0x00, /* 011100000000 */
	0x00, 0x00, /* 000000000000 */

	/* 153 0x99 '.' */
	0x00, 0x00, /* 000000000000 */
	0x19, 0x80, /* 000110011000 */
	0x19, 0x80, /* 000110011000 */
	0x00, 0x00, /* 000000000000 */
	0x0f, 0x80, /* 000011111000 */
	0x11, 0xc0, /* 000100011100 */
	0x20, 0xc0, /* 001000001100 */
	0x20, 0x60, /* 001000000110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x20, 0x40, /* 001000000100 */
	0x30, 0x40, /* 001100000100 */
	0x18, 0x80, /* 000110001000 */
	0x0f, 0x00, /* 000011110000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 154 0x9a '.' */
	0x00, 0x00, /* 000000000000 */
	0x19, 0x80, /* 000110011000 */
	0x19, 0x80, /* 000110011000 */
	0xe0, 0x30, /* 111000000011 */
	0x60, 0x20, /* 011000000010 */
	0x60, 0x20, /* 011000000010 */
	0x60, 0x20, /* 011000000010 */
	0x60, 0x20, /* 011000000010 */
	0x60, 0x20, /* 011000000010 */
	0x60, 0x20, /* 011000000010 */
	0x60, 0x20, /* 011000000010 */
	0x60, 0x20, /* 011000000010 */
	0x60, 0x20, /* 011000000010 */
	0x70, 0x40, /* 011100000100 */
	0x3f, 0xc0, /* 001111111100 */
	0x1f, 0x80, /* 000111111000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 155 0x9b '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x1f, 0x80, /* 000111111000 */
	0x36, 0xc0, /* 001101101100 */
	0x26, 0xc0, /* 001001101100 */
	0x66, 0x00, /* 011001100000 */
	0x66, 0x00, /* 011001100000 */
	0x66, 0x00, /* 011001100000 */
	0x66, 0x00, /* 011001100000 */
	0x76, 0x40, /* 011101100100 */
	0x36, 0xc0, /* 001101101100 */
	0x1f, 0x80, /* 000111111000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 156 0x9c '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x0f, 0x80, /* 000011111000 */
	0x1c, 0xc0, /* 000111001100 */
	0x18, 0xc0, /* 000110001100 */
	0x18, 0x00, /* 000110000000 */
	0x18, 0x00, /* 000110000000 */
	0x18, 0x00, /* 000110000000 */
	0x7e, 0x00, /* 011111100000 */
	0x7e, 0x00, /* 011111100000 */
	0x18, 0x00, /* 000110000000 */
	0x18, 0x00, /* 000110000000 */
	0x18, 0x00, /* 000110000000 */
	0x18, 0x00, /* 000110000000 */
	0x3e, 0x20, /* 001111100010 */
	0x7f, 0xe0, /* 011111111110 */
	0x61, 0xc0, /* 011000011100 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 157 0x9d '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x30, 0xc0, /* 001100001100 */
	0x19, 0x80, /* 000110011000 */
	0x19, 0x80, /* 000110011000 */
	0x0f, 0x00, /* 000011110000 */
	0x06, 0x00, /* 000001100000 */
	0x1f, 0x80, /* 000111111000 */
	0x06, 0x00, /* 000001100000 */
	0x1f, 0x80, /* 000111111000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 158 0x9e '.' */
	0x00, 0x00, /* 000000000000 */
	0x7f, 0x80, /* 011111111000 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0x60, /* 001100000110 */
	0x30, 0x60, /* 001100000110 */
	0x30, 0x60, /* 001100000110 */
	0x30, 0xc0, /* 001100001100 */
	0x37, 0x80, /* 001101111000 */
	0x30, 0x00, /* 001100000000 */
	0x33, 0x00, /* 001100110000 */
	0x37, 0x80, /* 001101111000 */
	0x33, 0x00, /* 001100110000 */
	0x33, 0x00, /* 001100110000 */
	0x33, 0x30, /* 001100110011 */
	0x31, 0xe0, /* 001100011110 */
	0x78, 0xc0, /* 011110001100 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 159 0x9f '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0xc0, /* 000000001100 */
	0x01, 0xe0, /* 000000011110 */
	0x03, 0x30, /* 000000110011 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x3f, 0xe0, /* 001111111110 */
	0x7f, 0xc0, /* 011111111100 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0xcc, 0x00, /* 110011000000 */
	0x78, 0x00, /* 011110000000 */
	0x30, 0x00, /* 001100000000 */
	0x00, 0x00, /* 000000000000 */

	/* 160 0xa0 '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x01, 0x80, /* 000000011000 */
	0x03, 0x00, /* 000000110000 */
	0x06, 0x00, /* 000001100000 */
	0x00, 0x00, /* 000000000000 */
	0x0f, 0x80, /* 000011111000 */
	0x18, 0xc0, /* 000110001100 */
	0x10, 0xc0, /* 000100001100 */
	0x03, 0xc0, /* 000000111100 */
	0x1c, 0xc0, /* 000111001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x39, 0xc0, /* 001110011100 */
	0x1e, 0xe0, /* 000111101110 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 161 0xa1 '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x01, 0x80, /* 000000011000 */
	0x03, 0x00, /* 000000110000 */
	0x06, 0x00, /* 000001100000 */
	0x00, 0x00, /* 000000000000 */
	0x1e, 0x00, /* 000111100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x1f, 0x80, /* 000111111000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 162 0xa2 '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x01, 0x80, /* 000000011000 */
	0x03, 0x00, /* 000000110000 */
	0x06, 0x00, /* 000001100000 */
	0x00, 0x00, /* 000000000000 */
	0x0f, 0x80, /* 000011111000 */
	0x11, 0xc0, /* 000100011100 */
	0x20, 0xe0, /* 001000001110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x70, 0x40, /* 011100000100 */
	0x38, 0x80, /* 001110001000 */
	0x1f, 0x00, /* 000111110000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 163 0xa3 '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x01, 0x80, /* 000000011000 */
	0x03, 0x00, /* 000000110000 */
	0x06, 0x00, /* 000001100000 */
	0x00, 0x00, /* 000000000000 */
	0x79, 0xe0, /* 011110011110 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x39, 0xc0, /* 001110011100 */
	0x1e, 0x60, /* 000111100110 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 164 0xa4 '.' */
	0x00, 0x00, /* 000000000000 */
	0x1c, 0x40, /* 000111000100 */
	0x3f, 0xc0, /* 001111111100 */
	0x23, 0x80, /* 001000111000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x27, 0x80, /* 001001111000 */
	0x79, 0xc0, /* 011110011100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x79, 0xe0, /* 011110011110 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 165 0xa5 '.' */
	0x00, 0x00, /* 000000000000 */
	0x1c, 0x40, /* 000111000100 */
	0x3f, 0xc0, /* 001111111100 */
	0x23, 0x80, /* 001000111000 */
	0xc0, 0x70, /* 110000000111 */
	0x60, 0x20, /* 011000000010 */
	0x70, 0x20, /* 011100000010 */
	0x78, 0x20, /* 011110000010 */
	0x5c, 0x20, /* 010111000010 */
	0x4e, 0x20, /* 010011100010 */
	0x47, 0x20, /* 010001110010 */
	0x43, 0xa0, /* 010000111010 */
	0x41, 0xe0, /* 010000011110 */
	0x40, 0xe0, /* 010000001110 */
	0x40, 0x60, /* 010000000110 */
	0xe0, 0x30, /* 111000000011 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 166 0xa6 '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x1f, 0x00, /* 000111110000 */
	0x31, 0x80, /* 001100011000 */
	0x01, 0x80, /* 000000011000 */
	0x07, 0x80, /* 000001111000 */
	0x19, 0x80, /* 000110011000 */
	0x31, 0x80, /* 001100011000 */
	0x31, 0x80, /* 001100011000 */
	0x33, 0x80, /* 001100111000 */
	0x1d, 0xc0, /* 000111011100 */
	0x00, 0x00, /* 000000000000 */
	0x3f, 0xc0, /* 001111111100 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 167 0xa7 '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x07, 0x00, /* 000001110000 */
	0x19, 0x80, /* 000110011000 */
	0x10, 0xc0, /* 000100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0x80, /* 001100001000 */
	0x19, 0x80, /* 000110011000 */
	0x0e, 0x00, /* 000011100000 */
	0x00, 0x00, /* 000000000000 */
	0x3f, 0xc0, /* 001111111100 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 168 0xa8 '.' */
	0x00, 0x00, /* 000000000000 */
	0x03, 0x00, /* 000000110000 */
	0x03, 0x00, /* 000000110000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x03, 0x00, /* 000000110000 */
	0x03, 0x00, /* 000000110000 */
	0x06, 0x00, /* 000001100000 */
	0x0c, 0x00, /* 000011000000 */
	0x18, 0x00, /* 000110000000 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x40, /* 001100000100 */
	0x39, 0xc0, /* 001110011100 */
	0x1f, 0x80, /* 000111111000 */
	0x0f, 0x00, /* 000011110000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 169 0xa9 '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x3f, 0xc0, /* 001111111100 */
	0x3f, 0xc0, /* 001111111100 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x00, /* 001100000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 170 0xaa '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x3f, 0xc0, /* 001111111100 */
	0x3f, 0xc0, /* 001111111100 */
	0x00, 0xc0, /* 000000001100 */
	0x00, 0xc0, /* 000000001100 */
	0x00, 0xc0, /* 000000001100 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 171 0xab '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x10, 0x00, /* 000100000000 */
	0x30, 0x00, /* 001100000000 */
	0x10, 0x00, /* 000100000000 */
	0x10, 0x40, /* 000100000100 */
	0x10, 0x80, /* 000100001000 */
	0x11, 0x00, /* 000100010000 */
	0x3a, 0x00, /* 001110100000 */
	0x05, 0xc0, /* 000001011100 */
	0x0a, 0x20, /* 000010100010 */
	0x10, 0x20, /* 000100000010 */
	0x20, 0xc0, /* 001000001100 */
	0x41, 0x00, /* 010000010000 */
	0x02, 0x00, /* 000000100000 */
	0x03, 0xe0, /* 000000111110 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 172 0xac '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x10, 0x00, /* 000100000000 */
	0x30, 0x00, /* 001100000000 */
	0x10, 0x00, /* 000100000000 */
	0x10, 0x40, /* 000100000100 */
	0x10, 0x80, /* 000100001000 */
	0x11, 0x00, /* 000100010000 */
	0x3a, 0x40, /* 001110100100 */
	0x04, 0xc0, /* 000001001100 */
	0x09, 0x40, /* 000010010100 */
	0x12, 0x40, /* 000100100100 */
	0x24, 0x40, /* 001001000100 */
	0x47, 0xe0, /* 010001111110 */
	0x00, 0x40, /* 000000000100 */
	0x00, 0x40, /* 000000000100 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 173 0xad '.' */
	0x00, 0x00, /* 000000000000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 174 0xae '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x06, 0x60, /* 000001100110 */
	0x0c, 0xc0, /* 000011001100 */
	0x19, 0x80, /* 000110011000 */
	0x33, 0x00, /* 001100110000 */
	0x66, 0x00, /* 011001100000 */
	0x33, 0x00, /* 001100110000 */
	0x19, 0x80, /* 000110011000 */
	0x0c, 0xc0, /* 000011001100 */
	0x06, 0x60, /* 000001100110 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 175 0xaf '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x66, 0x00, /* 011001100000 */
	0x33, 0x00, /* 001100110000 */
	0x19, 0x80, /* 000110011000 */
	0x0c, 0xc0, /* 000011001100 */
	0x06, 0x60, /* 000001100110 */
	0x0c, 0xc0, /* 000011001100 */
	0x19, 0x80, /* 000110011000 */
	0x33, 0x00, /* 001100110000 */
	0x66, 0x00, /* 011001100000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 176 0xb0 '.' */
	0x0c, 0x30, /* 000011000011 */
	0x08, 0x20, /* 000010000010 */
	0x61, 0x80, /* 011000011000 */
	0x20, 0x80, /* 001000001000 */
	0x0c, 0x30, /* 000011000011 */
	0x08, 0x20, /* 000010000010 */
	0x61, 0x80, /* 011000011000 */
	0x20, 0x80, /* 001000001000 */
	0x0c, 0x30, /* 000011000011 */
	0x08, 0x20, /* 000010000010 */
	0x61, 0x80, /* 011000011000 */
	0x20, 0x80, /* 001000001000 */
	0x0c, 0x30, /* 000011000011 */
	0x08, 0x20, /* 000010000010 */
	0x61, 0x80, /* 011000011000 */
	0x20, 0x80, /* 001000001000 */
	0x0c, 0x30, /* 000011000011 */
	0x08, 0x20, /* 000010000010 */
	0x61, 0x80, /* 011000011000 */
	0x20, 0x80, /* 001000001000 */
	0x0c, 0x30, /* 000011000011 */
	0x08, 0x20, /* 000010000010 */

	/* 177 0xb1 '.' */
	0x77, 0x70, /* 011101110111 */
	0x22, 0x20, /* 001000100010 */
	0x88, 0x80, /* 100010001000 */
	0xdd, 0xd0, /* 110111011101 */
	0x88, 0x80, /* 100010001000 */
	0x22, 0x20, /* 001000100010 */
	0x77, 0x70, /* 011101110111 */
	0x22, 0x20, /* 001000100010 */
	0x88, 0x80, /* 100010001000 */
	0xdd, 0xd0, /* 110111011101 */
	0x88, 0x80, /* 100010001000 */
	0x22, 0x20, /* 001000100010 */
	0x77, 0x70, /* 011101110111 */
	0x22, 0x20, /* 001000100010 */
	0x88, 0x80, /* 100010001000 */
	0xdd, 0xd0, /* 110111011101 */
	0x88, 0x80, /* 100010001000 */
	0x22, 0x20, /* 001000100010 */
	0x77, 0x70, /* 011101110111 */
	0x22, 0x20, /* 001000100010 */
	0x88, 0x80, /* 100010001000 */
	0xdd, 0xd0, /* 110111011101 */

	/* 178 0xb2 '.' */
	0xf3, 0xc0, /* 111100111100 */
	0xf7, 0xd0, /* 111101111101 */
	0x9e, 0x70, /* 100111100111 */
	0xdf, 0x70, /* 110111110111 */
	0xf3, 0xc0, /* 111100111100 */
	0xf7, 0xd0, /* 111101111101 */
	0x9e, 0x70, /* 100111100111 */
	0xdf, 0x70, /* 110111110111 */
	0xf3, 0xc0, /* 111100111100 */
	0xf7, 0xd0, /* 111101111101 */
	0x9e, 0x70, /* 100111100111 */
	0xdf, 0x70, /* 110111110111 */
	0xf3, 0xc0, /* 111100111100 */
	0xf7, 0xd0, /* 111101111101 */
	0x9e, 0x70, /* 100111100111 */
	0xdf, 0x70, /* 110111110111 */
	0xf3, 0xc0, /* 111100111100 */
	0xf7, 0xd0, /* 111101111101 */
	0x9e, 0x70, /* 100111100111 */
	0xdf, 0x70, /* 110111110111 */
	0xf3, 0xc0, /* 111100111100 */
	0xf7, 0xd0, /* 111101111101 */

	/* 179 0xb3 '.' */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */

	/* 180 0xb4 '.' */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0xfe, 0x00, /* 111111100000 */
	0xfe, 0x00, /* 111111100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */

	/* 181 0xb5 '.' */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0xfe, 0x00, /* 111111100000 */
	0xfe, 0x00, /* 111111100000 */
	0x06, 0x00, /* 000001100000 */
	0xfe, 0x00, /* 111111100000 */
	0xfe, 0x00, /* 111111100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */

	/* 182 0xb6 '.' */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0xfd, 0x80, /* 111111011000 */
	0xfd, 0x80, /* 111111011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */

	/* 183 0xb7 '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0xff, 0x80, /* 111111111000 */
	0xff, 0x80, /* 111111111000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */

	/* 184 0xb8 '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0xfe, 0x00, /* 111111100000 */
	0xfe, 0x00, /* 111111100000 */
	0x06, 0x00, /* 000001100000 */
	0xfe, 0x00, /* 111111100000 */
	0xfe, 0x00, /* 111111100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */

	/* 185 0xb9 '.' */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0xfd, 0x80, /* 111111011000 */
	0xfd, 0x80, /* 111111011000 */
	0x01, 0x80, /* 000000011000 */
	0xfd, 0x80, /* 111111011000 */
	0xfd, 0x80, /* 111111011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */

	/* 186 0xba '.' */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */

	/* 187 0xbb '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0xff, 0x80, /* 111111111000 */
	0xff, 0x80, /* 111111111000 */
	0x01, 0x80, /* 000000011000 */
	0xfd, 0x80, /* 111111011000 */
	0xfd, 0x80, /* 111111011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */

	/* 188 0xbc '.' */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0xfd, 0x80, /* 111111011000 */
	0xfd, 0x80, /* 111111011000 */
	0x01, 0x80, /* 000000011000 */
	0xff, 0x80, /* 111111111000 */
	0xff, 0x80, /* 111111111000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 189 0xbd '.' */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0xff, 0x80, /* 111111111000 */
	0xff, 0x80, /* 111111111000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 190 0xbe '.' */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0xfe, 0x00, /* 111111100000 */
	0xfe, 0x00, /* 111111100000 */
	0x06, 0x00, /* 000001100000 */
	0xfe, 0x00, /* 111111100000 */
	0xfe, 0x00, /* 111111100000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 191 0xbf '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0xfe, 0x00, /* 111111100000 */
	0xfe, 0x00, /* 111111100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */

	/* 192 0xc0 '.' */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x07, 0xf0, /* 000001111111 */
	0x07, 0xf0, /* 000001111111 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 193 0xc1 '.' */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 194 0xc2 '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */

	/* 195 0xc3 '.' */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x07, 0xf0, /* 000001111111 */
	0x07, 0xf0, /* 000001111111 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */

	/* 196 0xc4 '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 197 0xc5 '.' */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */

	/* 198 0xc6 '.' */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x07, 0xf0, /* 000001111111 */
	0x07, 0xf0, /* 000001111111 */
	0x06, 0x00, /* 000001100000 */
	0x07, 0xf0, /* 000001111111 */
	0x07, 0xf0, /* 000001111111 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */

	/* 199 0xc7 '.' */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0xf0, /* 000011011111 */
	0x0d, 0xf0, /* 000011011111 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */

	/* 200 0xc8 '.' */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0xf0, /* 000011011111 */
	0x0d, 0xf0, /* 000011011111 */
	0x0c, 0x00, /* 000011000000 */
	0x0f, 0xf0, /* 000011111111 */
	0x0f, 0xf0, /* 000011111111 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 201 0xc9 '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x0f, 0xf0, /* 000011111111 */
	0x0f, 0xf0, /* 000011111111 */
	0x0c, 0x00, /* 000011000000 */
	0x0d, 0xf0, /* 000011011111 */
	0x0d, 0xf0, /* 000011011111 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */

	/* 202 0xca '.' */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0xfd, 0xf0, /* 111111011111 */
	0xfd, 0xf0, /* 111111011111 */
	0x00, 0x00, /* 000000000000 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 203 0xcb '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0x00, 0x00, /* 000000000000 */
	0xfd, 0xf0, /* 111111011111 */
	0xfd, 0xf0, /* 111111011111 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */

	/* 204 0xcc '.' */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0xf0, /* 000011011111 */
	0x0d, 0xf0, /* 000011011111 */
	0x0c, 0x00, /* 000011000000 */
	0x0d, 0xf0, /* 000011011111 */
	0x0d, 0xf0, /* 000011011111 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */

	/* 205 0xcd '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0x00, 0x00, /* 000000000000 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 206 0xce '.' */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0xfd, 0xf0, /* 111111011111 */
	0xfd, 0xf0, /* 111111011111 */
	0x00, 0x00, /* 000000000000 */
	0xfd, 0xf0, /* 111111011111 */
	0xfd, 0xf0, /* 111111011111 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */

	/* 207 0xcf '.' */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0x00, 0x00, /* 000000000000 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 208 0xd0 '.' */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 209 0xd1 '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0x00, 0x00, /* 000000000000 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */

	/* 210 0xd2 '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */

	/* 211 0xd3 '.' */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0f, 0xf0, /* 000011111111 */
	0x0f, 0xf0, /* 000011111111 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 212 0xd4 '.' */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x07, 0xf0, /* 000001111111 */
	0x07, 0xf0, /* 000001111111 */
	0x06, 0x00, /* 000001100000 */
	0x07, 0xf0, /* 000001111111 */
	0x07, 0xf0, /* 000001111111 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 213 0xd5 '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x07, 0xf0, /* 000001111111 */
	0x07, 0xf0, /* 000001111111 */
	0x06, 0x00, /* 000001100000 */
	0x07, 0xf0, /* 000001111111 */
	0x07, 0xf0, /* 000001111111 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */

	/* 214 0xd6 '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x0f, 0xf0, /* 000011111111 */
	0x0f, 0xf0, /* 000011111111 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */

	/* 215 0xd7 '.' */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */
	0x0d, 0x80, /* 000011011000 */

	/* 216 0xd8 '.' */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0x06, 0x00, /* 000001100000 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */

	/* 217 0xd9 '.' */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0xfe, 0x00, /* 111111100000 */
	0xfe, 0x00, /* 111111100000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 218 0xda '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x07, 0xf0, /* 000001111111 */
	0x07, 0xf0, /* 000001111111 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */

	/* 219 0xdb '.' */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */

	/* 220 0xdc '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */

	/* 221 0xdd '.' */
	0xfc, 0x00, /* 111111000000 */
	0xfc, 0x00, /* 111111000000 */
	0xfc, 0x00, /* 111111000000 */
	0xfc, 0x00, /* 111111000000 */
	0xfc, 0x00, /* 111111000000 */
	0xfc, 0x00, /* 111111000000 */
	0xfc, 0x00, /* 111111000000 */
	0xfc, 0x00, /* 111111000000 */
	0xfc, 0x00, /* 111111000000 */
	0xfc, 0x00, /* 111111000000 */
	0xfc, 0x00, /* 111111000000 */
	0xfc, 0x00, /* 111111000000 */
	0xfc, 0x00, /* 111111000000 */
	0xfc, 0x00, /* 111111000000 */
	0xfc, 0x00, /* 111111000000 */
	0xfc, 0x00, /* 111111000000 */
	0xfc, 0x00, /* 111111000000 */
	0xfc, 0x00, /* 111111000000 */
	0xfc, 0x00, /* 111111000000 */
	0xfc, 0x00, /* 111111000000 */
	0xfc, 0x00, /* 111111000000 */
	0xfc, 0x00, /* 111111000000 */

	/* 222 0xde '.' */
	0x03, 0xf0, /* 000000111111 */
	0x03, 0xf0, /* 000000111111 */
	0x03, 0xf0, /* 000000111111 */
	0x03, 0xf0, /* 000000111111 */
	0x03, 0xf0, /* 000000111111 */
	0x03, 0xf0, /* 000000111111 */
	0x03, 0xf0, /* 000000111111 */
	0x03, 0xf0, /* 000000111111 */
	0x03, 0xf0, /* 000000111111 */
	0x03, 0xf0, /* 000000111111 */
	0x03, 0xf0, /* 000000111111 */
	0x03, 0xf0, /* 000000111111 */
	0x03, 0xf0, /* 000000111111 */
	0x03, 0xf0, /* 000000111111 */
	0x03, 0xf0, /* 000000111111 */
	0x03, 0xf0, /* 000000111111 */
	0x03, 0xf0, /* 000000111111 */
	0x03, 0xf0, /* 000000111111 */
	0x03, 0xf0, /* 000000111111 */
	0x03, 0xf0, /* 000000111111 */
	0x03, 0xf0, /* 000000111111 */
	0x03, 0xf0, /* 000000111111 */

	/* 223 0xdf '.' */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0xff, 0xf0, /* 111111111111 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 224 0xe0 '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x0f, 0x60, /* 000011110110 */
	0x13, 0xe0, /* 000100111110 */
	0x21, 0xc0, /* 001000011100 */
	0x60, 0xc0, /* 011000001100 */
	0x60, 0xc0, /* 011000001100 */
	0x60, 0xc0, /* 011000001100 */
	0x60, 0xc0, /* 011000001100 */
	0x70, 0x80, /* 011100001000 */
	0x39, 0xc0, /* 001110011100 */
	0x1f, 0x60, /* 000111110110 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 225 0xe1 '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x0f, 0x00, /* 000011110000 */
	0x19, 0x80, /* 000110011000 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x31, 0x80, /* 001100011000 */
	0x37, 0x80, /* 001101111000 */
	0x31, 0x80, /* 001100011000 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x31, 0x80, /* 001100011000 */
	0x77, 0x00, /* 011101110000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 226 0xe2 '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x3f, 0xe0, /* 001111111110 */
	0x3f, 0xe0, /* 001111111110 */
	0x30, 0x60, /* 001100000110 */
	0x30, 0x60, /* 001100000110 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x00, /* 001100000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 227 0xe3 '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x7f, 0xe0, /* 011111111110 */
	0x7f, 0xe0, /* 011111111110 */
	0x19, 0x80, /* 000110011000 */
	0x19, 0x80, /* 000110011000 */
	0x19, 0x80, /* 000110011000 */
	0x19, 0x80, /* 000110011000 */
	0x19, 0x80, /* 000110011000 */
	0x19, 0x80, /* 000110011000 */
	0x19, 0x80, /* 000110011000 */
	0x19, 0x80, /* 000110011000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 228 0xe4 '.' */
	0x00, 0x00, /* 000000000000 */
	0x7f, 0xe0, /* 011111111110 */
	0x7f, 0xe0, /* 011111111110 */
	0x60, 0x60, /* 011000000110 */
	0x30, 0x60, /* 001100000110 */
	0x30, 0x00, /* 001100000000 */
	0x18, 0x00, /* 000110000000 */
	0x18, 0x00, /* 000110000000 */
	0x0c, 0x00, /* 000011000000 */
	0x18, 0x00, /* 000110000000 */
	0x18, 0x00, /* 000110000000 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x60, /* 001100000110 */
	0x60, 0x60, /* 011000000110 */
	0x7f, 0xe0, /* 011111111110 */
	0x7f, 0xe0, /* 011111111110 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 229 0xe5 '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x07, 0xe0, /* 000001111110 */
	0x0f, 0xe0, /* 000011111110 */
	0x13, 0x80, /* 000100111000 */
	0x21, 0xc0, /* 001000011100 */
	0x60, 0xc0, /* 011000001100 */
	0x60, 0xc0, /* 011000001100 */
	0x60, 0xc0, /* 011000001100 */
	0x60, 0xc0, /* 011000001100 */
	0x70, 0x80, /* 011100001000 */
	0x39, 0x00, /* 001110010000 */
	0x1e, 0x00, /* 000111100000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 230 0xe6 '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x39, 0xc0, /* 001110011100 */
	0x36, 0xe0, /* 001101101110 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x00, /* 001100000000 */
	0x60, 0x00, /* 011000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 231 0xe7 '.' */
	0x00, 0x00, /* 000000000000 */
	0x19, 0x80, /* 000110011000 */
	0x3f, 0xc0, /* 001111111100 */
	0x66, 0x60, /* 011001100110 */
	0x66, 0x60, /* 011001100110 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 232 0xe8 '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x7f, 0xe0, /* 011111111110 */
	0x7f, 0xe0, /* 011111111110 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x0f, 0x00, /* 000011110000 */
	0x19, 0x80, /* 000110011000 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x19, 0x80, /* 000110011000 */
	0x0f, 0x00, /* 000011110000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x7f, 0xe0, /* 011111111110 */
	0x7f, 0xe0, /* 011111111110 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 233 0xe9 '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x0f, 0x00, /* 000011110000 */
	0x1f, 0x80, /* 000111111000 */
	0x30, 0xc0, /* 001100001100 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x7f, 0xe0, /* 011111111110 */
	0x7f, 0xe0, /* 011111111110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x30, 0xc0, /* 001100001100 */
	0x1f, 0x80, /* 000111111000 */
	0x0f, 0x00, /* 000011110000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 234 0xea '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x1f, 0x00, /* 000111110000 */
	0x31, 0x80, /* 001100011000 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x19, 0x80, /* 000110011000 */
	0x19, 0x80, /* 000110011000 */
	0xd9, 0xb0, /* 110110011011 */
	0x79, 0xe0, /* 011110011110 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 235 0xeb '.' */
	0x00, 0x00, /* 000000000000 */
	0x07, 0x80, /* 000001111000 */
	0x0c, 0xc0, /* 000011001100 */
	0x18, 0x60, /* 000110000110 */
	0x18, 0x00, /* 000110000000 */
	0x0c, 0x00, /* 000011000000 */
	0x06, 0x00, /* 000001100000 */
	0x03, 0x00, /* 000000110000 */
	0x0f, 0x80, /* 000011111000 */
	0x11, 0xc0, /* 000100011100 */
	0x20, 0xe0, /* 001000001110 */
	0x60, 0x60, /* 011000000110 */
	0x60, 0x60, /* 011000000110 */
	0x70, 0x40, /* 011100000100 */
	0x38, 0x80, /* 001110001000 */
	0x1f, 0x00, /* 000111110000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 236 0xec '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x39, 0xc0, /* 001110011100 */
	0x6f, 0x60, /* 011011110110 */
	0x66, 0x60, /* 011001100110 */
	0xc6, 0x30, /* 110001100011 */
	0xc6, 0x30, /* 110001100011 */
	0x66, 0x60, /* 011001100110 */
	0x6f, 0x60, /* 011011110110 */
	0x39, 0xc0, /* 001110011100 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 237 0xed '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0xc0, /* 000000001100 */
	0x00, 0xc0, /* 000000001100 */
	0x01, 0x80, /* 000000011000 */
	0x01, 0x80, /* 000000011000 */
	0x3b, 0xc0, /* 001110111100 */
	0x6f, 0x60, /* 011011110110 */
	0x66, 0x60, /* 011001100110 */
	0xc6, 0x30, /* 110001100011 */
	0xc6, 0x30, /* 110001100011 */
	0x66, 0x60, /* 011001100110 */
	0x6f, 0x60, /* 011011110110 */
	0x3d, 0xc0, /* 001111011100 */
	0x18, 0x00, /* 000110000000 */
	0x18, 0x00, /* 000110000000 */
	0x30, 0x00, /* 001100000000 */
	0x30, 0x00, /* 001100000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 238 0xee '.' */
	0x00, 0x00, /* 000000000000 */
	0x01, 0xc0, /* 000000011100 */
	0x03, 0x00, /* 000000110000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x18, 0x00, /* 000110000000 */
	0x1f, 0xc0, /* 000111111100 */
	0x18, 0x00, /* 000110000000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x03, 0x00, /* 000000110000 */
	0x01, 0xc0, /* 000000011100 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 239 0xef '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x0f, 0x00, /* 000011110000 */
	0x1f, 0x80, /* 000111111000 */
	0x39, 0xc0, /* 001110011100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x30, 0xc0, /* 001100001100 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 240 0xf0 '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x7f, 0xe0, /* 011111111110 */
	0x7f, 0xe0, /* 011111111110 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x7f, 0xe0, /* 011111111110 */
	0x7f, 0xe0, /* 011111111110 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x7f, 0xe0, /* 011111111110 */
	0x7f, 0xe0, /* 011111111110 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 241 0xf1 '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x7f, 0xe0, /* 011111111110 */
	0x7f, 0xe0, /* 011111111110 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x00, 0x00, /* 000000000000 */
	0x7f, 0xe0, /* 011111111110 */
	0x7f, 0xe0, /* 011111111110 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 242 0xf2 '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x60, 0x00, /* 011000000000 */
	0x38, 0x00, /* 001110000000 */
	0x0e, 0x00, /* 000011100000 */
	0x03, 0x80, /* 000000111000 */
	0x00, 0xe0, /* 000000001110 */
	0x00, 0xe0, /* 000000001110 */
	0x03, 0x80, /* 000000111000 */
	0x0e, 0x00, /* 000011100000 */
	0x38, 0x00, /* 001110000000 */
	0x60, 0x00, /* 011000000000 */
	0x00, 0x00, /* 000000000000 */
	0x7f, 0xe0, /* 011111111110 */
	0x7f, 0xe0, /* 011111111110 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 243 0xf3 '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x60, /* 000000000110 */
	0x01, 0xc0, /* 000000011100 */
	0x07, 0x00, /* 000001110000 */
	0x1c, 0x00, /* 000111000000 */
	0x70, 0x00, /* 011100000000 */
	0x70, 0x00, /* 011100000000 */
	0x1c, 0x00, /* 000111000000 */
	0x07, 0x00, /* 000001110000 */
	0x01, 0xc0, /* 000000011100 */
	0x00, 0x60, /* 000000000110 */
	0x00, 0x00, /* 000000000000 */
	0x7f, 0xe0, /* 011111111110 */
	0x7f, 0xe0, /* 011111111110 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 244 0xf4 '.' */
	0x00, 0x00, /* 000000000000 */
	0x03, 0x80, /* 000000111000 */
	0x07, 0xc0, /* 000001111100 */
	0x0c, 0x60, /* 000011000110 */
	0x0c, 0x60, /* 000011000110 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */
	0x0c, 0x00, /* 000011000000 */

	/* 245 0xf5 '.' */
	0x00, 0x00, /* 000000000000 */
	0x1c, 0x00, /* 000111000000 */
	0x3e, 0x00, /* 001111100000 */
	0x63, 0x00, /* 011000110000 */
	0x63, 0x00, /* 011000110000 */
	0x03, 0x00, /* 000000110000 */
	0x03, 0x00, /* 000000110000 */
	0x03, 0x00, /* 000000110000 */
	0x03, 0x00, /* 000000110000 */
	0x03, 0x00, /* 000000110000 */
	0x03, 0x00, /* 000000110000 */
	0x03, 0x00, /* 000000110000 */
	0x03, 0x00, /* 000000110000 */
	0x03, 0x00, /* 000000110000 */
	0x03, 0x00, /* 000000110000 */
	0x03, 0x00, /* 000000110000 */
	0x03, 0x00, /* 000000110000 */
	0x03, 0x00, /* 000000110000 */
	0x03, 0x00, /* 000000110000 */
	0x03, 0x00, /* 000000110000 */
	0x03, 0x00, /* 000000110000 */
	0x03, 0x00, /* 000000110000 */

	/* 246 0xf6 '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x7f, 0xe0, /* 011111111110 */
	0x7f, 0xe0, /* 011111111110 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 247 0xf7 '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x38, 0x00, /* 001110000000 */
	0x6c, 0x00, /* 011011000000 */
	0x06, 0x30, /* 000001100011 */
	0x03, 0x60, /* 000000110110 */
	0x39, 0xc0, /* 001110011100 */
	0x6c, 0x00, /* 011011000000 */
	0x06, 0x30, /* 000001100011 */
	0x03, 0x60, /* 000000110110 */
	0x01, 0xc0, /* 000000011100 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 248 0xf8 '.' */
	0x00, 0x00, /* 000000000000 */
	0x0f, 0x00, /* 000011110000 */
	0x19, 0x80, /* 000110011000 */
	0x19, 0x80, /* 000110011000 */
	0x19, 0x80, /* 000110011000 */
	0x0f, 0x00, /* 000011110000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 249 0xf9 '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x1c, 0x00, /* 000111000000 */
	0x3e, 0x00, /* 001111100000 */
	0x3e, 0x00, /* 001111100000 */
	0x3e, 0x00, /* 001111100000 */
	0x1c, 0x00, /* 000111000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 250 0xfa '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x18, 0x00, /* 000110000000 */
	0x3c, 0x00, /* 001111000000 */
	0x3c, 0x00, /* 001111000000 */
	0x18, 0x00, /* 000110000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 251 0xfb '.' */
	0x00, 0x00, /* 000000000000 */
	0x07, 0xe0, /* 000001111110 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0x06, 0x00, /* 000001100000 */
	0xc6, 0x00, /* 110001100000 */
	0x66, 0x00, /* 011001100000 */
	0x36, 0x00, /* 001101100000 */
	0x1e, 0x00, /* 000111100000 */
	0x0e, 0x00, /* 000011100000 */
	0x06, 0x00, /* 000001100000 */
	0x02, 0x00, /* 000000100000 */
	0x00, 0x00, /* 000000000000 */

	/* 252 0xfc '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x13, 0x80, /* 000100111000 */
	0x3d, 0xc0, /* 001111011100 */
	0x18, 0xc0, /* 000110001100 */
	0x18, 0xc0, /* 000110001100 */
	0x18, 0xc0, /* 000110001100 */
	0x18, 0xc0, /* 000110001100 */
	0x3d, 0xe0, /* 001111011110 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 253 0xfd '.' */
	0x00, 0x00, /* 000000000000 */
	0x0f, 0x00, /* 000011110000 */
	0x1f, 0x80, /* 000111111000 */
	0x31, 0x80, /* 001100011000 */
	0x21, 0x80, /* 001000011000 */
	0x03, 0x00, /* 000000110000 */
	0x06, 0x00, /* 000001100000 */
	0x0c, 0x00, /* 000011000000 */
	0x18, 0x40, /* 000110000100 */
	0x3f, 0xc0, /* 001111111100 */
	0x3f, 0xc0, /* 001111111100 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 254 0xfe '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x3f, 0xc0, /* 001111111100 */
	0x3f, 0xc0, /* 001111111100 */
	0x3f, 0xc0, /* 001111111100 */
	0x3f, 0xc0, /* 001111111100 */
	0x3f, 0xc0, /* 001111111100 */
	0x3f, 0xc0, /* 001111111100 */
	0x3f, 0xc0, /* 001111111100 */
	0x3f, 0xc0, /* 001111111100 */
	0x3f, 0xc0, /* 001111111100 */
	0x3f, 0xc0, /* 001111111100 */
	0x3f, 0xc0, /* 001111111100 */
	0x3f, 0xc0, /* 001111111100 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */

	/* 255 0xff '.' */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
	0x00, 0x00, /* 000000000000 */
} };


const struct font_desc font_sun_12x22 = {
	.idx	= SUN12x22_IDX,
	.name	= "SUN12x22",
	.width	= 12,
	.height	= 22,
	.charcount = 256,
	.data	= fontdata_sun12x22.data,
#ifdef __sparc__
	.pref	= 5,
#else
	.pref	= -1,
#endif
};
