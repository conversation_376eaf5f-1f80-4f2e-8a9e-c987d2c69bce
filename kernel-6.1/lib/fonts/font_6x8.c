// SPDX-License-Identifier: GPL-2.0
#include <linux/font.h>

#define FONTDATAMAX 2048

static const struct font_data fontdata_6x8 = {
	{ 0, 0, FONTDATAMAX, 0 }, {
	/* 0 0x00 '^@' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */

	/* 1 0x01 '^A' */
	0x78, /* 011110 */
	0x84, /* 100001 */
	0xCC, /* 110011 */
	0x84, /* 100001 */
	0xCC, /* 110011 */
	0xB4, /* 101101 */
	0x78, /* 011110 */
	0x00, /* 000000 */

	/* 2 0x02 '^B' */
	0x78, /* 011110 */
	0xFC, /* 111111 */
	0xB4, /* 101101 */
	0xFC, /* 111111 */
	0xB4, /* 101101 */
	0xCC, /* 110011 */
	0x78, /* 011110 */
	0x00, /* 000000 */

	/* 3 0x03 '^C' */
	0x00, /* 000000 */
	0x28, /* 001010 */
	0x7C, /* 011111 */
	0x7C, /* 011111 */
	0x38, /* 001110 */
	0x10, /* 000100 */
	0x00, /* 000000 */
	0x00, /* 000000 */

	/* 4 0x04 '^D' */
	0x00, /* 000000 */
	0x10, /* 000100 */
	0x38, /* 001110 */
	0x7C, /* 011111 */
	0x38, /* 001110 */
	0x10, /* 000100 */
	0x00, /* 000000 */
	0x00, /* 000000 */

	/* 5 0x05 '^E' */
	0x00, /* 000000 */
	0x38, /* 001110 */
	0x38, /* 001110 */
	0x6C, /* 011011 */
	0x6C, /* 011011 */
	0x10, /* 000100 */
	0x38, /* 001110 */
	0x00, /* 000000 */

	/* 6 0x06 '^F' */
	0x00, /* 000000 */
	0x10, /* 000100 */
	0x38, /* 001110 */
	0x7C, /* 011111 */
	0x7C, /* 011111 */
	0x10, /* 000100 */
	0x38, /* 001110 */
	0x00, /* 000000 */

	/* 7 0x07 '^G' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x30, /* 001100 */
	0x78, /* 011110 */
	0x30, /* 001100 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */

	/* 8 0x08 '^H' */
	0xFC, /* 111111 */
	0xFC, /* 111111 */
	0xCC, /* 110011 */
	0x84, /* 100001 */
	0xCC, /* 110011 */
	0xFC, /* 111111 */
	0xFC, /* 111111 */
	0xFC, /* 111111 */

	/* 9 0x09 '^I' */
	0x00, /* 000000 */
	0x30, /* 001100 */
	0x48, /* 010010 */
	0x84, /* 100001 */
	0x48, /* 010010 */
	0x30, /* 001100 */
	0x00, /* 000000 */
	0x00, /* 000000 */

	/* 10 0x0A '^J' */
	0xFC, /* 111111 */
	0xCC, /* 110011 */
	0xB4, /* 101101 */
	0x78, /* 011110 */
	0xB4, /* 101101 */
	0xCC, /* 110011 */
	0xFC, /* 111111 */
	0xFC, /* 111111 */

	/* 11 0x0B '^K' */
	0x3C, /* 001111 */
	0x14, /* 000101 */
	0x20, /* 001000 */
	0x78, /* 011110 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x38, /* 001110 */
	0x00, /* 000000 */

	/* 12 0x0C '^L' */
	0x38, /* 001110 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x38, /* 001110 */
	0x10, /* 000100 */
	0x38, /* 001110 */
	0x10, /* 000100 */
	0x00, /* 000000 */

	/* 13 0x0D '^M' */
	0x18, /* 000110 */
	0x14, /* 000101 */
	0x14, /* 000101 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x70, /* 011100 */
	0x60, /* 011000 */
	0x00, /* 000000 */

	/* 14 0x0E '^N' */
	0x3C, /* 001111 */
	0x24, /* 001001 */
	0x3C, /* 001111 */
	0x24, /* 001001 */
	0x24, /* 001001 */
	0x6C, /* 011011 */
	0x6C, /* 011011 */
	0x00, /* 000000 */

	/* 15 0x0F '^O' */
	0x10, /* 000100 */
	0x54, /* 010101 */
	0x38, /* 001110 */
	0x6C, /* 011011 */
	0x38, /* 001110 */
	0x54, /* 010101 */
	0x10, /* 000100 */
	0x00, /* 000000 */

	/* 16 0x10 '^P' */
	0x40, /* 010000 */
	0x60, /* 011000 */
	0x70, /* 011100 */
	0x78, /* 011110 */
	0x70, /* 011100 */
	0x60, /* 011000 */
	0x40, /* 010000 */
	0x00, /* 000000 */

	/* 17 0x11 '^Q' */
	0x04, /* 000001 */
	0x0C, /* 000011 */
	0x1C, /* 000111 */
	0x3C, /* 001111 */
	0x1C, /* 000111 */
	0x0C, /* 000011 */
	0x04, /* 000001 */
	0x00, /* 000000 */

	/* 18 0x12 '^R' */
	0x10, /* 000100 */
	0x38, /* 001110 */
	0x54, /* 010101 */
	0x10, /* 000100 */
	0x54, /* 010101 */
	0x38, /* 001110 */
	0x10, /* 000100 */
	0x00, /* 000000 */

	/* 19 0x13 '^S' */
	0x48, /* 010010 */
	0x48, /* 010010 */
	0x48, /* 010010 */
	0x48, /* 010010 */
	0x48, /* 010010 */
	0x00, /* 000000 */
	0x48, /* 010010 */
	0x00, /* 000000 */

	/* 20 0x14 '^T' */
	0x3C, /* 001111 */
	0x54, /* 010101 */
	0x54, /* 010101 */
	0x3C, /* 001111 */
	0x14, /* 000101 */
	0x14, /* 000101 */
	0x14, /* 000101 */
	0x00, /* 000000 */

	/* 21 0x15 '^U' */
	0x38, /* 001110 */
	0x44, /* 010001 */
	0x30, /* 001100 */
	0x28, /* 001010 */
	0x14, /* 000101 */
	0x0C, /* 000011 */
	0x44, /* 010001 */
	0x38, /* 001110 */

	/* 22 0x16 '^V' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0xF8, /* 111110 */
	0xF8, /* 111110 */
	0xF8, /* 111110 */
	0x00, /* 000000 */

	/* 23 0x17 '^W' */
	0x10, /* 000100 */
	0x38, /* 001110 */
	0x54, /* 010101 */
	0x10, /* 000100 */
	0x54, /* 010101 */
	0x38, /* 001110 */
	0x10, /* 000100 */
	0x7C, /* 011111 */

	/* 24 0x18 '^X' */
	0x10, /* 000100 */
	0x38, /* 001110 */
	0x54, /* 010101 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x00, /* 000000 */

	/* 25 0x19 '^Y' */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x54, /* 010101 */
	0x38, /* 001110 */
	0x10, /* 000100 */
	0x00, /* 000000 */

	/* 26 0x1A '^Z' */
	0x00, /* 000000 */
	0x10, /* 000100 */
	0x08, /* 000010 */
	0x7C, /* 011111 */
	0x08, /* 000010 */
	0x10, /* 000100 */
	0x00, /* 000000 */
	0x00, /* 000000 */

	/* 27 0x1B '^[' */
	0x00, /* 000000 */
	0x10, /* 000100 */
	0x20, /* 001000 */
	0x7C, /* 011111 */
	0x20, /* 001000 */
	0x10, /* 000100 */
	0x00, /* 000000 */
	0x00, /* 000000 */

	/* 28 0x1C '^\' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x40, /* 010000 */
	0x40, /* 010000 */
	0x40, /* 010000 */
	0x78, /* 011110 */
	0x00, /* 000000 */

	/* 29 0x1D '^]' */
	0x00, /* 000000 */
	0x48, /* 010010 */
	0x84, /* 100001 */
	0xFC, /* 111111 */
	0x84, /* 100001 */
	0x48, /* 010010 */
	0x00, /* 000000 */
	0x00, /* 000000 */

	/* 30 0x1E '^^' */
	0x00, /* 000000 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x38, /* 001110 */
	0x38, /* 001110 */
	0x7C, /* 011111 */
	0x7C, /* 011111 */
	0x00, /* 000000 */

	/* 31 0x1F '^_' */
	0x00, /* 000000 */
	0x7C, /* 011111 */
	0x7C, /* 011111 */
	0x38, /* 001110 */
	0x38, /* 001110 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x00, /* 000000 */

	/* 32 0x20 ' ' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */

	/* 33 0x21 '!' */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x00, /* 000000 */
	0x10, /* 000100 */
	0x00, /* 000000 */

	/* 34 0x22 '"' */
	0x28, /* 001010 */
	0x28, /* 001010 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */

	/* 35 0x23 '#' */
	0x00, /* 000000 */
	0x28, /* 001010 */
	0x7C, /* 011111 */
	0x28, /* 001010 */
	0x28, /* 001010 */
	0x7C, /* 011111 */
	0x28, /* 001010 */
	0x00, /* 000000 */

	/* 36 0x24 '$' */
	0x10, /* 000000 */
	0x38, /* 001000 */
	0x40, /* 010000 */
	0x30, /* 001000 */
	0x08, /* 000000 */
	0x70, /* 011000 */
	0x20, /* 001000 */
	0x00, /* 000000 */

	/* 37 0x25 '%' */
	0x64, /* 011001 */
	0x64, /* 011001 */
	0x08, /* 000010 */
	0x10, /* 000100 */
	0x20, /* 001000 */
	0x4C, /* 010011 */
	0x4C, /* 010011 */
	0x00, /* 000000 */

	/* 38 0x26 '&' */
	0x30, /* 001100 */
	0x48, /* 010010 */
	0x50, /* 010100 */
	0x20, /* 001000 */
	0x54, /* 010101 */
	0x48, /* 010010 */
	0x34, /* 001101 */
	0x00, /* 000000 */

	/* 39 0x27 ''' */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */

	/* 40 0x28 '(' */
	0x08, /* 000010 */
	0x10, /* 000100 */
	0x20, /* 001000 */
	0x20, /* 001000 */
	0x20, /* 001000 */
	0x10, /* 000100 */
	0x08, /* 000010 */
	0x00, /* 000000 */

	/* 41 0x29 ')' */
	0x20, /* 001000 */
	0x10, /* 000100 */
	0x08, /* 000010 */
	0x08, /* 000010 */
	0x08, /* 000010 */
	0x10, /* 000100 */
	0x20, /* 001000 */
	0x00, /* 000000 */

	/* 42 0x2A '*' */
	0x10, /* 000100 */
	0x54, /* 010101 */
	0x38, /* 001110 */
	0x54, /* 010101 */
	0x10, /* 000100 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */

	/* 43 0x2B '+' */
	0x00, /* 000000 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x7C, /* 011111 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x00, /* 000000 */
	0x00, /* 000000 */

	/* 44 0x2C ',' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x30, /* 001100 */
	0x30, /* 001100 */
	0x20, /* 001000 */

	/* 45 0x2D '-' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x7C, /* 011111 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */

	/* 46 0x2E '.' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x18, /* 000110 */
	0x18, /* 000110 */
	0x00, /* 000000 */

	/* 47 0x2F '/' */
	0x04, /* 000001 */
	0x08, /* 000010 */
	0x08, /* 000010 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x20, /* 001000 */
	0x20, /* 001000 */
	0x40, /* 010000 */

	/* 48 0x30 '0' */
	0x38, /* 001110 */
	0x44, /* 010001 */
	0x4C, /* 010011 */
	0x54, /* 010101 */
	0x64, /* 011001 */
	0x44, /* 010001 */
	0x38, /* 001110 */
	0x00, /* 000000 */

	/* 49 0x31 '1' */
	0x10, /* 000100 */
	0x30, /* 001100 */
	0x50, /* 010100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x7C, /* 011111 */
	0x00, /* 000000 */

	/* 50 0x32 '2' */
	0x38, /* 001110 */
	0x44, /* 010001 */
	0x04, /* 000001 */
	0x08, /* 000010 */
	0x10, /* 000100 */
	0x20, /* 001000 */
	0x7C, /* 011111 */
	0x00, /* 000000 */

	/* 51 0x33 '3' */
	0x38, /* 001110 */
	0x44, /* 010001 */
	0x04, /* 000001 */
	0x18, /* 000110 */
	0x04, /* 000001 */
	0x44, /* 010001 */
	0x38, /* 001110 */
	0x00, /* 000000 */

	/* 52 0x34 '4' */
	0x08, /* 000010 */
	0x18, /* 000110 */
	0x28, /* 001010 */
	0x48, /* 010010 */
	0x7C, /* 011111 */
	0x08, /* 000010 */
	0x08, /* 000010 */
	0x00, /* 000000 */

	/* 53 0x35 '5' */
	0x7C, /* 011111 */
	0x40, /* 010000 */
	0x78, /* 011110 */
	0x04, /* 000001 */
	0x04, /* 000001 */
	0x44, /* 010001 */
	0x38, /* 001110 */
	0x00, /* 000000 */

	/* 54 0x36 '6' */
	0x18, /* 000110 */
	0x20, /* 001000 */
	0x40, /* 010000 */
	0x78, /* 011110 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x38, /* 001110 */
	0x00, /* 000000 */

	/* 55 0x37 '7' */
	0x7C, /* 011111 */
	0x04, /* 000001 */
	0x04, /* 000001 */
	0x08, /* 000010 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x00, /* 000000 */

	/* 56 0x38 '8' */
	0x38, /* 001110 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x38, /* 001110 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x38, /* 001110 */
	0x00, /* 000000 */

	/* 57 0x39 '9' */
	0x38, /* 001110 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x3C, /* 001111 */
	0x04, /* 000001 */
	0x08, /* 000010 */
	0x30, /* 001100 */
	0x00, /* 000000 */

	/* 58 0x3A ':' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x18, /* 000110 */
	0x18, /* 000110 */
	0x00, /* 000000 */
	0x18, /* 000110 */
	0x18, /* 000110 */
	0x00, /* 000000 */

	/* 59 0x3B ';' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x30, /* 001100 */
	0x30, /* 001100 */
	0x00, /* 000000 */
	0x30, /* 001100 */
	0x30, /* 001100 */
	0x20, /* 001000 */

	/* 60 0x3C '<' */
	0x04, /* 000001 */
	0x08, /* 000010 */
	0x10, /* 000100 */
	0x20, /* 001000 */
	0x10, /* 000100 */
	0x08, /* 000010 */
	0x04, /* 000001 */
	0x00, /* 000000 */

	/* 61 0x3D '=' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x7C, /* 011111 */
	0x00, /* 000000 */
	0x7C, /* 011111 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */

	/* 62 0x3E '>' */
	0x20, /* 001000 */
	0x10, /* 000100 */
	0x08, /* 000010 */
	0x04, /* 000001 */
	0x08, /* 000010 */
	0x10, /* 000100 */
	0x20, /* 001000 */
	0x00, /* 000000 */

	/* 63 0x3F '?' */
	0x38, /* 001110 */
	0x44, /* 010001 */
	0x04, /* 000001 */
	0x08, /* 000010 */
	0x10, /* 000100 */
	0x00, /* 000000 */
	0x10, /* 000100 */
	0x00, /* 000000 */

	/* 64 0x40 '@' */
	0x38, /* 001110 */
	0x44, /* 010001 */
	0x5C, /* 010111 */
	0x54, /* 010101 */
	0x5C, /* 010111 */
	0x40, /* 010000 */
	0x38, /* 001110 */
	0x00, /* 000000 */

	/* 65 0x41 'A' */
	0x10, /* 000100 */
	0x28, /* 001010 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x7C, /* 011111 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x00, /* 000000 */

	/* 66 0x42 'B' */
	0x78, /* 011110 */
	0x24, /* 001001 */
	0x24, /* 001001 */
	0x38, /* 001110 */
	0x24, /* 001001 */
	0x24, /* 001001 */
	0x78, /* 011110 */
	0x00, /* 000000 */

	/* 67 0x43 'C' */
	0x38, /* 001110 */
	0x44, /* 010001 */
	0x40, /* 010000 */
	0x40, /* 010000 */
	0x40, /* 010000 */
	0x44, /* 010001 */
	0x38, /* 001110 */
	0x00, /* 000000 */

	/* 68 0x44 'D' */
	0x78, /* 011110 */
	0x24, /* 001001 */
	0x24, /* 001001 */
	0x24, /* 001001 */
	0x24, /* 001001 */
	0x24, /* 001001 */
	0x78, /* 011110 */
	0x00, /* 000000 */

	/* 69 0x45 'E' */
	0x7C, /* 011111 */
	0x40, /* 010000 */
	0x40, /* 010000 */
	0x78, /* 011110 */
	0x40, /* 010000 */
	0x40, /* 010000 */
	0x7C, /* 011111 */
	0x00, /* 000000 */

	/* 70 0x46 'F' */
	0x7C, /* 011111 */
	0x40, /* 010000 */
	0x40, /* 010000 */
	0x78, /* 011110 */
	0x40, /* 010000 */
	0x40, /* 010000 */
	0x40, /* 010000 */
	0x00, /* 000000 */

	/* 71 0x47 'G' */
	0x38, /* 001110 */
	0x44, /* 010001 */
	0x40, /* 010000 */
	0x5C, /* 010111 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x38, /* 001110 */
	0x00, /* 000000 */

	/* 72 0x48 'H' */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x7C, /* 011111 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x00, /* 000000 */

	/* 73 0x49 'I' */
	0x38, /* 001110 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x38, /* 001110 */
	0x00, /* 000000 */

	/* 74 0x4A 'J' */
	0x1C, /* 000111 */
	0x08, /* 000010 */
	0x08, /* 000010 */
	0x08, /* 000010 */
	0x48, /* 010010 */
	0x48, /* 010010 */
	0x30, /* 001100 */
	0x00, /* 000000 */

	/* 75 0x4B 'K' */
	0x44, /* 010001 */
	0x48, /* 010010 */
	0x50, /* 010100 */
	0x60, /* 011000 */
	0x50, /* 010100 */
	0x48, /* 010010 */
	0x44, /* 010001 */
	0x00, /* 000000 */

	/* 76 0x4C 'L' */
	0x40, /* 010000 */
	0x40, /* 010000 */
	0x40, /* 010000 */
	0x40, /* 010000 */
	0x40, /* 010000 */
	0x40, /* 010000 */
	0x7C, /* 011111 */
	0x00, /* 000000 */

	/* 77 0x4D 'M' */
	0x44, /* 010001 */
	0x6C, /* 011011 */
	0x54, /* 010101 */
	0x54, /* 010101 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x00, /* 000000 */

	/* 78 0x4E 'N' */
	0x44, /* 010001 */
	0x64, /* 011001 */
	0x54, /* 010101 */
	0x4C, /* 010011 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x00, /* 000000 */

	/* 79 0x4F 'O' */
	0x38, /* 001110 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x38, /* 001110 */
	0x00, /* 000000 */

	/* 80 0x50 'P' */
	0x78, /* 011110 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x78, /* 011110 */
	0x40, /* 010000 */
	0x40, /* 010000 */
	0x40, /* 010000 */
	0x00, /* 000000 */

	/* 81 0x51 'Q' */
	0x38, /* 001110 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x54, /* 010101 */
	0x48, /* 010010 */
	0x34, /* 001101 */
	0x00, /* 000000 */

	/* 82 0x52 'R' */
	0x78, /* 011110 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x78, /* 011110 */
	0x50, /* 010100 */
	0x48, /* 010010 */
	0x44, /* 010001 */
	0x00, /* 000000 */

	/* 83 0x53 'S' */
	0x38, /* 001110 */
	0x44, /* 010001 */
	0x40, /* 010000 */
	0x38, /* 001110 */
	0x04, /* 000001 */
	0x44, /* 010001 */
	0x38, /* 001110 */
	0x00, /* 000000 */

	/* 84 0x54 'T' */
	0x7C, /* 011111 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x00, /* 000000 */

	/* 85 0x55 'U' */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x38, /* 001110 */
	0x00, /* 000000 */

	/* 86 0x56 'V' */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x28, /* 001010 */
	0x10, /* 000100 */
	0x00, /* 000000 */

	/* 87 0x57 'W' */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x54, /* 010101 */
	0x54, /* 010101 */
	0x6C, /* 011011 */
	0x44, /* 010001 */
	0x00, /* 000000 */

	/* 88 0x58 'X' */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x28, /* 001010 */
	0x10, /* 000100 */
	0x28, /* 001010 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x00, /* 000000 */

	/* 89 0x59 'Y' */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x28, /* 001010 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x00, /* 000000 */

	/* 90 0x5A 'Z' */
	0x7C, /* 011111 */
	0x04, /* 000001 */
	0x08, /* 000010 */
	0x10, /* 000100 */
	0x20, /* 001000 */
	0x40, /* 010000 */
	0x7C, /* 011111 */
	0x00, /* 000000 */

	/* 91 0x5B '[' */
	0x18, /* 000110 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x18, /* 000110 */
	0x00, /* 000000 */

	/* 92 0x5C '\' */
	0x40, /* 010000 */
	0x20, /* 001000 */
	0x20, /* 001000 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x08, /* 000010 */
	0x08, /* 000010 */
	0x04, /* 000001 */

	/* 93 0x5D ']' */
	0x30, /* 001100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x30, /* 001100 */
	0x00, /* 000000 */

	/* 94 0x5E '^' */
	0x10, /* 000100 */
	0x28, /* 001010 */
	0x44, /* 010001 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */

	/* 95 0x5F '_' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x7C, /* 011111 */

	/* 96 0x60 '`' */
	0x20, /* 001000 */
	0x10, /* 000100 */
	0x08, /* 000010 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */

	/* 97 0x61 'a' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x38, /* 001110 */
	0x04, /* 000001 */
	0x3C, /* 001111 */
	0x44, /* 010001 */
	0x3C, /* 001111 */
	0x00, /* 000000 */

	/* 98 0x62 'b' */
	0x40, /* 010000 */
	0x40, /* 010000 */
	0x58, /* 010110 */
	0x64, /* 011001 */
	0x44, /* 010001 */
	0x64, /* 011001 */
	0x58, /* 010110 */
	0x00, /* 000000 */

	/* 99 0x63 'c' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x38, /* 001110 */
	0x44, /* 010001 */
	0x40, /* 010000 */
	0x44, /* 010001 */
	0x38, /* 001110 */
	0x00, /* 000000 */

	/* 100 0x64 'd' */
	0x04, /* 000001 */
	0x04, /* 000001 */
	0x34, /* 001101 */
	0x4C, /* 010011 */
	0x44, /* 010001 */
	0x4C, /* 010011 */
	0x34, /* 001101 */
	0x00, /* 000000 */

	/* 101 0x65 'e' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x38, /* 001110 */
	0x44, /* 010001 */
	0x7C, /* 011111 */
	0x40, /* 010000 */
	0x3C, /* 001111 */
	0x00, /* 000000 */

	/* 102 0x66 'f' */
	0x0C, /* 000011 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x38, /* 001110 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x00, /* 000000 */

	/* 103 0x67 'g' */
	0x00, /* 000000 */
	0x34, /* 001101 */
	0x4C, /* 010011 */
	0x44, /* 010001 */
	0x4C, /* 010011 */
	0x34, /* 001101 */
	0x04, /* 000001 */
	0x38, /* 001110 */

	/* 104 0x68 'h' */
	0x40, /* 010000 */
	0x40, /* 010000 */
	0x78, /* 011110 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x00, /* 000000 */

	/* 105 0x69 'i' */
	0x10, /* 000100 */
	0x00, /* 000000 */
	0x30, /* 001100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x38, /* 001110 */
	0x00, /* 000000 */

	/* 106 0x6A 'j' */
	0x10, /* 000100 */
	0x00, /* 000000 */
	0x30, /* 001100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x60, /* 011000 */

	/* 107 0x6B 'k' */
	0x40, /* 010000 */
	0x40, /* 010000 */
	0x48, /* 010010 */
	0x50, /* 010100 */
	0x70, /* 011100 */
	0x48, /* 010010 */
	0x44, /* 010001 */
	0x00, /* 000000 */

	/* 108 0x6C 'l' */
	0x30, /* 001100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x38, /* 001110 */
	0x00, /* 000000 */

	/* 109 0x6D 'm' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x68, /* 011010 */
	0x54, /* 010101 */
	0x54, /* 010101 */
	0x54, /* 010101 */
	0x54, /* 010101 */
	0x00, /* 000000 */

	/* 110 0x6E 'n' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x58, /* 010110 */
	0x64, /* 011001 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x00, /* 000000 */

	/* 111 0x6F 'o' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x38, /* 001110 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x38, /* 001110 */
	0x00, /* 000000 */

	/* 112 0x70 'p' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x78, /* 011110 */
	0x44, /* 010001 */
	0x64, /* 011001 */
	0x58, /* 010110 */
	0x40, /* 010000 */
	0x40, /* 010000 */

	/* 113 0x71 'q' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x3C, /* 001111 */
	0x44, /* 010001 */
	0x4C, /* 010011 */
	0x34, /* 001101 */
	0x04, /* 000001 */
	0x04, /* 000001 */

	/* 114 0x72 'r' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x58, /* 010110 */
	0x64, /* 011001 */
	0x40, /* 010000 */
	0x40, /* 010000 */
	0x40, /* 010000 */
	0x00, /* 000000 */

	/* 115 0x73 's' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x3C, /* 001111 */
	0x40, /* 010000 */
	0x38, /* 001110 */
	0x04, /* 000001 */
	0x78, /* 011110 */
	0x00, /* 000000 */

	/* 116 0x74 't' */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x38, /* 001110 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x0C, /* 000011 */
	0x00, /* 000000 */

	/* 117 0x75 'u' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x4C, /* 010011 */
	0x34, /* 001101 */
	0x00, /* 000000 */

	/* 118 0x76 'v' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x28, /* 001010 */
	0x10, /* 000100 */
	0x00, /* 000000 */

	/* 119 0x77 'w' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x54, /* 010101 */
	0x54, /* 010101 */
	0x54, /* 010101 */
	0x54, /* 010101 */
	0x28, /* 001010 */
	0x00, /* 000000 */

	/* 120 0x78 'x' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x44, /* 010001 */
	0x28, /* 001010 */
	0x10, /* 000100 */
	0x28, /* 001010 */
	0x44, /* 010001 */
	0x00, /* 000000 */

	/* 121 0x79 'y' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x3C, /* 001111 */
	0x04, /* 000001 */
	0x38, /* 001110 */

	/* 122 0x7A 'z' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x7C, /* 011111 */
	0x08, /* 000010 */
	0x10, /* 000100 */
	0x20, /* 001000 */
	0x7C, /* 011111 */
	0x00, /* 000000 */

	/* 123 0x7B '{' */
	0x08, /* 000010 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x20, /* 001000 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x08, /* 000010 */
	0x00, /* 000000 */

	/* 124 0x7C '|' */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x00, /* 000000 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x00, /* 000000 */

	/* 125 0x7D '}' */
	0x20, /* 001000 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x08, /* 000010 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x20, /* 001000 */
	0x00, /* 000000 */

	/* 126 0x7E '~' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x20, /* 001000 */
	0x54, /* 010101 */
	0x08, /* 000010 */
	0x00, /* 000000 */
	0x00, /* 000000 */

	/* 127 0x7F '' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x10, /* 000100 */
	0x28, /* 001010 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x7C, /* 011111 */
	0x00, /* 000000 */

	/* 128 0x80 '\200' */
	0x00, /* 000000 */
	0x38, /* 001110 */
	0x44, /* 010001 */
	0x40, /* 010000 */
	0x44, /* 010001 */
	0x38, /* 001110 */
	0x10, /* 000100 */
	0x20, /* 001000 */

	/* 129 0x81 '\201' */
	0x00, /* 000000 */
	0x28, /* 001010 */
	0x00, /* 000000 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x4C, /* 010011 */
	0x34, /* 001101 */
	0x00, /* 000000 */

	/* 130 0x82 '\202' */
	0x18, /* 000110 */
	0x00, /* 000000 */
	0x38, /* 001110 */
	0x44, /* 010001 */
	0x7C, /* 011111 */
	0x40, /* 010000 */
	0x3C, /* 001111 */
	0x00, /* 000000 */

	/* 131 0x83 '\203' */
	0x18, /* 000110 */
	0x00, /* 000000 */
	0x38, /* 001110 */
	0x04, /* 000001 */
	0x3C, /* 001111 */
	0x44, /* 010001 */
	0x3C, /* 001111 */
	0x00, /* 000000 */

	/* 132 0x84 '\204' */
	0x28, /* 001010 */
	0x00, /* 000000 */
	0x38, /* 001110 */
	0x04, /* 000001 */
	0x3C, /* 001111 */
	0x44, /* 010001 */
	0x3C, /* 001111 */
	0x00, /* 000000 */

	/* 133 0x85 '\205' */
	0x18, /* 000110 */
	0x00, /* 000000 */
	0x38, /* 001110 */
	0x04, /* 000001 */
	0x3C, /* 001111 */
	0x44, /* 010001 */
	0x3C, /* 001111 */
	0x00, /* 000000 */

	/* 134 0x86 '\206' */
	0x3C, /* 001111 */
	0x18, /* 000110 */
	0x38, /* 001110 */
	0x04, /* 000001 */
	0x3C, /* 001111 */
	0x44, /* 010001 */
	0x3C, /* 001111 */
	0x00, /* 000000 */

	/* 135 0x87 '\207' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x38, /* 001110 */
	0x44, /* 010001 */
	0x40, /* 010000 */
	0x44, /* 010001 */
	0x38, /* 001110 */
	0x10, /* 000100 */

	/* 136 0x88 '\210' */
	0x18, /* 000110 */
	0x00, /* 000000 */
	0x38, /* 001110 */
	0x44, /* 010001 */
	0x7C, /* 011111 */
	0x40, /* 010000 */
	0x3C, /* 001111 */
	0x00, /* 000000 */

	/* 137 0x89 '\211' */
	0x28, /* 001010 */
	0x00, /* 000000 */
	0x38, /* 001110 */
	0x44, /* 010001 */
	0x7C, /* 011111 */
	0x40, /* 010000 */
	0x3C, /* 001111 */
	0x00, /* 000000 */

	/* 138 0x8A '\212' */
	0x18, /* 000110 */
	0x00, /* 000000 */
	0x38, /* 001110 */
	0x44, /* 010001 */
	0x7C, /* 011111 */
	0x40, /* 010000 */
	0x3C, /* 001111 */
	0x00, /* 000000 */

	/* 139 0x8B '\213' */
	0x28, /* 001010 */
	0x00, /* 000000 */
	0x30, /* 001100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x38, /* 001110 */
	0x00, /* 000000 */

	/* 140 0x8C '\214' */
	0x18, /* 000110 */
	0x00, /* 000000 */
	0x30, /* 001100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x38, /* 001110 */
	0x00, /* 000000 */

	/* 141 0x8D '\215' */
	0x18, /* 000110 */
	0x00, /* 000000 */
	0x30, /* 001100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x38, /* 001110 */
	0x00, /* 000000 */

	/* 142 0x8E '\216' */
	0x44, /* 010001 */
	0x10, /* 000100 */
	0x28, /* 001010 */
	0x44, /* 010001 */
	0x7C, /* 011111 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x00, /* 000000 */

	/* 143 0x8F '\217' */
	0x30, /* 001100 */
	0x48, /* 010010 */
	0x38, /* 001110 */
	0x44, /* 010001 */
	0x7C, /* 011111 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x00, /* 000000 */

	/* 144 0x90 '\220' */
	0x10, /* 000100 */
	0x7C, /* 011111 */
	0x40, /* 010000 */
	0x78, /* 011110 */
	0x40, /* 010000 */
	0x40, /* 010000 */
	0x7C, /* 011111 */
	0x00, /* 000000 */

	/* 145 0x91 '\221' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x78, /* 011110 */
	0x14, /* 000101 */
	0x7C, /* 011111 */
	0x50, /* 010100 */
	0x3C, /* 001111 */
	0x00, /* 000000 */

	/* 146 0x92 '\222' */
	0x3C, /* 001111 */
	0x50, /* 010100 */
	0x50, /* 010100 */
	0x78, /* 011110 */
	0x50, /* 010100 */
	0x50, /* 010100 */
	0x5C, /* 010111 */
	0x00, /* 000000 */

	/* 147 0x93 '\223' */
	0x18, /* 000110 */
	0x00, /* 000000 */
	0x38, /* 001110 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x38, /* 001110 */
	0x00, /* 000000 */

	/* 148 0x94 '\224' */
	0x28, /* 001010 */
	0x00, /* 000000 */
	0x38, /* 001110 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x38, /* 001110 */
	0x00, /* 000000 */

	/* 149 0x95 '\225' */
	0x18, /* 000110 */
	0x00, /* 000000 */
	0x38, /* 001110 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x38, /* 001110 */
	0x00, /* 000000 */

	/* 150 0x96 '\226' */
	0x10, /* 000100 */
	0x28, /* 001010 */
	0x00, /* 000000 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x4C, /* 010011 */
	0x34, /* 001101 */
	0x00, /* 000000 */

	/* 151 0x97 '\227' */
	0x20, /* 001000 */
	0x10, /* 000100 */
	0x00, /* 000000 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x4C, /* 010011 */
	0x34, /* 001101 */
	0x00, /* 000000 */

	/* 152 0x98 '\230' */
	0x28, /* 001010 */
	0x00, /* 000000 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x3C, /* 001111 */
	0x04, /* 000001 */
	0x38, /* 001110 */

	/* 153 0x99 '\231' */
	0x84, /* 100001 */
	0x38, /* 001110 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x38, /* 001110 */
	0x00, /* 000000 */

	/* 154 0x9A '\232' */
	0x88, /* 100010 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x38, /* 001110 */
	0x00, /* 000000 */

	/* 155 0x9B '\233' */
	0x10, /* 000100 */
	0x38, /* 001110 */
	0x54, /* 010101 */
	0x50, /* 010100 */
	0x54, /* 010101 */
	0x38, /* 001110 */
	0x10, /* 000100 */
	0x00, /* 000000 */

	/* 156 0x9C '\234' */
	0x30, /* 001100 */
	0x48, /* 010010 */
	0x40, /* 010000 */
	0x70, /* 011100 */
	0x40, /* 010000 */
	0x44, /* 010001 */
	0x78, /* 011110 */
	0x00, /* 000000 */

	/* 157 0x9D '\235' */
	0x44, /* 010001 */
	0x28, /* 001010 */
	0x7C, /* 011111 */
	0x10, /* 000100 */
	0x7C, /* 011111 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x00, /* 000000 */

	/* 158 0x9E '\236' */
	0x70, /* 011100 */
	0x48, /* 010010 */
	0x70, /* 011100 */
	0x48, /* 010010 */
	0x5C, /* 010111 */
	0x48, /* 010010 */
	0x44, /* 010001 */
	0x00, /* 000000 */

	/* 159 0x9F '\237' */
	0x0C, /* 000011 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x38, /* 001110 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x60, /* 011000 */
	0x00, /* 000000 */

	/* 160 0xA0 '\240' */
	0x18, /* 000110 */
	0x00, /* 000000 */
	0x38, /* 001110 */
	0x04, /* 000001 */
	0x3C, /* 001111 */
	0x44, /* 010001 */
	0x3C, /* 001111 */
	0x00, /* 000000 */

	/* 161 0xA1 '\241' */
	0x08, /* 000010 */
	0x10, /* 000100 */
	0x00, /* 000000 */
	0x30, /* 001100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x38, /* 001110 */
	0x00, /* 000000 */

	/* 162 0xA2 '\242' */
	0x08, /* 000010 */
	0x10, /* 000100 */
	0x00, /* 000000 */
	0x38, /* 001110 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x38, /* 001110 */
	0x00, /* 000000 */

	/* 163 0xA3 '\243' */
	0x08, /* 000010 */
	0x10, /* 000100 */
	0x00, /* 000000 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x4C, /* 010011 */
	0x34, /* 001101 */
	0x00, /* 000000 */

	/* 164 0xA4 '\244' */
	0x34, /* 001101 */
	0x58, /* 010110 */
	0x00, /* 000000 */
	0x58, /* 010110 */
	0x64, /* 011001 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x00, /* 000000 */

	/* 165 0xA5 '\245' */
	0x58, /* 010110 */
	0x44, /* 010001 */
	0x64, /* 011001 */
	0x54, /* 010101 */
	0x4C, /* 010011 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x00, /* 000000 */

	/* 166 0xA6 '\246' */
	0x38, /* 001110 */
	0x04, /* 000001 */
	0x3C, /* 001111 */
	0x44, /* 010001 */
	0x3C, /* 001111 */
	0x00, /* 000000 */
	0x7C, /* 011111 */
	0x00, /* 000000 */

	/* 167 0xA7 '\247' */
	0x38, /* 001110 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x38, /* 001110 */
	0x00, /* 000000 */
	0x7C, /* 011111 */
	0x00, /* 000000 */

	/* 168 0xA8 '\250' */
	0x10, /* 000100 */
	0x00, /* 000000 */
	0x10, /* 000100 */
	0x20, /* 001000 */
	0x40, /* 010000 */
	0x44, /* 010001 */
	0x38, /* 001110 */
	0x00, /* 000000 */

	/* 169 0xA9 '\251' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x7C, /* 011111 */
	0x40, /* 010000 */
	0x40, /* 010000 */
	0x00, /* 000000 */
	0x00, /* 000000 */

	/* 170 0xAA '\252' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x7C, /* 011111 */
	0x04, /* 000001 */
	0x04, /* 000001 */
	0x00, /* 000000 */
	0x00, /* 000000 */

	/* 171 0xAB '\253' */
	0x20, /* 001000 */
	0x24, /* 001001 */
	0x28, /* 001010 */
	0x10, /* 000100 */
	0x28, /* 001010 */
	0x44, /* 010001 */
	0x08, /* 000010 */
	0x1C, /* 000111 */

	/* 172 0xAC '\254' */
	0x20, /* 001000 */
	0x24, /* 001001 */
	0x28, /* 001010 */
	0x10, /* 000100 */
	0x28, /* 001010 */
	0x58, /* 010110 */
	0x3C, /* 001111 */
	0x08, /* 000010 */

	/* 173 0xAD '\255' */
	0x10, /* 000100 */
	0x00, /* 000000 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x00, /* 000000 */

	/* 174 0xAE '\256' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x24, /* 001001 */
	0x48, /* 010010 */
	0x90, /* 100100 */
	0x48, /* 010010 */
	0x24, /* 001001 */
	0x00, /* 000000 */

	/* 175 0xAF '\257' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x90, /* 100100 */
	0x48, /* 010010 */
	0x24, /* 001001 */
	0x48, /* 010010 */
	0x90, /* 100100 */
	0x00, /* 000000 */

	/* 176 0xB0 '\260' */
	0x10, /* 000100 */
	0x44, /* 010001 */
	0x10, /* 000100 */
	0x44, /* 010001 */
	0x10, /* 000100 */
	0x44, /* 010001 */
	0x10, /* 000100 */
	0x44, /* 010001 */

	/* 177 0xB1 '\261' */
	0xA8, /* 101010 */
	0x54, /* 010101 */
	0xA8, /* 101010 */
	0x54, /* 010101 */
	0xA8, /* 101010 */
	0x54, /* 010101 */
	0xA8, /* 101010 */
	0x54, /* 010101 */

	/* 178 0xB2 '\262' */
	0xDC, /* 110111 */
	0x74, /* 011101 */
	0xDC, /* 110111 */
	0x74, /* 011101 */
	0xDC, /* 110111 */
	0x74, /* 011101 */
	0xDC, /* 110111 */
	0x74, /* 011101 */

	/* 179 0xB3 '\263' */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */

	/* 180 0xB4 '\264' */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0xF0, /* 111100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */

	/* 181 0xB5 '\265' */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0xF0, /* 111100 */
	0x10, /* 000100 */
	0xF0, /* 111100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */

	/* 182 0xB6 '\266' */
	0x28, /* 001010 */
	0x28, /* 001010 */
	0x28, /* 001010 */
	0xE8, /* 111010 */
	0x28, /* 001010 */
	0x28, /* 001010 */
	0x28, /* 001010 */
	0x28, /* 001010 */

	/* 183 0xB7 '\267' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0xF8, /* 111110 */
	0x28, /* 001010 */
	0x28, /* 001010 */
	0x28, /* 001010 */
	0x28, /* 001010 */

	/* 184 0xB8 '\270' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0xF0, /* 111100 */
	0x10, /* 000100 */
	0xF0, /* 111100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */

	/* 185 0xB9 '\271' */
	0x28, /* 001010 */
	0x28, /* 001010 */
	0xE8, /* 111010 */
	0x08, /* 000010 */
	0xE8, /* 111010 */
	0x28, /* 001010 */
	0x28, /* 001010 */
	0x28, /* 001010 */

	/* 186 0xBA '\272' */
	0x28, /* 001010 */
	0x28, /* 001010 */
	0x28, /* 001010 */
	0x28, /* 001010 */
	0x28, /* 001010 */
	0x28, /* 001010 */
	0x28, /* 001010 */
	0x28, /* 001010 */

	/* 187 0xBB '\273' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0xF8, /* 111110 */
	0x08, /* 000010 */
	0xE8, /* 111010 */
	0x28, /* 001010 */
	0x28, /* 001010 */
	0x28, /* 001010 */

	/* 188 0xBC '\274' */
	0x28, /* 001010 */
	0x28, /* 001010 */
	0xE8, /* 111010 */
	0x08, /* 000010 */
	0xF8, /* 111110 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */

	/* 189 0xBD '\275' */
	0x28, /* 001010 */
	0x28, /* 001010 */
	0x28, /* 001010 */
	0xF8, /* 111110 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */

	/* 190 0xBE '\276' */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0xF0, /* 111100 */
	0x10, /* 000100 */
	0xF0, /* 111100 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */

	/* 191 0xBF '\277' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0xF0, /* 111100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */

	/* 192 0xC0 '\300' */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x1C, /* 000111 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */

	/* 193 0xC1 '\301' */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0xFC, /* 111111 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */

	/* 194 0xC2 '\302' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0xFC, /* 111111 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */

	/* 195 0xC3 '\303' */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x1C, /* 000111 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */

	/* 196 0xC4 '\304' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0xFC, /* 111111 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */

	/* 197 0xC5 '\305' */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0xFC, /* 111111 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */

	/* 198 0xC6 '\306' */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x1C, /* 000111 */
	0x10, /* 000100 */
	0x1C, /* 000111 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */

	/* 199 0xC7 '\307' */
	0x28, /* 001010 */
	0x28, /* 001010 */
	0x28, /* 001010 */
	0x2C, /* 001011 */
	0x28, /* 001010 */
	0x28, /* 001010 */
	0x28, /* 001010 */
	0x28, /* 001010 */

	/* 200 0xC8 '\310' */
	0x28, /* 001010 */
	0x28, /* 001010 */
	0x2C, /* 001011 */
	0x20, /* 001000 */
	0x3C, /* 001111 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */

	/* 201 0xC9 '\311' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x3C, /* 001111 */
	0x20, /* 001000 */
	0x2C, /* 001011 */
	0x28, /* 001010 */
	0x28, /* 001010 */
	0x28, /* 001010 */

	/* 202 0xCA '\312' */
	0x28, /* 001010 */
	0x28, /* 001010 */
	0xEC, /* 111011 */
	0x00, /* 000000 */
	0xFC, /* 111111 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */

	/* 203 0xCB '\313' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0xFC, /* 111111 */
	0x00, /* 000000 */
	0xEC, /* 111011 */
	0x28, /* 001010 */
	0x28, /* 001010 */
	0x28, /* 001010 */

	/* 204 0xCC '\314' */
	0x28, /* 001010 */
	0x28, /* 001010 */
	0x2C, /* 001011 */
	0x20, /* 001000 */
	0x2C, /* 001011 */
	0x28, /* 001010 */
	0x28, /* 001010 */
	0x28, /* 001010 */

	/* 205 0xCD '\315' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0xFC, /* 111111 */
	0x00, /* 000000 */
	0xFC, /* 111111 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */

	/* 206 0xCE '\316' */
	0x28, /* 001010 */
	0x28, /* 001010 */
	0xEC, /* 111011 */
	0x00, /* 000000 */
	0xEC, /* 111011 */
	0x28, /* 001010 */
	0x28, /* 001010 */
	0x28, /* 001010 */

	/* 207 0xCF '\317' */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0xFC, /* 111111 */
	0x00, /* 000000 */
	0xFC, /* 111111 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */

	/* 208 0xD0 '\320' */
	0x28, /* 001010 */
	0x28, /* 001010 */
	0x28, /* 001010 */
	0xFC, /* 111111 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */

	/* 209 0xD1 '\321' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0xFC, /* 111111 */
	0x00, /* 000000 */
	0xFC, /* 111111 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */

	/* 210 0xD2 '\322' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0xFC, /* 111111 */
	0x28, /* 001010 */
	0x28, /* 001010 */
	0x28, /* 001010 */
	0x28, /* 001010 */

	/* 211 0xD3 '\323' */
	0x28, /* 001010 */
	0x28, /* 001010 */
	0x28, /* 001010 */
	0x3C, /* 001111 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */

	/* 212 0xD4 '\324' */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x1C, /* 000111 */
	0x10, /* 000100 */
	0x1C, /* 000111 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */

	/* 213 0xD5 '\325' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x1C, /* 000111 */
	0x10, /* 000100 */
	0x1C, /* 000111 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */

	/* 214 0xD6 '\326' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x3C, /* 001111 */
	0x28, /* 001010 */
	0x28, /* 001010 */
	0x28, /* 001010 */
	0x28, /* 001010 */

	/* 215 0xD7 '\327' */
	0x28, /* 001010 */
	0x28, /* 001010 */
	0x28, /* 001010 */
	0xFC, /* 111111 */
	0x28, /* 001010 */
	0x28, /* 001010 */
	0x28, /* 001010 */
	0x28, /* 001010 */

	/* 216 0xD8 '\330' */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0xFC, /* 111111 */
	0x10, /* 000100 */
	0xFC, /* 111111 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */

	/* 217 0xD9 '\331' */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0xF0, /* 111100 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */

	/* 218 0xDA '\332' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x1C, /* 000111 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */

	/* 219 0xDB '\333' */
	0xFC, /* 111111 */
	0xFC, /* 111111 */
	0xFC, /* 111111 */
	0xFC, /* 111111 */
	0xFC, /* 111111 */
	0xFC, /* 111111 */
	0xFC, /* 111111 */
	0xFC, /* 111111 */

	/* 220 0xDC '\334' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0xFC, /* 111111 */
	0xFC, /* 111111 */
	0xFC, /* 111111 */
	0xFC, /* 111111 */

	/* 221 0xDD '\335' */
	0xE0, /* 111000 */
	0xE0, /* 111000 */
	0xE0, /* 111000 */
	0xE0, /* 111000 */
	0xE0, /* 111000 */
	0xE0, /* 111000 */
	0xE0, /* 111000 */
	0xE0, /* 111000 */

	/* 222 0xDE '\336' */
	0x1C, /* 000111 */
	0x1C, /* 000111 */
	0x1C, /* 000111 */
	0x1C, /* 000111 */
	0x1C, /* 000111 */
	0x1C, /* 000111 */
	0x1C, /* 000111 */
	0x1C, /* 000111 */

	/* 223 0xDF '\337' */
	0xFC, /* 111111 */
	0xFC, /* 111111 */
	0xFC, /* 111111 */
	0xFC, /* 111111 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */

	/* 224 0xE0 '\340' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x34, /* 001101 */
	0x48, /* 010010 */
	0x48, /* 010010 */
	0x48, /* 010010 */
	0x34, /* 001101 */
	0x00, /* 000000 */

	/* 225 0xE1 '\341' */
	0x24, /* 001001 */
	0x44, /* 010001 */
	0x48, /* 010010 */
	0x48, /* 010010 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x58, /* 010110 */
	0x40, /* 010000 */

	/* 226 0xE2 '\342' */
	0x7C, /* 011111 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x40, /* 010000 */
	0x40, /* 010000 */
	0x40, /* 010000 */
	0x40, /* 010000 */
	0x00, /* 000000 */

	/* 227 0xE3 '\343' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x7C, /* 011111 */
	0x28, /* 001010 */
	0x28, /* 001010 */
	0x28, /* 001010 */
	0x28, /* 001010 */
	0x00, /* 000000 */

	/* 228 0xE4 '\344' */
	0x7C, /* 011111 */
	0x24, /* 001001 */
	0x10, /* 000100 */
	0x08, /* 000010 */
	0x10, /* 000100 */
	0x24, /* 001001 */
	0x7C, /* 011111 */
	0x00, /* 000000 */

	/* 229 0xE5 '\345' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x3C, /* 001111 */
	0x48, /* 010010 */
	0x48, /* 010010 */
	0x48, /* 010010 */
	0x30, /* 001100 */
	0x00, /* 000000 */

	/* 230 0xE6 '\346' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x48, /* 010010 */
	0x48, /* 010010 */
	0x48, /* 010010 */
	0x48, /* 010010 */
	0x74, /* 011101 */
	0x40, /* 010000 */

	/* 231 0xE7 '\347' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x7C, /* 011111 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x0C, /* 000011 */
	0x00, /* 000000 */

	/* 232 0xE8 '\350' */
	0x10, /* 000100 */
	0x38, /* 001110 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x38, /* 001110 */
	0x10, /* 000100 */
	0x38, /* 001110 */
	0x00, /* 000000 */

	/* 233 0xE9 '\351' */
	0x38, /* 001110 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x7C, /* 011111 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x38, /* 001110 */
	0x00, /* 000000 */

	/* 234 0xEA '\352' */
	0x38, /* 001110 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x28, /* 001010 */
	0x6C, /* 011011 */
	0x00, /* 000000 */

	/* 235 0xEB '\353' */
	0x18, /* 000110 */
	0x20, /* 001000 */
	0x18, /* 000110 */
	0x24, /* 001001 */
	0x24, /* 001001 */
	0x24, /* 001001 */
	0x18, /* 000110 */
	0x00, /* 000000 */

	/* 236 0xEC '\354' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x38, /* 001110 */
	0x54, /* 010101 */
	0x54, /* 010101 */
	0x54, /* 010101 */
	0x38, /* 001110 */
	0x00, /* 000000 */

	/* 237 0xED '\355' */
	0x00, /* 000000 */
	0x04, /* 000001 */
	0x38, /* 001110 */
	0x54, /* 010101 */
	0x54, /* 010101 */
	0x38, /* 001110 */
	0x40, /* 010000 */
	0x00, /* 000000 */

	/* 238 0xEE '\356' */
	0x3C, /* 001111 */
	0x40, /* 010000 */
	0x40, /* 010000 */
	0x38, /* 001110 */
	0x40, /* 010000 */
	0x40, /* 010000 */
	0x3C, /* 001111 */
	0x00, /* 000000 */

	/* 239 0xEF '\357' */
	0x38, /* 001110 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x44, /* 010001 */
	0x00, /* 000000 */

	/* 240 0xF0 '\360' */
	0x00, /* 000000 */
	0xFC, /* 111111 */
	0x00, /* 000000 */
	0xFC, /* 111111 */
	0x00, /* 000000 */
	0xFC, /* 111111 */
	0x00, /* 000000 */
	0x00, /* 000000 */

	/* 241 0xF1 '\361' */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x7C, /* 011111 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x00, /* 000000 */
	0x7C, /* 011111 */
	0x00, /* 000000 */

	/* 242 0xF2 '\362' */
	0x20, /* 001000 */
	0x10, /* 000100 */
	0x08, /* 000010 */
	0x10, /* 000100 */
	0x20, /* 001000 */
	0x00, /* 000000 */
	0x38, /* 001110 */
	0x00, /* 000000 */

	/* 243 0xF3 '\363' */
	0x08, /* 000010 */
	0x10, /* 000100 */
	0x20, /* 001000 */
	0x10, /* 000100 */
	0x08, /* 000010 */
	0x00, /* 000000 */
	0x38, /* 001110 */
	0x00, /* 000000 */

	/* 244 0xF4 '\364' */
	0x0C, /* 000011 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */

	/* 245 0xF5 '\365' */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x10, /* 000100 */
	0x60, /* 011000 */

	/* 246 0xF6 '\366' */
	0x00, /* 000000 */
	0x10, /* 000100 */
	0x00, /* 000000 */
	0x7C, /* 011111 */
	0x00, /* 000000 */
	0x10, /* 000100 */
	0x00, /* 000000 */
	0x00, /* 000000 */

	/* 247 0xF7 '\367' */
	0x00, /* 000000 */
	0x20, /* 001000 */
	0x54, /* 010101 */
	0x08, /* 000010 */
	0x20, /* 001000 */
	0x54, /* 010101 */
	0x08, /* 000010 */
	0x00, /* 000000 */

	/* 248 0xF8 '\370' */
	0x30, /* 001100 */
	0x48, /* 010010 */
	0x48, /* 010010 */
	0x30, /* 001100 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */

	/* 249 0xF9 '\371' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x10, /* 000100 */
	0x38, /* 001110 */
	0x10, /* 000100 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */

	/* 250 0xFA '\372' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x10, /* 000100 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */

	/* 251 0xFB '\373' */
	0x04, /* 000001 */
	0x08, /* 000010 */
	0x08, /* 000010 */
	0x50, /* 010100 */
	0x50, /* 010100 */
	0x20, /* 001000 */
	0x20, /* 001000 */
	0x00, /* 000000 */

	/* 252 0xFC '\374' */
	0x60, /* 011000 */
	0x50, /* 010100 */
	0x50, /* 010100 */
	0x50, /* 010100 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */

	/* 253 0xFD '\375' */
	0x60, /* 011000 */
	0x10, /* 000100 */
	0x20, /* 001000 */
	0x70, /* 011100 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */

	/* 254 0xFE '\376' */
	0x00, /* 000000 */
	0x38, /* 001110 */
	0x38, /* 001110 */
	0x38, /* 001110 */
	0x38, /* 001110 */
	0x38, /* 001110 */
	0x38, /* 001110 */
	0x00, /* 000000 */

	/* 255 0xFF '\377' */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
	0x00, /* 000000 */
} };

const struct font_desc font_6x8 = {
	.idx	= FONT6x8_IDX,
	.name	= "6x8",
	.width	= 6,
	.height	= 8,
	.charcount = 256,
	.data	= fontdata_6x8.data,
	.pref	= 0,
};
