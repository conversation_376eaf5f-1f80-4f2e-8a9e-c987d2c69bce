
/* Hand composed "Minuscule" 4x6 font, with binary data generated using
 * Perl stub.
 *
 * Use 'perl -x mini_4x6.c < mini_4x6.c > new_version.c' to regenerate
 * binary data.
 *
 * Created by <PERSON>.
 * No rights reserved, released to the public domain.
 *
 * Version 1.0
 */

/*

#!/usr/bin/perl -pn

s{((0x)?[0-9a-fA-F]+)(.*\[([\*\ ]{4})\])}{

	($num,$pat,$bits) = ($1,$3,$4);
	
	$bits =~ s/([^\s0])|(.)/ defined($1) + 0 /ge;
	
	$num = ord(pack("B8", $bits));
	$num |= $num >> 4;
	$num = sprintf("0x%.2x", $num);
	
	#print "$num,$pat,$bits\n";
	
	$num . $pat;
}ge;

__END__;
*/

/* Note: binary data consists of one byte for each row of each character top
   to bottom, character 0 to character 255, six bytes per character. Each
   byte contains the same four character bits in both nybbles.
   MSBit to LSBit = left to right.
 */

#include <linux/font.h>

#define FONTDATAMAX 1536

static const struct font_data fontdata_mini_4x6 = {
	{ 0, 0, FONTDATAMAX, 0 }, {
	/*{*/
	  	/*   Char 0: ' '  */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0x00,	/*=  [    ]       */
	/*}*/
	/*{*/
	  	/*   Char 1: ' '  */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0x00,	/*=  [    ]       */
	/*}*/
	/*{*/
	  	/*   Char 2: ' '  */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0x00,	/*=  [    ]       */
	/*}*/
	/*{*/
	  	/*   Char 3: ' '  */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0x00,	/*=  [    ]       */
	/*}*/
	/*{*/
	  	/*   Char 4: ' '  */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0x00,	/*=  [    ]       */
	/*}*/
	/*{*/
	  	/*   Char 5: ' '  */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0x00,	/*=  [    ]       */
	/*}*/
	/*{*/
	  	/*   Char 6: ' '  */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0x00,	/*=  [    ]       */
	/*}*/
	/*{*/
	  	/*   Char 7: ' '  */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0x00,	/*=  [    ]       */
	/*}*/
	/*{*/
	  	/*   Char 8: ' '  */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0x00,	/*=  [    ]       */
	/*}*/
	/*{*/
	  	/*   Char 9: ' '  */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0x00,	/*=  [    ]       */
	/*}*/
	/*{*/
	  	/*   Char 10: '' */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0x00,	/*=  [    ]       */
	/*}*/
	/*{*/
	  	/*   Char 11: ' ' */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0x00,	/*=  [    ]       */
	/*}*/
	/*{*/
	  	/*   Char 12: ' ' */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0x00,	/*=  [    ]       */
	/*}*/
	/*{*/
	  	/*   Char 13: ' ' */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0x00,	/*=  [    ]       */
	/*}*/
	/*{*/
	  	/*   Char 14: ' ' */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0x00,	/*=  [    ]       */
	/*}*/
	/*{*/
	  	/*   Char 15: ' ' */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0x00,	/*=  [    ]       */
	/*}*/
	/*{*/
	  	/*   Char 16: ' ' */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0x00,	/*=  [    ]       */
	/*}*/
	/*{*/
	  	/*   Char 17: ' ' */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0x00,	/*=  [    ]       */
	/*}*/
	/*{*/
	  	/*   Char 18: ' ' */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0x00,	/*=  [    ]       */
	/*}*/
	/*{*/
	  	/*   Char 19: ' ' */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0x00,	/*=  [    ]       */
	/*}*/
	/*{*/
	  	/*   Char 20: ' ' */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0x00,	/*=  [    ]       */
	/*}*/
	/*{*/
	  	/*   Char 21: ' ' */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0x00,	/*=  [    ]       */
	/*}*/
	/*{*/
	  	/*   Char 22: ' ' */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0x00,	/*=  [    ]       */
	/*}*/
	/*{*/
	  	/*   Char 23: ' ' */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0x00,	/*=  [    ]       */
	/*}*/
	/*{*/
	  	/*   Char 24: ' ' */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0x00,	/*=  [    ]       */
	/*}*/
	/*{*/
	  	/*   Char 25: ' ' */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0x00,	/*=  [    ]       */
	/*}*/
	/*{*/
	  	/*   Char 26: ' ' */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0x00,	/*=  [    ]       */
	/*}*/
	/*{*/
	  	/*   Char 27: ' ' */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0x00,	/*=  [    ]       */
	/*}*/
	/*{*/
	  	/*   Char 28: ' ' */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0x00,	/*=  [    ]       */
	/*}*/
	/*{*/
	  	/*   Char 29: ' ' */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0x00,	/*=  [    ]       */
	/*}*/
	/*{*/
	  	/*   Char 30: ' ' */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0x00,	/*=  [    ]       */
	/*}*/
	/*{*/
	  	/*   Char 31: ' ' */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0x00,	/*=  [    ]       */
	/*}*/
	/*{*/
	  	/*   Char 32: ' ' */
	0x00,	/*=  [    ]       */
	0x00,	/*=  [    ]       */
	0x00,	/*=  [    ]       */
	0x00,	/*=  [    ]       */
	0x00,	/*=  [    ]       */
	0x00,	/*=  [    ]       */
	/*}*/
	/*{*/
	  	/*   Char 33: '!' */
	0x44,	/*=  [ *  ]       */
	0x44,	/*=  [ *  ]       */
	0x44,	/*=  [ *  ]       */
	0x00,	/*=  [    ]       */
	0x44,	/*=  [ *  ]       */
	0x00,	/*=  [    ]       */
	/*}*/
	/*{*/
	  	/*   Char 34: '"' */
	0xaa,	/*=  [* * ]       */
	0xaa,	/*=  [* * ]       */
	0x00,	/*=  [    ]       */
	0x00,	/*=  [    ]       */
	0x00,	/*=  [    ]       */
	0x00,	/*=  [    ]       */
	/*}*/
	/*{*/
	  	/*   Char 35: '#' */
	0xaa,	/*=  [* * ]       */
	0xff,	/*=  [****]       */
	0xff,	/*=  [****]       */
	0xaa,	/*=  [* * ]       */
	0x00,	/*=  [    ]       */
	0x00,	/*=  [    ]       */
	/*}*/
	/*{*/
	  	/*   Char 36: '$' */
	0x44,	/*=  [ *  ]       */
	0x66,	/*=  [ ** ]       */
	0xee,	/*=  [*** ]       */
	0xcc,	/*=  [**  ]       */
	0x44,	/*=  [ *  ]       */
	0x00,	/*=  [    ]       */
	/*}*/
	/*{*/
	  	/*   Char 37: '%' */
	0xaa,	/*=  [* * ]       */
	0x22,	/*=  [  * ]       */
	0x44,	/*=  [ *  ]       */
	0x88,	/*=  [*   ]       */
	0xaa,	/*=  [* * ]       */
	0x00,	/*=  [    ]       */
	/*}*/
	/*{*/
	  	/*   Char 38: '&' */
	0x66,	/*=  [ ** ]       */
	0x99,	/*=  [*  *]       */
	0x66,	/*=  [ ** ]       */
	0xaa,	/*=  [* * ]       */
	0xdd,	/*=  [** *]       */
	0x00,	/*=  [    ]       */
	/*}*/
	/*{*/
	  	/*   Char 39: ''' */
	0x22,	/*=  [  * ]       */
	0x44,	/*=  [ *  ]       */
	0x00,	/*=  [    ]       */
	0x00,	/*=  [    ]       */
	0x00,	/*=  [    ]       */
	0x00,	/*=  [    ]       */
	/*}*/
	/*{*/
	  	/*   Char 40: '(' */
	0x22,	/*=  [  * ]       */
	0x44,	/*=  [ *  ]       */
	0x44,	/*=  [ *  ]       */
	0x44,	/*=  [ *  ]       */
	0x22,	/*=  [  * ]       */
	0x00,	/*=  [    ]       */
	/*}*/
	/*{*/
	  	/*   Char 41: ')' */
	0x44,	/*=  [ *  ]       */
	0x22,	/*=  [  * ]       */
	0x22,	/*=  [  * ]       */
	0x22,	/*=  [  * ]       */
	0x44,	/*=  [ *  ]       */
	0x00,	/*=  [    ]       */
	/*}*/
	/*{*/
	  	/*   Char 42: '*' */
	0x00,	/*=  [    ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0xee,	/*=  [*** ]       */
	0x00,	/*=  [    ]       */
	0x00,	/*=  [    ]       */
	/*}*/
	/*{*/
	  	/*   Char 43: '+' */
	0x00,	/*=  [    ]       */
	0x44,	/*=  [ *  ]       */
	0xee,	/*=  [*** ]       */
	0x44,	/*=  [ *  ]       */
	0x00,	/*=  [    ]       */
	0x00,	/*=  [    ]       */
	/*}*/
	/*{*/
	  	/*   Char 44: ',' */
	0x00,	/*=  [    ]       */
	0x00,	/*=  [    ]       */
	0x00,	/*=  [    ]       */
	0x44,	/*=  [ *  ]       */
	0x88,	/*=  [*   ]       */
	0x00,	/*=  [    ]       */
	/*}*/
	/*{*/
	  	/*   Char 45: '-' */
	0x00,	/*=  [    ]       */
	0x00,	/*=  [    ]       */
	0xee,	/*=  [*** ]       */
	0x00,	/*=  [    ]       */
	0x00,	/*=  [    ]       */
	0x00,	/*=  [    ]       */
	/*}*/
	/*{*/
	  	/*   Char 46: '.' */
	0x00,	/*=  [    ]       */
	0x00,	/*=  [    ]       */
	0x00,	/*=  [    ]       */
	0x00,	/*=  [    ]       */
	0x44,	/*=  [ *  ]       */
	0x00,	/*=  [    ]       */
	/*}*/
	/*{*/
	  	/*   Char 47: '/' */
	0x00,	/*=  [    ]       */
	0x22,	/*=  [  * ]       */
	0x44,	/*=  [ *  ]       */
	0x88,	/*=  [*   ]       */
	0x00,	/*=  [    ]       */
	0x00,	/*=  [    ]       */
	/*}*/
	/*{*/
	  	/*   Char 48: '0'   */
	0x44,	/*=   [ *  ]        */
	0xaa,	/*=   [* * ]        */
	0xaa,	/*=   [* * ]        */
	0xaa,	/*=   [* * ]        */
	0x44,	/*=   [ *  ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/
	  	/*   Char 49: '1'   */
	0x44,	/*=   [ *  ]        */
	0xcc,	/*=   [**  ]        */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/
	  	/*   Char 50: '2'   */
	0xcc,	/*=   [**  ]        */
	0x22,	/*=   [  * ]        */
	0x44,	/*=   [ *  ]        */
	0x88,	/*=   [*   ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/
	  	/*   Char 51: '3'   */
	0xee,	/*=   [*** ]        */
	0x22,	/*=   [  * ]        */
	0x66,	/*=   [ ** ]        */
	0x22,	/*=   [  * ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 52: '4'   */
	0xaa,	/*=   [* * ]        */
	0xaa,	/*=   [* * ]        */
	0xee,	/*=   [*** ]        */
	0x22,	/*=   [  * ]        */
	0x22,	/*=   [  * ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 53: '5'   */
	0xee,	/*=   [*** ]        */
	0x88,	/*=   [*   ]        */
	0xee,	/*=   [*** ]        */
	0x22,	/*=   [  * ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 54: '6'   */
	0xee,	/*=   [*** ]        */
	0x88,	/*=   [*   ]        */
	0xee,	/*=   [*** ]        */
	0xaa,	/*=   [* * ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 55: '7'   */
	0xee,	/*=   [*** ]        */
	0x22,	/*=   [  * ]        */
	0x22,	/*=   [  * ]        */
	0x22,	/*=   [  * ]        */
	0x22,	/*=   [  * ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 56: '8'   */
	0xee,	/*=   [*** ]        */
	0xaa,	/*=   [* * ]        */
	0xee,	/*=   [*** ]        */
	0xaa,	/*=   [* * ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 57: '9'   */
	0xee,	/*=   [*** ]        */
	0xaa,	/*=   [* * ]        */
	0xee,	/*=   [*** ]        */
	0x22,	/*=   [  * ]        */
	0x22,	/*=   [  * ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 58: ':'   */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	0x44,	/*=   [ *  ]        */
	0x00,	/*=   [    ]        */
	0x44,	/*=   [ *  ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 59: ';'   */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	0x44,	/*=   [ *  ]        */
	0x00,	/*=   [    ]        */
	0x44,	/*=   [ *  ]        */
	0x88,	/*=   [*   ]        */
	/*}*/
	/*{*/ 	/*   Char 60: '<'   */
	0x22,	/*=   [  * ]        */
	0x44,	/*=   [ *  ]        */
	0x88,	/*=   [*   ]        */
	0x44,	/*=   [ *  ]        */
	0x22,	/*=   [  * ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 61: '='   */
	0x00,	/*=   [    ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 62: '>'   */
	0x88,	/*=   [*   ]        */
	0x44,	/*=   [ *  ]        */
	0x22,	/*=   [  * ]        */
	0x44,	/*=   [ *  ]        */
	0x88,	/*=   [*   ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 63: '?'   */
	0xee,	/*=   [*** ]        */
	0x22,	/*=   [  * ]        */
	0x66,	/*=   [ ** ]        */
	0x00,	/*=   [    ]        */
	0x44,	/*=   [ *  ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 64: '@'   */
	0x44,	/*=   [ *  ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x88,	/*=   [*   ]        */
	0x44,	/*=   [ *  ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 65: 'A'   */
	0x44,	/*=   [ *  ]        */
	0xaa,	/*=   [* * ]        */
	0xee,	/*=   [*** ]        */
	0xaa,	/*=   [* * ]        */
	0xaa,	/*=   [* * ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 66: 'B'   */
	0xcc,	/*=   [**  ]        */
	0xaa,	/*=   [* * ]        */
	0xcc,	/*=   [**  ]        */
	0xaa,	/*=   [* * ]        */
	0xcc,	/*=   [**  ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 67: 'C'   */
	0x66,	/*=   [ ** ]        */
	0x88,	/*=   [*   ]        */
	0x88,	/*=   [*   ]        */
	0x88,	/*=   [*   ]        */
	0x66,	/*=   [ ** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 68: 'D'   */
	0xcc,	/*=   [**  ]        */
	0xaa,	/*=   [* * ]        */
	0xaa,	/*=   [* * ]        */
	0xaa,	/*=   [* * ]        */
	0xcc,	/*=   [**  ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 69: 'E'   */
	0xee,	/*=   [*** ]        */
	0x88,	/*=   [*   ]        */
	0xee,	/*=   [*** ]        */
	0x88,	/*=   [*   ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 70: 'F'   */
	0xee,	/*=   [*** ]        */
	0x88,	/*=   [*   ]        */
	0xee,	/*=   [*** ]        */
	0x88,	/*=   [*   ]        */
	0x88,	/*=   [*   ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 71: 'G'   */
	0x66,	/*=   [ ** ]        */
	0x88,	/*=   [*   ]        */
	0xee,	/*=   [*** ]        */
	0xaa,	/*=   [* * ]        */
	0x66,	/*=   [ ** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 72: 'H'   */
	0xaa,	/*=   [* * ]        */
	0xaa,	/*=   [* * ]        */
	0xee,	/*=   [*** ]        */
	0xaa,	/*=   [* * ]        */
	0xaa,	/*=   [* * ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 73: 'I'   */
	0xee,	/*=   [*** ]        */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 74: 'J'   */
	0x22,	/*=   [  * ]        */
	0x22,	/*=   [  * ]        */
	0x22,	/*=   [  * ]        */
	0xaa,	/*=   [* * ]        */
	0x44,	/*=   [ *  ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 75: 'K'   */
	0xaa,	/*=   [* * ]        */
	0xaa,	/*=   [* * ]        */
	0xcc,	/*=   [**  ]        */
	0xaa,	/*=   [* * ]        */
	0xaa,	/*=   [* * ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 76: 'L'   */
	0x88,	/*=   [*   ]        */
	0x88,	/*=   [*   ]        */
	0x88,	/*=   [*   ]        */
	0x88,	/*=   [*   ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 77: 'M'   */
	0xaa,	/*=   [* * ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xaa,	/*=   [* * ]        */
	0xaa,	/*=   [* * ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 78: 'N'   */
	0xaa,	/*=   [* * ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xaa,	/*=   [* * ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 79: 'O'   */
	0x44,	/*=   [ *  ]        */
	0xaa,	/*=   [* * ]        */
	0xaa,	/*=   [* * ]        */
	0xaa,	/*=   [* * ]        */
	0x44,	/*=   [ *  ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 80: 'P'   */
	0xcc,	/*=   [**  ]        */
	0xaa,	/*=   [* * ]        */
	0xcc,	/*=   [**  ]        */
	0x88,	/*=   [*   ]        */
	0x88,	/*=   [*   ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 81: 'Q'   */
	0x44,	/*=   [ *  ]        */
	0xaa,	/*=   [* * ]        */
	0xaa,	/*=   [* * ]        */
	0xee,	/*=   [*** ]        */
	0x66,	/*=   [ ** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 82: 'R'   */
	0xcc,	/*=   [**  ]        */
	0xaa,	/*=   [* * ]        */
	0xee,	/*=   [*** ]        */
	0xcc,	/*=   [**  ]        */
	0xaa,	/*=   [* * ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 83: 'S'   */
	0x66,	/*=   [ ** ]        */
	0x88,	/*=   [*   ]        */
	0x44,	/*=   [ *  ]        */
	0x22,	/*=   [  * ]        */
	0xcc,	/*=   [**  ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 84: 'T'   */
	0xee,	/*=   [*** ]        */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 85: 'U'   */
	0xaa,	/*=   [* * ]        */
	0xaa,	/*=   [* * ]        */
	0xaa,	/*=   [* * ]        */
	0xaa,	/*=   [* * ]        */
	0x66,	/*=   [ ** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 86: 'V'   */
	0xaa,	/*=   [* * ]        */
	0xaa,	/*=   [* * ]        */
	0xaa,	/*=   [* * ]        */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 87: 'W'   */
	0xaa,	/*=   [* * ]        */
	0xaa,	/*=   [* * ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xaa,	/*=   [* * ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 88: 'X'   */
	0xaa,	/*=   [* * ]        */
	0xaa,	/*=   [* * ]        */
	0x44,	/*=   [ *  ]        */
	0xaa,	/*=   [* * ]        */
	0xaa,	/*=   [* * ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 89: 'Y'   */
	0xaa,	/*=   [* * ]        */
	0xaa,	/*=   [* * ]        */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 90: 'Z'   */
	0xee,	/*=   [*** ]        */
	0x22,	/*=   [  * ]        */
	0x44,	/*=   [ *  ]        */
	0x88,	/*=   [*   ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 91: '['   */
	0x66,	/*=   [ ** ]        */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	0x66,	/*=   [ ** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 92: '\'   */
	0x00,	/*=   [    ]        */
	0x88,	/*=   [*   ]        */
	0x44,	/*=   [ *  ]        */
	0x22,	/*=   [  * ]        */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 93: ']'   */
	0x66,	/*=   [ ** ]        */
	0x22,	/*=   [  * ]        */
	0x22,	/*=   [  * ]        */
	0x22,	/*=   [  * ]        */
	0x66,	/*=   [ ** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 94: '^'   */
	0x44,	/*=   [ *  ]        */
	0xaa,	/*=   [* * ]        */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 95: '_'   */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	0xff,	/*=   [****]        */
	/*}*/
	/*{*/ 	/*   Char 96: '`'   */
	0x88,	/*=   [*   ]        */
	0x44,	/*=   [ *  ]        */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 97: 'a'   */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	0x66,	/*=   [ ** ]        */
	0xaa,	/*=   [* * ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 98: 'b'   */
	0x88,	/*=   [*   ]        */
	0x88,	/*=   [*   ]        */
	0xcc,	/*=   [**  ]        */
	0xaa,	/*=   [* * ]        */
	0xcc,	/*=   [**  ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 99: 'c'   */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	0x66,	/*=   [ ** ]        */
	0x88,	/*=   [*   ]        */
	0x66,	/*=   [ ** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 100: 'd'   */
	0x22,	/*=   [  * ]        */
	0x22,	/*=   [  * ]        */
	0x66,	/*=   [ ** ]        */
	0xaa,	/*=   [* * ]        */
	0x66,	/*=   [ ** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 101: 'e'   */
	0x00,	/*=   [    ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x88,	/*=   [*   ]        */
	0x66,	/*=   [ ** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 102: 'f'   */
	0x22,	/*=   [  * ]        */
	0x44,	/*=   [ *  ]        */
	0xee,	/*=   [*** ]        */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 103: 'g'   */
	0x00,	/*=   [    ]        */
	0x66,	/*=   [ ** ]        */
	0xaa,	/*=   [* * ]        */
	0x66,	/*=   [ ** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 104: 'h'   */
	0x88,	/*=   [*   ]        */
	0x88,	/*=   [*   ]        */
	0xcc,	/*=   [**  ]        */
	0xaa,	/*=   [* * ]        */
	0xaa,	/*=   [* * ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 105: 'i'   */
	0x44,	/*=   [ *  ]        */
	0x00,	/*=   [    ]        */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 106: 'j'   */
	0x44,	/*=   [ *  ]        */
	0x00,	/*=   [    ]        */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	0x88,	/*=   [*   ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 107: 'k'   */
	0x00,	/*=   [    ]        */
	0x88,	/*=   [*   ]        */
	0xaa,	/*=   [* * ]        */
	0xcc,	/*=   [**  ]        */
	0xaa,	/*=   [* * ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 108: 'l'   */
	0x00,	/*=   [    ]        */
	0xcc,	/*=   [**  ]        */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 109: 'm'   */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xaa,	/*=   [* * ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 110: 'n'   */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	0xcc,	/*=   [**  ]        */
	0xaa,	/*=   [* * ]        */
	0xaa,	/*=   [* * ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 111: 'o'   */
	0x00,	/*=   [    ]        */
	0x44,	/*=   [ *  ]        */
	0xaa,	/*=   [* * ]        */
	0xaa,	/*=   [* * ]        */
	0x44,	/*=   [ *  ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 112: 'p'   */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	0xcc,	/*=   [**  ]        */
	0xaa,	/*=   [* * ]        */
	0xcc,	/*=   [**  ]        */
	0x88,	/*=   [*   ]        */
	/*}*/
	/*{*/ 	/*   Char 113: 'q'   */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	0x66,	/*=   [ ** ]        */
	0xaa,	/*=   [* * ]        */
	0x66,	/*=   [ ** ]        */
	0x22,	/*=   [  * ]        */
	/*}*/
	/*{*/ 	/*   Char 114: 'r'   */
	0x00,	/*=   [    ]        */
	0xcc,	/*=   [**  ]        */
	0xaa,	/*=   [* * ]        */
	0x88,	/*=   [*   ]        */
	0x88,	/*=   [*   ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 115: 's'   */
	0x00,	/*=   [    ]        */
	0x66,	/*=   [ ** ]        */
	0xcc,	/*=   [**  ]        */
	0x22,	/*=   [  * ]        */
	0xcc,	/*=   [**  ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 116: 't'   */
	0x00,	/*=   [    ]        */
	0x44,	/*=   [ *  ]        */
	0xee,	/*=   [*** ]        */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 117: 'u'   */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	0xaa,	/*=   [* * ]        */
	0xaa,	/*=   [* * ]        */
	0x66,	/*=   [ ** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 118: 'v'   */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	0xaa,	/*=   [* * ]        */
	0xee,	/*=   [*** ]        */
	0x44,	/*=   [ *  ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 119: 'w'   */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	0xaa,	/*=   [* * ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 120: 'x'   */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	0xaa,	/*=   [* * ]        */
	0x44,	/*=   [ *  ]        */
	0xaa,	/*=   [* * ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 121: 'y'   */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	0xaa,	/*=   [* * ]        */
	0xee,	/*=   [*** ]        */
	0x22,	/*=   [  * ]        */
	0xcc,	/*=   [**  ]        */
	/*}*/
	/*{*/ 	/*   Char 122: 'z' */
	0x00,	/*=   [    ]        */
	0xee,	/*=   [*** ]        */
	0x66,	/*=   [ ** ]        */
	0xcc,	/*=   [**  ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 123: '{' */
	0x22,	/*=   [  * ]        */
	0x44,	/*=   [ *  ]        */
	0xcc,	/*=   [**  ]        */
	0x44,	/*=   [ *  ]        */
	0x22,	/*=   [  * ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 124: '|' */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 125: '}' */
	0x88,	/*=   [*   ]        */
	0x44,	/*=   [ *  ]        */
	0x66,	/*=   [ ** ]        */
	0x44,	/*=   [ *  ]        */
	0x88,	/*=   [*   ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 126: '~' */
	0x55,	/*=   [ * *]        */
	0xaa,	/*=   [* * ]        */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 127: '' */
	0x44,	/*=   [ *  ]        */
	0xaa,	/*=   [* * ]        */
	0xaa,	/*=   [* * ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 128:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 129:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 130:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 131:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 132:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 133:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 134:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 135:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 136:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 137:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 138:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 139:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 140:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 141:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 142:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 143:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 144:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 145:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 146:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 147:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 148:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 149:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 150:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 151:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 152:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 153:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 154:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 155:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 156:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 157:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 158:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 159:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 160:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 161:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 162:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 163:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 164:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 165:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 166:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 167:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 168:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 169:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 170:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 171:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 172:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 173:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 174:  */
	0x00,	/*=   [    ]        */
	0x66,	/*=   [ ** ]        */
	0xcc,	/*=   [**  ]        */
	0x66,	/*=   [ ** ]        */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 175:  */
	0x00,	/*=   [    ]        */
	0xcc,	/*=   [**  ]        */
	0x66,	/*=   [ ** ]        */
	0xcc,	/*=   [**  ]        */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 176:  */
	0x88,	/*=   [*   ]        */
	0x22,	/*=   [  * ]        */
	0x88,	/*=   [*   ]        */
	0x22,	/*=   [  * ]        */
	0x88,	/*=   [*   ]        */
	0x22,	/*=   [  * ]        */
	/*}*/
	/*{*/ 	/*   Char 177:  */
	0xaa,	/*=   [* * ]        */
	0x55,	/*=   [ * *]        */
	0xaa,	/*=   [* * ]        */
	0x55,	/*=   [ * *]        */
	0xaa,	/*=   [* * ]        */
	0x55,	/*=   [ * *]        */
	/*}*/
	/*{*/ 	/*   Char 178:  */
	0xdd,	/*=   [** *]        */
	0xbb,	/*=   [* **]        */
	0xdd,	/*=   [** *]        */
	0xbb,	/*=   [* **]        */
	0xdd,	/*=   [** *]        */
	0xbb,	/*=   [* **]        */
	/*}*/
	/*{*/ 	/*   Char 179:  */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	/*}*/
	/*{*/ 	/*   Char 180:  */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	0xcc,	/*=   [**  ]        */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	/*}*/
	/*{*/ 	/*   Char 181:  */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	0xcc,	/*=   [**  ]        */
	0xcc,	/*=   [**  ]        */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	/*}*/
	/*{*/ 	/*   Char 182:  */
	0x66,	/*=   [ ** ]        */
	0x66,	/*=   [ ** ]        */
	0xee,	/*=   [*** ]        */
	0x66,	/*=   [ ** ]        */
	0x66,	/*=   [ ** ]        */
	0x66,	/*=   [ ** ]        */
	/*}*/
	/*{*/ 	/*   Char 183:  */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	0xee,	/*=   [*** ]        */
	0x66,	/*=   [ ** ]        */
	0x66,	/*=   [ ** ]        */
	0x66,	/*=   [ ** ]        */
	/*}*/
	/*{*/ 	/*   Char 184:  */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	0xcc,	/*=   [**  ]        */
	0xcc,	/*=   [**  ]        */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	/*}*/
	/*{*/ 	/*   Char 185:  */
	0x66,	/*=   [ ** ]        */
	0x66,	/*=   [ ** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x66,	/*=   [ ** ]        */
	0x66,	/*=   [ ** ]        */
	/*}*/
	/*{*/ 	/*   Char 186:  */
	0x66,	/*=   [ ** ]        */
	0x66,	/*=   [ ** ]        */
	0x66,	/*=   [ ** ]        */
	0x66,	/*=   [ ** ]        */
	0x66,	/*=   [ ** ]        */
	0x66,	/*=   [ ** ]        */
	/*}*/
	/*{*/ 	/*   Char 187:  */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x66,	/*=   [ ** ]        */
	0x66,	/*=   [ ** ]        */
	/*}*/
	/*{*/ 	/*   Char 188:  */
	0x66,	/*=   [ ** ]        */
	0x66,	/*=   [ ** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 189:  */
	0x66,	/*=   [ ** ]        */
	0x66,	/*=   [ ** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 190:  */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	0xcc,	/*=   [**  ]        */
	0xcc,	/*=   [**  ]        */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 191:  */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	0xcc,	/*=   [**  ]        */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	/*}*/
	/*{*/ 	/*   Char 192:  */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	0x77,	/*=   [ ***]        */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 193:  */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	0xff,	/*=   [****]        */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 194:  */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	0xff,	/*=   [****]        */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	/*}*/
	/*{*/ 	/*   Char 195:  */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	0x77,	/*=   [ ***]        */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	/*}*/
	/*{*/ 	/*   Char 196:  */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	0xff,	/*=   [****]        */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 197:  */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	0xff,	/*=   [****]        */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	/*}*/
	/*{*/ 	/*   Char 198:  */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	0x77,	/*=   [ ***]        */
	0x77,	/*=   [ ***]        */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	/*}*/
	/*{*/ 	/*   Char 199:  */
	0x66,	/*=   [ ** ]        */
	0x66,	/*=   [ ** ]        */
	0x77,	/*=   [ ***]        */
	0x66,	/*=   [ ** ]        */
	0x66,	/*=   [ ** ]        */
	0x66,	/*=   [ ** ]        */
	/*}*/
	/*{*/ 	/*   Char 200:  */
	0x66,	/*=   [ ** ]        */
	0x66,	/*=   [ ** ]        */
	0x77,	/*=   [ ***]        */
	0x77,	/*=   [ ***]        */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 201:  */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	0x77,	/*=   [ ***]        */
	0x77,	/*=   [ ***]        */
	0x66,	/*=   [ ** ]        */
	0x66,	/*=   [ ** ]        */
	/*}*/
	/*{*/ 	/*   Char 202:  */
	0x66,	/*=   [ ** ]        */
	0x66,	/*=   [ ** ]        */
	0xff,	/*=   [****]        */
	0xff,	/*=   [****]        */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 203:  */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	0xff,	/*=   [****]        */
	0xff,	/*=   [****]        */
	0x66,	/*=   [ ** ]        */
	0x66,	/*=   [ ** ]        */
	/*}*/
	/*{*/ 	/*   Char 204:  */
	0x66,	/*=   [ ** ]        */
	0x66,	/*=   [ ** ]        */
	0x77,	/*=   [ ***]        */
	0x77,	/*=   [ ***]        */
	0x66,	/*=   [ ** ]        */
	0x66,	/*=   [ ** ]        */
	/*}*/
	/*{*/ 	/*   Char 205:  */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	0xff,	/*=   [****]        */
	0xff,	/*=   [****]        */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 206:  */
	0x66,	/*=   [ ** ]        */
	0x66,	/*=   [ ** ]        */
	0xff,	/*=   [****]        */
	0xff,	/*=   [****]        */
	0x66,	/*=   [ ** ]        */
	0x66,	/*=   [ ** ]        */
	/*}*/
	/*{*/ 	/*   Char 207:  */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	0xff,	/*=   [****]        */
	0xff,	/*=   [****]        */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 208:  */
	0x66,	/*=   [ ** ]        */
	0x66,	/*=   [ ** ]        */
	0xff,	/*=   [****]        */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 209:  */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	0xff,	/*=   [****]        */
	0xff,	/*=   [****]        */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	/*}*/
	/*{*/ 	/*   Char 210:  */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	0xff,	/*=   [****]        */
	0x66,	/*=   [ ** ]        */
	0x66,	/*=   [ ** ]        */
	0x66,	/*=   [ ** ]        */
	/*}*/
	/*{*/ 	/*   Char 211:  */
	0x66,	/*=   [ ** ]        */
	0x66,	/*=   [ ** ]        */
	0x77,	/*=   [ ***]        */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 212:  */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	0x77,	/*=   [ ***]        */
	0x77,	/*=   [ ***]        */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 213:  */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	0x77,	/*=   [ ***]        */
	0x77,	/*=   [ ***]        */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	/*}*/
	/*{*/ 	/*   Char 214:  */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	0x77,	/*=   [ ***]        */
	0x66,	/*=   [ ** ]        */
	0x66,	/*=   [ ** ]        */
	0x66,	/*=   [ ** ]        */
	/*}*/
	/*{*/ 	/*   Char 215:  */
	0x66,	/*=   [ ** ]        */
	0x66,	/*=   [ ** ]        */
	0xff,	/*=   [****]        */
	0x66,	/*=   [ ** ]        */
	0x66,	/*=   [ ** ]        */
	0x66,	/*=   [ ** ]        */
	/*}*/
	/*{*/ 	/*   Char 216:  */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	0xff,	/*=   [****]        */
	0xff,	/*=   [****]        */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	/*}*/
	/*{*/ 	/*   Char 217:  */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	0xcc,	/*=   [**  ]        */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 218:  */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	0x77,	/*=   [ ***]        */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	0x44,	/*=   [ *  ]        */
	/*}*/
	/*{*/ 	/*   Char 219:  */
	0xff,	/*=   [****]        */
	0xff,	/*=   [****]        */
	0xff,	/*=   [****]        */
	0xff,	/*=   [****]        */
	0xff,	/*=   [****]        */
	0xff,	/*=   [****]        */
	/*}*/
	/*{*/ 	/*   Char 220:  */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	0xff,	/*=   [****]        */
	0xff,	/*=   [****]        */
	0xff,	/*=   [****]        */
	/*}*/
	/*{*/ 	/*   Char 221:  */
	0xcc,	/*=   [**  ]        */
	0xcc,	/*=   [**  ]        */
	0xcc,	/*=   [**  ]        */
	0xcc,	/*=   [**  ]        */
	0xcc,	/*=   [**  ]        */
	0xcc,	/*=   [**  ]        */
	/*}*/
	/*{*/ 	/*   Char 222:  */
	0x33,	/*=   [  **]        */
	0x33,	/*=   [  **]        */
	0x33,	/*=   [  **]        */
	0x33,	/*=   [  **]        */
	0x33,	/*=   [  **]        */
	0x33,	/*=   [  **]        */
	/*}*/
	/*{*/ 	/*   Char 223:  */
	0xff,	/*=   [****]        */
	0xff,	/*=   [****]        */
	0xff,	/*=   [****]        */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 224:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 225:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 226:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 227:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 228:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 229:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 230:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 231:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 232:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 233:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 234:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 235:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 236:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 237:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 238:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 239:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 240:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 241:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 242:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 243:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 244:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 245:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 246:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 247:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 248:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 249:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 250:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 251:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 252:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 253:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 254:  */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	0x66,	/*=   [ ** ]        */
	0x66,	/*=   [ ** ]        */
	0x00,	/*=   [    ]        */
	0x00,	/*=   [    ]        */
	/*}*/
	/*{*/ 	/*   Char 255:  */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0xee,	/*=   [*** ]        */
	0x00,	/*=   [    ]        */
	/*}*/
} };

const struct font_desc font_mini_4x6 = {
	.idx	= MINI4x6_IDX,
	.name	= "MINI4x6",
	.width	= 4,
	.height	= 6,
	.charcount = 256,
	.data	= fontdata_mini_4x6.data,
	.pref	= 3,
};

