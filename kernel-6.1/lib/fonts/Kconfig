# SPDX-License-Identifier: GPL-2.0-only
#
# Font configuration
#

config FONT_SUPPORT
	tristate

if FONT_SUPPORT

config FONTS
	bool "Select compiled-in fonts"
	depends on FRAMEBUFFER_CONSOLE || STI_CONSOLE
	help
	  Say Y here if you would like to use fonts other than the default
	  your frame buffer console usually use.

	  Note that the answer to this question won't directly affect the
	  kernel: saying N will just cause the configurator to skip all
	  the questions about foreign fonts.

	  If unsure, say N (the default choices are safe).

config FONT_8x8
	bool "VGA 8x8 font" if FONTS
	depends on FRAMEBUFFER_CONSOLE || STI_CONSOLE
	default y if !SPARC && !FONTS
	help
	  This is the "high resolution" font for the VGA frame buffer (the one
	  provided by the text console 80x50 (and higher) modes).

	  Note that this is a poor quality font. The VGA 8x16 font is quite a
	  lot more readable.

	  Given the resolution provided by the frame buffer device, answer N
	  here is safe.

config FONT_8x16
	bool "VGA 8x16 font" if FONTS
	default y if !SPARC && !FONTS
	help
	  This is the "high resolution" font for the VGA frame buffer (the one
	  provided by the VGA text console 80x25 mode.

	  If unsure, say Y.

config FONT_6x11
	bool "Mac console 6x11 font (not supported by all drivers)" if FONTS
	depends on FRAMEBUFFER_CONSOLE || STI_CONSOLE
	default y if !SPARC && !FONTS && MAC
	help
	  Small console font with Macintosh-style high-half glyphs.  Some Mac
	  framebuffer drivers don't support this one at all.

config FONT_7x14
	bool "console 7x14 font (not supported by all drivers)" if FONTS
	depends on FRAMEBUFFER_CONSOLE
	help
	  Console font with characters just a bit smaller than the default.
	  If the standard 8x16 font is a little too big for you, say Y.
	  Otherwise, say N.

config FONT_PEARL_8x8
	bool "Pearl (old m68k) console 8x8 font" if FONTS
	depends on FRAMEBUFFER_CONSOLE
	default y if !SPARC && !FONTS && AMIGA
	help
	  Small console font with PC-style control-character and high-half
	  glyphs.

config FONT_ACORN_8x8
	bool "Acorn console 8x8 font" if FONTS
	depends on FRAMEBUFFER_CONSOLE
	default y if !SPARC && !FONTS && ARM && ARCH_ACORN
	help
	  Small console font with PC-style control characters and high-half
	  glyphs.

config FONT_MINI_4x6
	bool "Mini 4x6 font"
	depends on !SPARC && FONTS

config FONT_6x10
	bool "Medium-size 6x10 font"
	depends on !SPARC && FONTS
	help
	  Medium-size console font. Suitable for framebuffer consoles on
	  embedded devices with a 320x240 screen, to get a reasonable number
	  of characters (53x24) that are still at a readable size.

config FONT_10x18
	bool "console 10x18 font (not supported by all drivers)" if FONTS
	depends on FRAMEBUFFER_CONSOLE
	help
	  This is a high resolution console font for machines with very
	  big letters. It fits between the sun 12x22 and the normal 8x16 font.
	  If other fonts are too big or too small for you, say Y, otherwise say N.

config FONT_SUN8x16
	bool "Sparc console 8x16 font"
	depends on FRAMEBUFFER_CONSOLE && (!SPARC && FONTS || SPARC)
	help
	  This is the high resolution console font for Sun machines. Say Y.

config FONT_SUN12x22
	bool "Sparc console 12x22 font (not supported by all drivers)"
	depends on FRAMEBUFFER_CONSOLE && (!SPARC && FONTS || SPARC)
	help
	  This is the high resolution console font for Sun machines with very
	  big letters (like the letters used in the SPARC PROM). If the
	  standard font is unreadable for you, say Y, otherwise say N.

config FONT_TER16x32
	bool "Terminus 16x32 font (not supported by all drivers)"
	depends on FRAMEBUFFER_CONSOLE && (!SPARC && FONTS || SPARC)
	help
	  Terminus Font is a clean, fixed width bitmap font, designed
	  for long (8 and more hours per day) work with computers.
	  This is the high resolution, large version for use with HiDPI screens.
	  If the standard font is unreadable for you, say Y, otherwise say N.

config FONT_6x8
	bool "OLED 6x8 font" if FONTS
	depends on FRAMEBUFFER_CONSOLE
	help
	  This font is useful for small displays (OLED).

config FONT_AUTOSELECT
	def_bool y
	depends on !FONT_8x8
	depends on !FONT_6x11
	depends on !FONT_7x14
	depends on !FONT_PEARL_8x8
	depends on !FONT_ACORN_8x8
	depends on !FONT_MINI_4x6
	depends on !FONT_6x10
	depends on !FONT_SUN8x16
	depends on !FONT_SUN12x22
	depends on !FONT_10x18
	depends on !FONT_TER16x32
	depends on !FONT_6x8
	select FONT_8x16

endif # FONT_SUPPORT
