// SPDX-License-Identifier: GPL-2.0
/**************************************/
/* this file adapted from font_8x16.c */
/* by <PERSON><PERSON><PERSON> 05-2005        */
/**************************************/

#include <linux/font.h>

#define FONTDATAMAX 3584

static const struct font_data fontdata_7x14 = {
	{ 0, 0, FONTDATAMAX, 0 }, {
	/* 0 0x00 '^@' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 1 0x01 '^A' */
	0x00, /* 0000000 */
	0x7c, /* 0111110 */
	0x82, /* 1000001 */
	0xaa, /* 1010101 */
	0x82, /* 1000001 */
	0x82, /* 1000001 */
	0xba, /* 1011101 */
	0x92, /* 1001001 */
	0x82, /* 1000001 */
	0x7c, /* 0111110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 2 0x02 '^B' */
	0x00, /* 0000000 */
	0x7c, /* 0111110 */
	0xfe, /* 1111111 */
	0xd6, /* 1101011 */
	0xfe, /* 1111111 */
	0xfe, /* 1111111 */
	0xc6, /* 1100011 */
	0xee, /* 1110111 */
	0xfe, /* 1111111 */
	0xfe, /* 1111111 */
	0x7c, /* 0111110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 3 0x03 '^C' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x6c, /* 0110110 */
	0x7c, /* 0111110 */
	0xfe, /* 1111111 */
	0x7c, /* 0111110 */
	0x38, /* 0011100 */
	0x18, /* 0001100 */
	0x10, /* 0001000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 4 0x04 '^D' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x10, /* 0001000 */
	0x38, /* 0011100 */
	0x7c, /* 0111110 */
	0xfe, /* 1111111 */
	0x7c, /* 0111110 */
	0x38, /* 0011100 */
	0x10, /* 0001000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 5 0x05 '^E' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x38, /* 0011100 */
	0x38, /* 0011100 */
	0x38, /* 0011100 */
	0xee, /* 1110111 */
	0xee, /* 1110111 */
	0xee, /* 1110111 */
	0x10, /* 0001000 */
	0x10, /* 0001000 */
	0x38, /* 0011100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 6 0x06 '^F' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x10, /* 0001000 */
	0x38, /* 0011100 */
	0x7c, /* 0111110 */
	0xfe, /* 1111111 */
	0xfe, /* 1111111 */
	0x7c, /* 0111110 */
	0x10, /* 0001000 */
	0x10, /* 0001000 */
	0x38, /* 0011100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 7 0x07 '^G' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x18, /* 0001100 */
	0x3c, /* 0011110 */
	0x3c, /* 0011110 */
	0x18, /* 0001100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 8 0x08 '^H' */
	0xfe, /* 1111111 */
	0xfe, /* 1111111 */
	0xfe, /* 1111111 */
	0xfe, /* 1111111 */
	0xfe, /* 1111111 */
	0xe6, /* 1110011 */
	0xc2, /* 1100001 */
	0xc2, /* 1100001 */
	0xe6, /* 1110011 */
	0xfe, /* 1111111 */
	0xfe, /* 1111111 */
	0xfe, /* 1111111 */
	0xfe, /* 1111111 */
	0xfe, /* 1111111 */

	/* 9 0x09 '^I' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x38, /* 0011100 */
	0x6c, /* 0110110 */
	0x44, /* 0100010 */
	0x6c, /* 0110110 */
	0x38, /* 0011100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 10 0x0a '^J' */
	0xfe, /* 1111111 */
	0xfe, /* 1111111 */
	0xfe, /* 1111111 */
	0xfe, /* 1111111 */
	0xfe, /* 1111111 */
	0xc6, /* 1100011 */
	0x92, /* 1001001 */
	0xba, /* 1011101 */
	0x92, /* 1001001 */
	0xc6, /* 1100011 */
	0xfe, /* 1111111 */
	0xfe, /* 1111111 */
	0xfe, /* 1111111 */
	0xfe, /* 1111111 */

	/* 11 0x0b '^K' */
	0x00, /* 0000000 */
	0x1e, /* 0001111 */
	0x0e, /* 0000111 */
	0x1a, /* 0001101 */
	0x1a, /* 0001101 */
	0x78, /* 0111100 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x78, /* 0111100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 12 0x0c '^L' */
	0x00, /* 0000000 */
	0x3c, /* 0011110 */
	0x66, /* 0110011 */
	0x66, /* 0110011 */
	0x66, /* 0110011 */
	0x66, /* 0110011 */
	0x3c, /* 0011110 */
	0x18, /* 0001100 */
	0x7e, /* 0111111 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 13 0x0d '^M' */
	0x00, /* 0000000 */
	0x3e, /* 0011111 */
	0x36, /* 0011011 */
	0x3e, /* 0011111 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x70, /* 0111000 */
	0xf0, /* 1111000 */
	0xe0, /* 1110000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 14 0x0e '^N' */
	0x00, /* 0000000 */
	0x7e, /* 0111111 */
	0x66, /* 0110011 */
	0x7e, /* 0111111 */
	0x66, /* 0110011 */
	0x66, /* 0110011 */
	0x66, /* 0110011 */
	0x66, /* 0110011 */
	0x6e, /* 0110111 */
	0xee, /* 1110111 */
	0xec, /* 1110110 */
	0xc0, /* 1100000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 15 0x0f '^O' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x10, /* 0001000 */
	0x10, /* 0001000 */
	0xd6, /* 1101011 */
	0x38, /* 0011100 */
	0xee, /* 1110111 */
	0x38, /* 0011100 */
	0xd6, /* 1101011 */
	0x10, /* 0001000 */
	0x10, /* 0001000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 16 0x10 '^P' */
	0x00, /* 0000000 */
	0x80, /* 1000000 */
	0xc0, /* 1100000 */
	0xe0, /* 1110000 */
	0xf0, /* 1111000 */
	0xfc, /* 1111110 */
	0xf0, /* 1111000 */
	0xe0, /* 1110000 */
	0xc0, /* 1100000 */
	0x80, /* 1000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 17 0x11 '^Q' */
	0x00, /* 0000000 */
	0x04, /* 0000010 */
	0x0c, /* 0000110 */
	0x1c, /* 0001110 */
	0x3c, /* 0011110 */
	0xfc, /* 1111110 */
	0x3c, /* 0011110 */
	0x1c, /* 0001110 */
	0x0c, /* 0000110 */
	0x04, /* 0000010 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 18 0x12 '^R' */
	0x00, /* 0000000 */
	0x18, /* 0001100 */
	0x3c, /* 0011110 */
	0x7e, /* 0111111 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x7e, /* 0111111 */
	0x3c, /* 0011110 */
	0x18, /* 0001100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 19 0x13 '^S' */
	0x00, /* 0000000 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x00, /* 0000000 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 20 0x14 '^T' */
	0x00, /* 0000000 */
	0x7e, /* 0111111 */
	0xd4, /* 1101010 */
	0xd4, /* 1101010 */
	0xd4, /* 1101010 */
	0x74, /* 0111010 */
	0x14, /* 0001010 */
	0x14, /* 0001010 */
	0x14, /* 0001010 */
	0x14, /* 0001010 */
	0x16, /* 0001011 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 21 0x15 '^U' */
	0x78, /* 0111100 */
	0xcc, /* 1100110 */
	0x60, /* 0110000 */
	0x38, /* 0011100 */
	0x6c, /* 0110110 */
	0xc6, /* 1100011 */
	0xc6, /* 1100011 */
	0x6c, /* 0110110 */
	0x38, /* 0011100 */
	0x18, /* 0001100 */
	0xcc, /* 1100110 */
	0x78, /* 0111100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 22 0x16 '^V' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0xfc, /* 1111110 */
	0xfc, /* 1111110 */
	0xfc, /* 1111110 */
	0xfc, /* 1111110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 23 0x17 '^W' */
	0x00, /* 0000000 */
	0x18, /* 0001100 */
	0x3c, /* 0011110 */
	0x7e, /* 0111111 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x7e, /* 0111111 */
	0x3c, /* 0011110 */
	0x18, /* 0001100 */
	0x7e, /* 0111111 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 24 0x18 '^X' */
	0x00, /* 0000000 */
	0x18, /* 0001100 */
	0x3c, /* 0011110 */
	0x7e, /* 0111111 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 25 0x19 '^Y' */
	0x00, /* 0000000 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x7e, /* 0111111 */
	0x3c, /* 0011110 */
	0x18, /* 0001100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 26 0x1a '^Z' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x30, /* 0011000 */
	0x18, /* 0001100 */
	0xfc, /* 1111110 */
	0x18, /* 0001100 */
	0x30, /* 0011000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 27 0x1b '^[' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x30, /* 0011000 */
	0x60, /* 0110000 */
	0xfc, /* 1111110 */
	0x60, /* 0110000 */
	0x30, /* 0011000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 28 0x1c '^\' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0xfc, /* 1111110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 29 0x1d '^]' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x28, /* 0010100 */
	0x6c, /* 0110110 */
	0xfe, /* 1111111 */
	0x6c, /* 0110110 */
	0x28, /* 0010100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 30 0x1e '^^' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x78, /* 0111100 */
	0x78, /* 0111100 */
	0xfc, /* 1111110 */
	0xfc, /* 1111110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 31 0x1f '^_' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0xfc, /* 1111110 */
	0xfc, /* 1111110 */
	0x78, /* 0111100 */
	0x78, /* 0111100 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 32 0x20 ' ' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 33 0x21 '!' */
	0x00, /* 0000000 */
	0x18, /* 0001100 */
	0x3c, /* 0011110 */
	0x3c, /* 0011110 */
	0x3c, /* 0011110 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x00, /* 0000000 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 34 0x22 '"' */
	0x00, /* 0000000 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x28, /* 0010100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 35 0x23 '#' */
	0x00, /* 0000000 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0xfe, /* 1111111 */
	0xfe, /* 1111111 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0xfe, /* 1111111 */
	0xfe, /* 1111111 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 36 0x24 '$' */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x78, /* 0111100 */
	0xcc, /* 1100110 */
	0xc4, /* 1100010 */
	0xc0, /* 1100000 */
	0x78, /* 0111100 */
	0x0c, /* 0000110 */
	0x8c, /* 1000110 */
	0xcc, /* 1100110 */
	0x78, /* 0111100 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x00, /* 0000000 */

	/* 37 0x25 '%' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0xc0, /* 1100000 */
	0xc4, /* 1100010 */
	0x0c, /* 0000110 */
	0x18, /* 0001100 */
	0x30, /* 0011000 */
	0x60, /* 0110000 */
	0xcc, /* 1100110 */
	0x8c, /* 1000110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 38 0x26 '&' */
	0x00, /* 0000000 */
	0x38, /* 0011100 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x38, /* 0011100 */
	0x78, /* 0111100 */
	0xde, /* 1101111 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xdc, /* 1101110 */
	0x76, /* 0111011 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 39 0x27 ''' */
	0x00, /* 0000000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x60, /* 0110000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 40 0x28 '(' */
	0x00, /* 0000000 */
	0x0c, /* 0000110 */
	0x18, /* 0001100 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x18, /* 0001100 */
	0x0c, /* 0000110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 41 0x29 ')' */
	0x00, /* 0000000 */
	0x30, /* 0011000 */
	0x18, /* 0001100 */
	0x0c, /* 0000110 */
	0x0c, /* 0000110 */
	0x0c, /* 0000110 */
	0x0c, /* 0000110 */
	0x0c, /* 0000110 */
	0x0c, /* 0000110 */
	0x18, /* 0001100 */
	0x30, /* 0011000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 42 0x2a '*' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x6c, /* 0110110 */
	0x38, /* 0011100 */
	0xfe, /* 1111111 */
	0x38, /* 0011100 */
	0x6c, /* 0110110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 43 0x2b '+' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x10, /* 0001000 */
	0x10, /* 0001000 */
	0x7c, /* 0111110 */
	0x10, /* 0001000 */
	0x10, /* 0001000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 44 0x2c ',' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x30, /* 0011000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 45 0x2d '-' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0xfc, /* 1111110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 46 0x2e '.' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 47 0x2f '/' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x04, /* 0000010 */
	0x0c, /* 0000110 */
	0x18, /* 0001100 */
	0x30, /* 0011000 */
	0x60, /* 0110000 */
	0xc0, /* 1100000 */
	0x80, /* 1000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 48 0x30 '0' */
	0x00, /* 0000000 */
	0x30, /* 0011000 */
	0x78, /* 0111100 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xdc, /* 1101110 */
	0xec, /* 1110110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x78, /* 0111100 */
	0x30, /* 0011000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 49 0x31 '1' */
	0x00, /* 0000000 */
	0x18, /* 0001100 */
	0x38, /* 0011100 */
	0x78, /* 0111100 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x7c, /* 0111110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 50 0x32 '2' */
	0x00, /* 0000000 */
	0x78, /* 0111100 */
	0xcc, /* 1100110 */
	0x0c, /* 0000110 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x30, /* 0011000 */
	0x60, /* 0110000 */
	0xc0, /* 1100000 */
	0xcc, /* 1100110 */
	0xfc, /* 1111110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 51 0x33 '3' */
	0x00, /* 0000000 */
	0x78, /* 0111100 */
	0xcc, /* 1100110 */
	0x0c, /* 0000110 */
	0x0c, /* 0000110 */
	0x38, /* 0011100 */
	0x0c, /* 0000110 */
	0x0c, /* 0000110 */
	0x0c, /* 0000110 */
	0xcc, /* 1100110 */
	0x78, /* 0111100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 52 0x34 '4' */
	0x00, /* 0000000 */
	0x0c, /* 0000110 */
	0x1c, /* 0001110 */
	0x3c, /* 0011110 */
	0x6c, /* 0110110 */
	0xcc, /* 1100110 */
	0xfe, /* 1111111 */
	0x0c, /* 0000110 */
	0x0c, /* 0000110 */
	0x0c, /* 0000110 */
	0x0c, /* 0000110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 53 0x35 '5' */
	0x00, /* 0000000 */
	0xfc, /* 1111110 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0xf8, /* 1111100 */
	0x0c, /* 0000110 */
	0x0c, /* 0000110 */
	0x0c, /* 0000110 */
	0xcc, /* 1100110 */
	0x78, /* 0111100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 54 0x36 '6' */
	0x00, /* 0000000 */
	0x30, /* 0011000 */
	0x60, /* 0110000 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0xf8, /* 1111100 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x78, /* 0111100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 55 0x37 '7' */
	0x00, /* 0000000 */
	0xfc, /* 1111110 */
	0xcc, /* 1100110 */
	0x0c, /* 0000110 */
	0x0c, /* 0000110 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 56 0x38 '8' */
	0x00, /* 0000000 */
	0x78, /* 0111100 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x78, /* 0111100 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x78, /* 0111100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 57 0x39 '9' */
	0x00, /* 0000000 */
	0x78, /* 0111100 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x7c, /* 0111110 */
	0x0c, /* 0000110 */
	0x0c, /* 0000110 */
	0x0c, /* 0000110 */
	0x18, /* 0001100 */
	0x70, /* 0111000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 58 0x3a ':' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 59 0x3b ';' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x30, /* 0011000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 60 0x3c '<' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x04, /* 0000010 */
	0x0c, /* 0000110 */
	0x18, /* 0001100 */
	0x30, /* 0011000 */
	0x60, /* 0110000 */
	0x30, /* 0011000 */
	0x18, /* 0001100 */
	0x0c, /* 0000110 */
	0x04, /* 0000010 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 61 0x3d '=' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x7c, /* 0111110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x7c, /* 0111110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 62 0x3e '>' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x40, /* 0100000 */
	0x60, /* 0110000 */
	0x30, /* 0011000 */
	0x18, /* 0001100 */
	0x0c, /* 0000110 */
	0x18, /* 0001100 */
	0x30, /* 0011000 */
	0x60, /* 0110000 */
	0x40, /* 0100000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 63 0x3f '?' */
	0x00, /* 0000000 */
	0x78, /* 0111100 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x18, /* 0001100 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x00, /* 0000000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 64 0x40 '@' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x78, /* 0111100 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xdc, /* 1101110 */
	0xdc, /* 1101110 */
	0xd8, /* 1101100 */
	0xc0, /* 1100000 */
	0x78, /* 0111100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 65 0x41 'A' */
	0x00, /* 0000000 */
	0x30, /* 0011000 */
	0x78, /* 0111100 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xfc, /* 1111110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 66 0x42 'B' */
	0x00, /* 0000000 */
	0xf8, /* 1111100 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x78, /* 0111100 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0xf8, /* 1111100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 67 0x43 'C' */
	0x00, /* 0000000 */
	0x38, /* 0011100 */
	0x6c, /* 0110110 */
	0xc4, /* 1100010 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0xc4, /* 1100010 */
	0x6c, /* 0110110 */
	0x38, /* 0011100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 68 0x44 'D' */
	0x00, /* 0000000 */
	0xf0, /* 1111000 */
	0xd8, /* 1101100 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xd8, /* 1101100 */
	0xf0, /* 1111000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 69 0x45 'E' */
	0x00, /* 0000000 */
	0x7c, /* 0111110 */
	0x6c, /* 0110110 */
	0x64, /* 0110010 */
	0x68, /* 0110100 */
	0x78, /* 0111100 */
	0x68, /* 0110100 */
	0x60, /* 0110000 */
	0x64, /* 0110010 */
	0x6c, /* 0110110 */
	0x7c, /* 0111110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 70 0x46 'F' */
	0x00, /* 0000000 */
	0x7c, /* 0111110 */
	0x64, /* 0110010 */
	0x60, /* 0110000 */
	0x68, /* 0110100 */
	0x78, /* 0111100 */
	0x68, /* 0110100 */
	0x60, /* 0110000 */
	0x60, /* 0110000 */
	0x60, /* 0110000 */
	0x70, /* 0111000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 71 0x47 'G' */
	0x00, /* 0000000 */
	0x38, /* 0011100 */
	0x6c, /* 0110110 */
	0xc4, /* 1100010 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0xdc, /* 1101110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x6c, /* 0110110 */
	0x34, /* 0011010 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 72 0x48 'H' */
	0x00, /* 0000000 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xfc, /* 1111110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 73 0x49 'I' */
	0x00, /* 0000000 */
	0x3c, /* 0011110 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x3c, /* 0011110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 74 0x4a 'J' */
	0x00, /* 0000000 */
	0x1c, /* 0001110 */
	0x0c, /* 0000110 */
	0x0c, /* 0000110 */
	0x0c, /* 0000110 */
	0x0c, /* 0000110 */
	0x0c, /* 0000110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x78, /* 0111100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 75 0x4b 'K' */
	0x00, /* 0000000 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xd8, /* 1101100 */
	0xf0, /* 1111000 */
	0xf0, /* 1111000 */
	0xd8, /* 1101100 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 76 0x4c 'L' */
	0x00, /* 0000000 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0xc4, /* 1100010 */
	0xcc, /* 1100110 */
	0xfc, /* 1111110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 77 0x4d 'M' */
	0x00, /* 0000000 */
	0xc6, /* 1100011 */
	0xee, /* 1110111 */
	0xfe, /* 1111111 */
	0xfe, /* 1111111 */
	0xd6, /* 1101011 */
	0xc6, /* 1100011 */
	0xc6, /* 1100011 */
	0xc6, /* 1100011 */
	0xc6, /* 1100011 */
	0xc6, /* 1100011 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 78 0x4e 'N' */
	0x00, /* 0000000 */
	0xcc, /* 1100110 */
	0xec, /* 1110110 */
	0xec, /* 1110110 */
	0xfc, /* 1111110 */
	0xdc, /* 1101110 */
	0xdc, /* 1101110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 79 0x4f 'O' */
	0x00, /* 0000000 */
	0x78, /* 0111100 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x78, /* 0111100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 80 0x50 'P' */
	0x00, /* 0000000 */
	0xf8, /* 1111100 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xf8, /* 1111100 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 81 0x51 'Q' */
	0x00, /* 0000000 */
	0x78, /* 0111100 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xdc, /* 1101110 */
	0x78, /* 0111100 */
	0x18, /* 0001100 */
	0x1c, /* 0001110 */
	0x00, /* 0000000 */

	/* 82 0x52 'R' */
	0x00, /* 0000000 */
	0xf8, /* 1111100 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xf8, /* 1111100 */
	0xd8, /* 1101100 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 83 0x53 'S' */
	0x00, /* 0000000 */
	0x7c, /* 0111110 */
	0xc4, /* 1100010 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0x60, /* 0110000 */
	0x38, /* 0011100 */
	0x0c, /* 0000110 */
	0x0c, /* 0000110 */
	0x8c, /* 1000110 */
	0xf8, /* 1111100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 84 0x54 'T' */
	0x00, /* 0000000 */
	0xfc, /* 1111110 */
	0xfc, /* 1111110 */
	0xb4, /* 1011010 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x78, /* 0111100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 85 0x55 'U' */
	0x00, /* 0000000 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x78, /* 0111100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 86 0x56 'V' */
	0x00, /* 0000000 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x78, /* 0111100 */
	0x78, /* 0111100 */
	0x30, /* 0011000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 87 0x57 'W' */
	0x00, /* 0000000 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xfc, /* 1111110 */
	0xfc, /* 1111110 */
	0x48, /* 0100100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 88 0x58 'X' */
	0x00, /* 0000000 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x78, /* 0111100 */
	0x78, /* 0111100 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x78, /* 0111100 */
	0x78, /* 0111100 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 89 0x59 'Y' */
	0x00, /* 0000000 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x78, /* 0111100 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 90 0x5a 'Z' */
	0x00, /* 0000000 */
	0xfc, /* 1111110 */
	0xcc, /* 1100110 */
	0x8c, /* 1000110 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x30, /* 0011000 */
	0x60, /* 0110000 */
	0xc4, /* 1100010 */
	0xcc, /* 1100110 */
	0xfc, /* 1111110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 91 0x5b '[' */
	0x00, /* 0000000 */
	0x78, /* 0111100 */
	0x60, /* 0110000 */
	0x60, /* 0110000 */
	0x60, /* 0110000 */
	0x60, /* 0110000 */
	0x60, /* 0110000 */
	0x60, /* 0110000 */
	0x60, /* 0110000 */
	0x60, /* 0110000 */
	0x78, /* 0111100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 92 0x5c '\' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x80, /* 1000000 */
	0xc0, /* 1100000 */
	0xe0, /* 1110000 */
	0x70, /* 0111000 */
	0x38, /* 0011100 */
	0x1c, /* 0001110 */
	0x0c, /* 0000110 */
	0x04, /* 0000010 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 93 0x5d ']' */
	0x00, /* 0000000 */
	0x78, /* 0111100 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x78, /* 0111100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 94 0x5e '^' */
	0x10, /* 0001000 */
	0x38, /* 0011100 */
	0x6c, /* 0110110 */
	0xc6, /* 1100011 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 95 0x5f '_' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0xfe, /* 1111111 */
	0x00, /* 0000000 */

	/* 96 0x60 '`' */
	0x00, /* 0000000 */
	0x60, /* 0110000 */
	0x30, /* 0011000 */
	0x18, /* 0001100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 97 0x61 'a' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x78, /* 0111100 */
	0x0c, /* 0000110 */
	0x7c, /* 0111110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x76, /* 0111011 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 98 0x62 'b' */
	0x00, /* 0000000 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0xf0, /* 1111000 */
	0xd8, /* 1101100 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xf8, /* 1111100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 99 0x63 'c' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x78, /* 0111100 */
	0xcc, /* 1100110 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0xcc, /* 1100110 */
	0x78, /* 0111100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 100 0x64 'd' */
	0x00, /* 0000000 */
	0x1c, /* 0001110 */
	0x0c, /* 0000110 */
	0x0c, /* 0000110 */
	0x3c, /* 0011110 */
	0x6c, /* 0110110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x76, /* 0111011 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 101 0x65 'e' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x78, /* 0111100 */
	0xcc, /* 1100110 */
	0xfc, /* 1111110 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0xcc, /* 1100110 */
	0x78, /* 0111100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 102 0x66 'f' */
	0x00, /* 0000000 */
	0x38, /* 0011100 */
	0x6c, /* 0110110 */
	0x64, /* 0110010 */
	0x60, /* 0110000 */
	0xf0, /* 1111000 */
	0x60, /* 0110000 */
	0x60, /* 0110000 */
	0x60, /* 0110000 */
	0x60, /* 0110000 */
	0xf0, /* 1111000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 103 0x67 'g' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x76, /* 0111011 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x7c, /* 0111110 */
	0x0c, /* 0000110 */
	0xcc, /* 1100110 */
	0x78, /* 0111100 */

	/* 104 0x68 'h' */
	0x00, /* 0000000 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0xd8, /* 1101100 */
	0xec, /* 1110110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 105 0x69 'i' */
	0x00, /* 0000000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x00, /* 0000000 */
	0x70, /* 0111000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x78, /* 0111100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 106 0x6a 'j' */
	0x00, /* 0000000 */
	0x0c, /* 0000110 */
	0x0c, /* 0000110 */
	0x00, /* 0000000 */
	0x1c, /* 0001110 */
	0x0c, /* 0000110 */
	0x0c, /* 0000110 */
	0x0c, /* 0000110 */
	0x0c, /* 0000110 */
	0x0c, /* 0000110 */
	0x0c, /* 0000110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x78, /* 0111100 */

	/* 107 0x6b 'k' */
	0x00, /* 0000000 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0xcc, /* 1100110 */
	0xd8, /* 1101100 */
	0xf0, /* 1111000 */
	0xf0, /* 1111000 */
	0xd8, /* 1101100 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 108 0x6c 'l' */
	0x00, /* 0000000 */
	0x70, /* 0111000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x78, /* 0111100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 109 0x6d 'm' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0xec, /* 1110110 */
	0xfe, /* 1111111 */
	0xd6, /* 1101011 */
	0xd6, /* 1101011 */
	0xd6, /* 1101011 */
	0xd6, /* 1101011 */
	0xd6, /* 1101011 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 110 0x6e 'n' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0xb8, /* 1011100 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 111 0x6f 'o' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x78, /* 0111100 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x78, /* 0111100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 112 0x70 'p' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0xb8, /* 1011100 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xf8, /* 1111100 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */

	/* 113 0x71 'q' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x74, /* 0111010 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x7c, /* 0111110 */
	0x0c, /* 0000110 */
	0x0c, /* 0000110 */
	0x0c, /* 0000110 */

	/* 114 0x72 'r' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0xb8, /* 1011100 */
	0xec, /* 1110110 */
	0xcc, /* 1100110 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 115 0x73 's' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x78, /* 0111100 */
	0xcc, /* 1100110 */
	0x60, /* 0110000 */
	0x30, /* 0011000 */
	0x18, /* 0001100 */
	0xcc, /* 1100110 */
	0x78, /* 0111100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 116 0x74 't' */
	0x00, /* 0000000 */
	0x10, /* 0001000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0xfc, /* 1111110 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x36, /* 0011011 */
	0x1c, /* 0001110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 117 0x75 'u' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x76, /* 0111011 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 118 0x76 'v' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x78, /* 0111100 */
	0x30, /* 0011000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 119 0x77 'w' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0xc6, /* 1100011 */
	0xc6, /* 1100011 */
	0xd6, /* 1101011 */
	0xd6, /* 1101011 */
	0xd6, /* 1101011 */
	0xfe, /* 1111111 */
	0x6c, /* 0110110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 120 0x78 'x' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x78, /* 0111100 */
	0x30, /* 0011000 */
	0x78, /* 0111100 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 121 0x79 'y' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x7c, /* 0111110 */
	0x0c, /* 0000110 */
	0x18, /* 0001100 */
	0xf0, /* 1111000 */

	/* 122 0x7a 'z' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0xfc, /* 1111110 */
	0xcc, /* 1100110 */
	0x18, /* 0001100 */
	0x30, /* 0011000 */
	0x60, /* 0110000 */
	0xcc, /* 1100110 */
	0xfc, /* 1111110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 123 0x7b '{' */
	0x00, /* 0000000 */
	0x1c, /* 0001110 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0xe0, /* 1110000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x1c, /* 0001110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 124 0x7c '|' */
	0x00, /* 0000000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 125 0x7d '}' */
	0x00, /* 0000000 */
	0x70, /* 0111000 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x0e, /* 0000111 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x70, /* 0111000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 126 0x7e '~' */
	0x00, /* 0000000 */
	0xec, /* 1110110 */
	0xb8, /* 1011100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 127 0x7f '' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x10, /* 0001000 */
	0x38, /* 0011100 */
	0x6c, /* 0110110 */
	0xc6, /* 1100011 */
	0xc6, /* 1100011 */
	0xc6, /* 1100011 */
	0xfe, /* 1111111 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 128 0x80 'Ç' */
	0x00, /* 0000000 */
	0x38, /* 0011100 */
	0x6c, /* 0110110 */
	0xc4, /* 1100010 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0xc4, /* 1100010 */
	0x6c, /* 0110110 */
	0x38, /* 0011100 */
	0x18, /* 0001100 */
	0x70, /* 0111000 */
	0x00, /* 0000000 */

	/* 129 0x81 'ü' */
	0x00, /* 0000000 */
	0xcc, /* 1100110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x76, /* 0111011 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 130 0x82 'é' */
	0x0c, /* 0000110 */
	0x18, /* 0001100 */
	0x30, /* 0011000 */
	0x00, /* 0000000 */
	0x78, /* 0111100 */
	0xcc, /* 1100110 */
	0xfc, /* 1111110 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0xcc, /* 1100110 */
	0x78, /* 0111100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 131 0x83 'â' */
	0x10, /* 0001000 */
	0x38, /* 0011100 */
	0x6c, /* 0110110 */
	0x00, /* 0000000 */
	0x78, /* 0111100 */
	0x0c, /* 0000110 */
	0x7c, /* 0111110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x76, /* 0111011 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 132 0x84 'ä' */
	0x00, /* 0000000 */
	0xcc, /* 1100110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x78, /* 0111100 */
	0x0c, /* 0000110 */
	0x7c, /* 0111110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x76, /* 0111011 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 133 0x85 'à' */
	0x60, /* 0110000 */
	0x30, /* 0011000 */
	0x18, /* 0001100 */
	0x00, /* 0000000 */
	0x78, /* 0111100 */
	0x0c, /* 0000110 */
	0x7c, /* 0111110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x76, /* 0111011 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 134 0x86 'å' */
	0x38, /* 0011100 */
	0x6c, /* 0110110 */
	0x38, /* 0011100 */
	0x00, /* 0000000 */
	0x78, /* 0111100 */
	0x0c, /* 0000110 */
	0x7c, /* 0111110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x76, /* 0111011 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 135 0x87 'ç' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x78, /* 0111100 */
	0xcc, /* 1100110 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0xcc, /* 1100110 */
	0x78, /* 0111100 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0xe0, /* 1110000 */

	/* 136 0x88 'ê' */
	0x10, /* 0001000 */
	0x38, /* 0011100 */
	0x6c, /* 0110110 */
	0x00, /* 0000000 */
	0x78, /* 0111100 */
	0xcc, /* 1100110 */
	0xfc, /* 1111110 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0xcc, /* 1100110 */
	0x78, /* 0111100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 137 0x89 'ë' */
	0x00, /* 0000000 */
	0xcc, /* 1100110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x78, /* 0111100 */
	0xcc, /* 1100110 */
	0xfc, /* 1111110 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0xcc, /* 1100110 */
	0x78, /* 0111100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 138 0x8a 'è' */
	0xc0, /* 1100000 */
	0x60, /* 0110000 */
	0x30, /* 0011000 */
	0x00, /* 0000000 */
	0x78, /* 0111100 */
	0xcc, /* 1100110 */
	0xfc, /* 1111110 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0xcc, /* 1100110 */
	0x78, /* 0111100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 139 0x8b 'ï' */
	0x00, /* 0000000 */
	0x6c, /* 0110110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x38, /* 0011100 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x3c, /* 0011110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 140 0x8c 'î' */
	0x30, /* 0011000 */
	0x78, /* 0111100 */
	0xcc, /* 1100110 */
	0x00, /* 0000000 */
	0x70, /* 0111000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x78, /* 0111100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 141 0x8d 'ì' */
	0xc0, /* 1100000 */
	0x60, /* 0110000 */
	0x30, /* 0011000 */
	0x00, /* 0000000 */
	0x70, /* 0111000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x78, /* 0111100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 142 0x8e 'Ä' */
	0x00, /* 0000000 */
	0xcc, /* 1100110 */
	0x00, /* 0000000 */
	0x30, /* 0011000 */
	0x78, /* 0111100 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xfc, /* 1111110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 143 0x8f 'Å' */
	0x30, /* 0011000 */
	0x48, /* 0100100 */
	0x48, /* 0100100 */
	0x30, /* 0011000 */
	0x78, /* 0111100 */
	0xcc, /* 1100110 */
	0xfc, /* 1111110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 144 0x90 'É' */
	0x18, /* 0001100 */
	0x30, /* 0011000 */
	0xfc, /* 1111110 */
	0xcc, /* 1100110 */
	0xc4, /* 1100010 */
	0xd0, /* 1101000 */
	0xf0, /* 1111000 */
	0xd0, /* 1101000 */
	0xc4, /* 1100010 */
	0xcc, /* 1100110 */
	0xfc, /* 1111110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 145 0x91 'æ' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0xec, /* 1110110 */
	0x36, /* 0011011 */
	0x36, /* 0011011 */
	0x7e, /* 0111111 */
	0xd8, /* 1101100 */
	0xd8, /* 1101100 */
	0x6e, /* 0110111 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 146 0x92 'Æ' */
	0x00, /* 0000000 */
	0x3e, /* 0011111 */
	0x6c, /* 0110110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xfe, /* 1111111 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xce, /* 1100111 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 147 0x93 'ô' */
	0x10, /* 0001000 */
	0x38, /* 0011100 */
	0x6c, /* 0110110 */
	0x00, /* 0000000 */
	0x78, /* 0111100 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x78, /* 0111100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 148 0x94 'ö' */
	0x00, /* 0000000 */
	0xcc, /* 1100110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x78, /* 0111100 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x78, /* 0111100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 149 0x95 'ò' */
	0xc0, /* 1100000 */
	0x60, /* 0110000 */
	0x30, /* 0011000 */
	0x00, /* 0000000 */
	0x78, /* 0111100 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x78, /* 0111100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 150 0x96 'û' */
	0x30, /* 0011000 */
	0x78, /* 0111100 */
	0xcc, /* 1100110 */
	0x00, /* 0000000 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x76, /* 0111011 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 151 0x97 'ù' */
	0x60, /* 0110000 */
	0x30, /* 0011000 */
	0x18, /* 0001100 */
	0x00, /* 0000000 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x76, /* 0111011 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 152 0x98 'ÿ' */
	0x00, /* 0000000 */
	0xcc, /* 1100110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x7c, /* 0111110 */
	0x0c, /* 0000110 */
	0x18, /* 0001100 */
	0x70, /* 0111000 */

	/* 153 0x99 'Ö' */
	0xcc, /* 1100110 */
	0x00, /* 0000000 */
	0x78, /* 0111100 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x78, /* 0111100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 154 0x9a 'Ü' */
	0xcc, /* 1100110 */
	0x00, /* 0000000 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x78, /* 0111100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 155 0x9b '¢' */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x7c, /* 0111110 */
	0xcc, /* 1100110 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0xcc, /* 1100110 */
	0x7c, /* 0111110 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 156 0x9c '£' */
	0x38, /* 0011100 */
	0x6c, /* 0110110 */
	0x64, /* 0110010 */
	0x60, /* 0110000 */
	0xf0, /* 1111000 */
	0x60, /* 0110000 */
	0x60, /* 0110000 */
	0x60, /* 0110000 */
	0x60, /* 0110000 */
	0xe6, /* 1110011 */
	0xfc, /* 1111110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 157 0x9d '¥' */
	0x00, /* 0000000 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x78, /* 0111100 */
	0x30, /* 0011000 */
	0xfc, /* 1111110 */
	0x30, /* 0011000 */
	0xfc, /* 1111110 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 158 0x9e '₧' */
	0xf8, /* 1111100 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xf8, /* 1111100 */
	0xc4, /* 1100010 */
	0xcc, /* 1100110 */
	0xde, /* 1101111 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xc6, /* 1100011 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 159 0x9f 'ƒ' */
	0x1c, /* 0001110 */
	0x36, /* 0011011 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0xfc, /* 1111110 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0xb0, /* 1011000 */
	0xe0, /* 1110000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 160 0xa0 'á' */
	0x18, /* 0001100 */
	0x30, /* 0011000 */
	0x60, /* 0110000 */
	0x00, /* 0000000 */
	0x78, /* 0111100 */
	0x0c, /* 0000110 */
	0x7c, /* 0111110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x76, /* 0111011 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 161 0xa1 'í' */
	0x18, /* 0001100 */
	0x30, /* 0011000 */
	0x60, /* 0110000 */
	0x00, /* 0000000 */
	0x70, /* 0111000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x78, /* 0111100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 162 0xa2 'ó' */
	0x18, /* 0001100 */
	0x30, /* 0011000 */
	0x60, /* 0110000 */
	0x00, /* 0000000 */
	0x78, /* 0111100 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x78, /* 0111100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 163 0xa3 'ú' */
	0x18, /* 0001100 */
	0x30, /* 0011000 */
	0x60, /* 0110000 */
	0x00, /* 0000000 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x76, /* 0111011 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 164 0xa4 'ñ' */
	0x00, /* 0000000 */
	0x76, /* 0111011 */
	0xdc, /* 1101110 */
	0x00, /* 0000000 */
	0xb8, /* 1011100 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 165 0xa5 'Ñ' */
	0x76, /* 0111011 */
	0xdc, /* 1101110 */
	0x00, /* 0000000 */
	0xcc, /* 1100110 */
	0xec, /* 1110110 */
	0xec, /* 1110110 */
	0xfc, /* 1111110 */
	0xdc, /* 1101110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 166 0xa6 'ª' */
	0x00, /* 0000000 */
	0x78, /* 0111100 */
	0xd8, /* 1101100 */
	0xd8, /* 1101100 */
	0x7c, /* 0111110 */
	0x00, /* 0000000 */
	0xfc, /* 1111110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 167 0xa7 'º' */
	0x00, /* 0000000 */
	0x70, /* 0111000 */
	0xd8, /* 1101100 */
	0xd8, /* 1101100 */
	0x70, /* 0111000 */
	0x00, /* 0000000 */
	0xf8, /* 1111100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 168 0xa8 '¿' */
	0x00, /* 0000000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x00, /* 0000000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x60, /* 0110000 */
	0xc0, /* 1100000 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x78, /* 0111100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 169 0xa9 '⌐' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0xfc, /* 1111110 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 170 0xaa '¬' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0xfc, /* 1111110 */
	0x0c, /* 0000110 */
	0x0c, /* 0000110 */
	0x0c, /* 0000110 */
	0x0c, /* 0000110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 171 0xab '½' */
	0x60, /* 0110000 */
	0xe0, /* 1110000 */
	0x62, /* 0110001 */
	0x66, /* 0110011 */
	0x6c, /* 0110110 */
	0x18, /* 0001100 */
	0x30, /* 0011000 */
	0x60, /* 0110000 */
	0xc0, /* 1100000 */
	0xb8, /* 1011100 */
	0x4c, /* 0100110 */
	0x18, /* 0001100 */
	0x30, /* 0011000 */
	0x7c, /* 0111110 */

	/* 172 0xac '¼' */
	0x60, /* 0110000 */
	0xe0, /* 1110000 */
	0x62, /* 0110001 */
	0x66, /* 0110011 */
	0x6c, /* 0110110 */
	0x18, /* 0001100 */
	0x30, /* 0011000 */
	0x6c, /* 0110110 */
	0xdc, /* 1101110 */
	0xb4, /* 1011010 */
	0x7e, /* 0111111 */
	0x0c, /* 0000110 */
	0x0c, /* 0000110 */
	0x00, /* 0000000 */

	/* 173 0xad '¡' */
	0x00, /* 0000000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x00, /* 0000000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x78, /* 0111100 */
	0x78, /* 0111100 */
	0x78, /* 0111100 */
	0x30, /* 0011000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 174 0xae '«' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x36, /* 0011011 */
	0x6c, /* 0110110 */
	0xd8, /* 1101100 */
	0x6c, /* 0110110 */
	0x36, /* 0011011 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 175 0xaf '»' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0xd8, /* 1101100 */
	0x6c, /* 0110110 */
	0x36, /* 0011011 */
	0x6c, /* 0110110 */
	0xd8, /* 1101100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 176 0xb0 '░' */
	0x88, /* 1000100 */
	0x22, /* 0010001 */
	0x88, /* 1000100 */
	0x22, /* 0010001 */
	0x88, /* 1000100 */
	0x22, /* 0010001 */
	0x88, /* 1000100 */
	0x22, /* 0010001 */
	0x88, /* 1000100 */
	0x22, /* 0010001 */
	0x88, /* 1000100 */
	0x22, /* 0010001 */
	0x88, /* 1000100 */
	0x22, /* 0010001 */

	/* 177 0xb1 '▒' */
	0x54, /* 0101010 */
	0xaa, /* 1010101 */
	0x54, /* 0101010 */
	0xaa, /* 1010101 */
	0x54, /* 0101010 */
	0xaa, /* 1010101 */
	0x54, /* 0101010 */
	0xaa, /* 1010101 */
	0x54, /* 0101010 */
	0xaa, /* 1010101 */
	0x54, /* 0101010 */
	0xaa, /* 1010101 */
	0x54, /* 0101010 */
	0xaa, /* 1010101 */

	/* 178 0xb2 '▓' */
	0xee, /* 1110111 */
	0xba, /* 1011101 */
	0xee, /* 1110111 */
	0xba, /* 1011101 */
	0xee, /* 1110111 */
	0xba, /* 1011101 */
	0xee, /* 1110111 */
	0xba, /* 1011101 */
	0xee, /* 1110111 */
	0xba, /* 1011101 */
	0xee, /* 1110111 */
	0xba, /* 1011101 */
	0xee, /* 1110111 */
	0xba, /* 1011101 */

	/* 179 0xb3 '│' */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */

	/* 180 0xb4 '┤' */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0xf0, /* 1111000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */

	/* 181 0xb5 '╡' */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0xf0, /* 1111000 */
	0x30, /* 0011000 */
	0xf0, /* 1111000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */

	/* 182 0xb6 '╢' */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0xec, /* 1110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */

	/* 183 0xb7 '╖' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0xfc, /* 1111110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */

	/* 184 0xb8 '╕' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0xf0, /* 1111000 */
	0x30, /* 0011000 */
	0xf0, /* 1111000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */

	/* 185 0xb9 '╣' */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0xec, /* 1110110 */
	0x0c, /* 0000110 */
	0xec, /* 1110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */

	/* 186 0xba '║' */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */

	/* 187 0xbb '╗' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0xfc, /* 1111110 */
	0x0c, /* 0000110 */
	0xec, /* 1110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */

	/* 188 0xbc '╝' */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0xec, /* 1110110 */
	0x0c, /* 0000110 */
	0xfc, /* 1111110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 189 0xbd '╜' */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0xfc, /* 1111110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 190 0xbe '╛' */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0xf0, /* 1111000 */
	0x30, /* 0011000 */
	0xf0, /* 1111000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 191 0xbf '┐' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0xf0, /* 1111000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */

	/* 192 0xc0 '└' */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x3e, /* 0011111 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 193 0xc1 '┴' */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0xfe, /* 1111111 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 194 0xc2 '┬' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0xfe, /* 1111111 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */

	/* 195 0xc3 '├' */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x3e, /* 0011111 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */

	/* 196 0xc4 '─' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0xfe, /* 1111111 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 197 0xc5 '┼' */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0xfe, /* 1111111 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */

	/* 198 0xc6 '╞' */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x3e, /* 0011111 */
	0x30, /* 0011000 */
	0x3e, /* 0011111 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */

	/* 199 0xc7 '╟' */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6e, /* 0110111 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */

	/* 200 0xc8 '╚' */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6e, /* 0110111 */
	0x60, /* 0110000 */
	0x7e, /* 0111111 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 201 0xc9 '╔' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x7e, /* 0111111 */
	0x60, /* 0110000 */
	0x6e, /* 0110111 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */

	/* 202 0xca '╩' */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0xee, /* 1110111 */
	0x00, /* 0000000 */
	0xfe, /* 1111111 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 203 0xcb '╦' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0xfe, /* 1111111 */
	0x00, /* 0000000 */
	0xee, /* 1110111 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */

	/* 204 0xcc '╠' */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6e, /* 0110111 */
	0x60, /* 0110000 */
	0x6e, /* 0110111 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */

	/* 205 0xcd '═' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0xfe, /* 1111111 */
	0x00, /* 0000000 */
	0xfe, /* 1111111 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 206 0xce '╬' */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0xee, /* 1110111 */
	0x00, /* 0000000 */
	0xee, /* 1110111 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */

	/* 207 0xcf '╧' */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0xfe, /* 1111111 */
	0x00, /* 0000000 */
	0xfe, /* 1111111 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 208 0xd0 '╨' */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0xfe, /* 1111111 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 209 0xd1 '╤' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0xfe, /* 1111111 */
	0x00, /* 0000000 */
	0xfe, /* 1111111 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */

	/* 210 0xd2 '╥' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0xfe, /* 1111111 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */

	/* 211 0xd3 '╙' */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x7e, /* 0111111 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 212 0xd4 '╘' */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x3e, /* 0011111 */
	0x30, /* 0011000 */
	0x3e, /* 0011111 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 213 0xd5 '╒' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x3e, /* 0011111 */
	0x30, /* 0011000 */
	0x3e, /* 0011111 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */

	/* 214 0xd6 '╓' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x7e, /* 0111111 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */

	/* 215 0xd7 '╫' */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0xfe, /* 1111111 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */

	/* 216 0xd8 '╪' */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0xfe, /* 1111111 */
	0x30, /* 0011000 */
	0xfe, /* 1111111 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */

	/* 217 0xd9 '┘' */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0xf0, /* 1111000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 218 0xda '┌' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x3e, /* 0011111 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */

	/* 219 0xdb '█' */
	0xfe, /* 1111111 */
	0xfe, /* 1111111 */
	0xfe, /* 1111111 */
	0xfe, /* 1111111 */
	0xfe, /* 1111111 */
	0xfe, /* 1111111 */
	0xfe, /* 1111111 */
	0xfe, /* 1111111 */
	0xfe, /* 1111111 */
	0xfe, /* 1111111 */
	0xfe, /* 1111111 */
	0xfe, /* 1111111 */
	0xfe, /* 1111111 */
	0xfe, /* 1111111 */

	/* 220 0xdc '▄' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0xfe, /* 1111111 */
	0xfe, /* 1111111 */
	0xfe, /* 1111111 */
	0xfe, /* 1111111 */
	0xfe, /* 1111111 */
	0xfe, /* 1111111 */
	0xfe, /* 1111111 */
	0xfe, /* 1111111 */

	/* 221 0xdd '▌' */
	0xe0, /* 1110000 */
	0xe0, /* 1110000 */
	0xe0, /* 1110000 */
	0xe0, /* 1110000 */
	0xe0, /* 1110000 */
	0xe0, /* 1110000 */
	0xe0, /* 1110000 */
	0xe0, /* 1110000 */
	0xe0, /* 1110000 */
	0xe0, /* 1110000 */
	0xe0, /* 1110000 */
	0xe0, /* 1110000 */
	0xe0, /* 1110000 */
	0xe0, /* 1110000 */

	/* 222 0xde '▐' */
	0x1e, /* 0001111 */
	0x1e, /* 0001111 */
	0x1e, /* 0001111 */
	0x1e, /* 0001111 */
	0x1e, /* 0001111 */
	0x1e, /* 0001111 */
	0x1e, /* 0001111 */
	0x1e, /* 0001111 */
	0x1e, /* 0001111 */
	0x1e, /* 0001111 */
	0x1e, /* 0001111 */
	0x1e, /* 0001111 */
	0x1e, /* 0001111 */
	0x1e, /* 0001111 */

	/* 223 0xdf '▀' */
	0xfe, /* 1111111 */
	0xfe, /* 1111111 */
	0xfe, /* 1111111 */
	0xfe, /* 1111111 */
	0xfe, /* 1111111 */
	0xfe, /* 1111111 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 224 0xe0 'α' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x76, /* 0111011 */
	0xdc, /* 1101110 */
	0xd8, /* 1101100 */
	0xd8, /* 1101100 */
	0xd8, /* 1101100 */
	0xdc, /* 1101110 */
	0x76, /* 0111011 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 225 0xe1 'ß' */
	0x00, /* 0000000 */
	0x78, /* 0111100 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xd8, /* 1101100 */
	0xcc, /* 1100110 */
	0xc6, /* 1100011 */
	0xc6, /* 1100011 */
	0xc6, /* 1100011 */
	0xcc, /* 1100110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 226 0xe2 'Γ' */
	0x00, /* 0000000 */
	0xfc, /* 1111110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 227 0xe3 'π' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0xfe, /* 1111111 */
	0xfe, /* 1111111 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 228 0xe4 'Σ' */
	0x00, /* 0000000 */
	0xfc, /* 1111110 */
	0xcc, /* 1100110 */
	0x60, /* 0110000 */
	0x30, /* 0011000 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x30, /* 0011000 */
	0x60, /* 0110000 */
	0xcc, /* 1100110 */
	0xfc, /* 1111110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 229 0xe5 'σ' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x7e, /* 0111111 */
	0xd8, /* 1101100 */
	0xd8, /* 1101100 */
	0xd8, /* 1101100 */
	0xd8, /* 1101100 */
	0xd8, /* 1101100 */
	0x70, /* 0111000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 230 0xe6 'µ' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xf8, /* 1111100 */
	0xc0, /* 1100000 */
	0xc0, /* 1100000 */
	0x80, /* 1000000 */

	/* 231 0xe7 'τ' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x76, /* 0111011 */
	0xdc, /* 1101110 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 232 0xe8 'Φ' */
	0x00, /* 0000000 */
	0xfc, /* 1111110 */
	0x30, /* 0011000 */
	0x78, /* 0111100 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x78, /* 0111100 */
	0x30, /* 0011000 */
	0xfc, /* 1111110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 233 0xe9 'Θ' */
	0x00, /* 0000000 */
	0x38, /* 0011100 */
	0x6c, /* 0110110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xfc, /* 1111110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x6c, /* 0110110 */
	0x38, /* 0011100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 234 0xea 'Ω' */
	0x00, /* 0000000 */
	0x38, /* 0011100 */
	0x6c, /* 0110110 */
	0xc6, /* 1100011 */
	0xc6, /* 1100011 */
	0xc6, /* 1100011 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0xee, /* 1110111 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 235 0xeb 'δ' */
	0x00, /* 0000000 */
	0x3c, /* 0011110 */
	0x60, /* 0110000 */
	0x30, /* 0011000 */
	0x18, /* 0001100 */
	0x7c, /* 0111110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x78, /* 0111100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 236 0xec '∞' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x7c, /* 0111110 */
	0xd6, /* 1101011 */
	0xd6, /* 1101011 */
	0xd6, /* 1101011 */
	0x7c, /* 0111110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 237 0xed 'φ' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x06, /* 0000011 */
	0x0c, /* 0000110 */
	0x7c, /* 0111110 */
	0xd6, /* 1101011 */
	0xd6, /* 1101011 */
	0xe6, /* 1110011 */
	0x7c, /* 0111110 */
	0x60, /* 0110000 */
	0xc0, /* 1100000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 238 0xee 'ε' */
	0x00, /* 0000000 */
	0x1c, /* 0001110 */
	0x30, /* 0011000 */
	0x60, /* 0110000 */
	0x60, /* 0110000 */
	0x7c, /* 0111110 */
	0x60, /* 0110000 */
	0x60, /* 0110000 */
	0x60, /* 0110000 */
	0x30, /* 0011000 */
	0x1c, /* 0001110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 239 0xef '∩' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x78, /* 0111100 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0xcc, /* 1100110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 240 0xf0 '≡' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0xfc, /* 1111110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0xfc, /* 1111110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0xfc, /* 1111110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 241 0xf1 '±' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0xfc, /* 1111110 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0xfc, /* 1111110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 242 0xf2 '≥' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x60, /* 0110000 */
	0x30, /* 0011000 */
	0x18, /* 0001100 */
	0x0c, /* 0000110 */
	0x18, /* 0001100 */
	0x30, /* 0011000 */
	0x60, /* 0110000 */
	0x00, /* 0000000 */
	0xfc, /* 1111110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 243 0xf3 '≤' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x18, /* 0001100 */
	0x30, /* 0011000 */
	0x60, /* 0110000 */
	0xc0, /* 1100000 */
	0x60, /* 0110000 */
	0x30, /* 0011000 */
	0x18, /* 0001100 */
	0x00, /* 0000000 */
	0xfc, /* 1111110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 244 0xf4 '⌠' */
	0x00, /* 0000000 */
	0x1c, /* 0001110 */
	0x36, /* 0011011 */
	0x36, /* 0011011 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */

	/* 245 0xf5 '⌡' */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0xd8, /* 1101100 */
	0xd8, /* 1101100 */
	0xd8, /* 1101100 */
	0x70, /* 0111000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 246 0xf6 '÷' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x30, /* 0011000 */
	0x00, /* 0000000 */
	0xfc, /* 1111110 */
	0x00, /* 0000000 */
	0x30, /* 0011000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 247 0xf7 '≈' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x76, /* 0111011 */
	0xdc, /* 1101110 */
	0x00, /* 0000000 */
	0x76, /* 0111011 */
	0xdc, /* 1101110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 248 0xf8 '°' */
	0x38, /* 0011100 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x38, /* 0011100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 249 0xf9 '·' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x30, /* 0011000 */
	0x30, /* 0011000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 250 0xfa '•' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x30, /* 0011000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 251 0xfb '√' */
	0x1e, /* 0001111 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0x18, /* 0001100 */
	0xd8, /* 1101100 */
	0xd8, /* 1101100 */
	0xd8, /* 1101100 */
	0x78, /* 0111100 */
	0x38, /* 0011100 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 252 0xfc 'ⁿ' */
	0xd8, /* 1101100 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x6c, /* 0110110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 253 0xfd '²' */
	0x78, /* 0111100 */
	0xcc, /* 1100110 */
	0x18, /* 0001100 */
	0x30, /* 0011000 */
	0x64, /* 0110010 */
	0xfc, /* 1111110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 254 0xfe '■' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x7c, /* 0111110 */
	0x7c, /* 0111110 */
	0x7c, /* 0111110 */
	0x7c, /* 0111110 */
	0x7c, /* 0111110 */
	0x7c, /* 0111110 */
	0x7c, /* 0111110 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */

	/* 255 0xff ' ' */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
	0x00, /* 0000000 */
} };


const struct font_desc font_7x14 = {
	.idx	= FONT7x14_IDX,
	.name	= "7x14",
	.width	= 7,
	.height	= 14,
	.charcount = 256,
	.data	= fontdata_7x14.data,
	.pref	= 0,
};
