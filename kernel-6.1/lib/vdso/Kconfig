# SPDX-License-Identifier: GPL-2.0

config HAVE_GENERIC_VDSO
	bool

if HAVE_GENERIC_VDSO

config GENERIC_GETTIMEOFDAY
	bool
	help
	  This is a generic implementation of gettimeofday vdso.
	  Each architecture that enables this feature has to
	  provide the fallback implementation.

config GENERIC_VDSO_32
	bool
	depends on GENERIC_GETTIMEOFDAY && !64BIT
	help
	  This config option helps to avoid possible performance issues
	  in 32 bit only architectures.

config GENERIC_COMPAT_VDSO
	bool
	help
	  This config option enables the compat VDSO layer.

config GENERIC_VDSO_TIME_NS
	bool
	help
	  Selected by architectures which support time namespaces in the
	  VDSO

endif
