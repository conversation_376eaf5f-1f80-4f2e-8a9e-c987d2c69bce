# SPDX-License-Identifier: GPL-2.0-only
#
# RCU-related debugging configuration options
#

menu "RCU Debugging"

config PROVE_RCU
	def_bool PROVE_LOCKING

config PROVE_RCU_LIST
	bool "RCU list lockdep debugging"
	depends on PROVE_RCU && RCU_EXPERT
	default n
	help
	  Enable RCU lockdep checking for list usages. By default it is
	  turned off since there are several list RCU users that still
	  need to be converted to pass a lockdep expression. To prevent
	  false-positive splats, we keep it default disabled but once all
	  users are converted, we can remove this config option.

config TORTURE_TEST
	tristate
	default n

config RCU_SCALE_TEST
	tristate "performance tests for <PERSON>U"
	depends on DEBUG_KERNEL
	select TORTURE_TEST
	select SRCU
	default n
	help
	  This option provides a kernel module that runs performance
	  tests on the RCU infrastructure.  The kernel module may be built
	  after the fact on the running kernel to be tested, if desired.

	  Say Y here if you want RCU performance tests to be built into
	  the kernel.
	  Say M if you want the RCU performance tests to build as a module.
	  Say N if you are unsure.

config RCU_TORTURE_TEST
	tristate "torture tests for <PERSON><PERSON>"
	depends on <PERSON>BUG_KERNEL
	select TORTURE_TEST
	select SRCU
	default n
	help
	  This option provides a kernel module that runs torture tests
	  on the RCU infrastructure.  The kernel module may be built
	  after the fact on the running kernel to be tested, if desired.

	  Say Y here if you want RCU torture tests to be built into
	  the kernel.
	  Say M if you want the RCU torture tests to build as a module.
	  Say N if you are unsure.

config RCU_REF_SCALE_TEST
	tristate "Scalability tests for read-side synchronization (RCU and others)"
	depends on DEBUG_KERNEL
	select TORTURE_TEST
	select SRCU
	default n
	help
	  This option provides a kernel module that runs performance tests
	  useful comparing RCU with various read-side synchronization mechanisms.
	  The kernel module may be built after the fact on the running kernel to be
	  tested, if desired.

	  Say Y here if you want these performance tests built into the kernel.
	  Say M if you want to build it as a module instead.
	  Say N if you are unsure.

config RCU_CPU_STALL_TIMEOUT
	int "RCU CPU stall timeout in seconds"
	depends on RCU_STALL_COMMON
	range 3 300
	default 21
	help
	  If a given RCU grace period extends more than the specified
	  number of seconds, a CPU stall warning is printed.  If the
	  RCU grace period persists, additional CPU stall warnings are
	  printed at more widely spaced intervals.

config BOOTPARAM_RCU_STALL_PANIC
	bool "Panic (Reboot) On RCU CPU stall timeout"
	depends on RCU_STALL_COMMON
	help
	  Say Y here to enable the kernel to panic on "rcu stall timeout",
	  which are bugs that RCU grace period extends more than 21 seconds
	  (configurable using the RCU_CPU_STALL_TIMEOUT).

	  The panic can be used in combination with panic_timeout,
	  to cause the system to reboot automatically after a
	  stall timeout. This feature is useful for high-availability
	  systems that have uptime guarantees and where a stall timeout
	  must be resolved ASAP.

	  Say N if unsure.

config BOOTPARAM_RCU_STALL_PANIC_VALUE
	int
	depends on RCU_STALL_COMMON
	range 0 1
	default 0 if !BOOTPARAM_RCU_STALL_PANIC
	default 1 if BOOTPARAM_RCU_STALL_PANIC

config RCU_EXP_CPU_STALL_TIMEOUT
	int "Expedited RCU CPU stall timeout in milliseconds"
	depends on RCU_STALL_COMMON
	range 0 21000
	default 0
	help
	  If a given expedited RCU grace period extends more than the
	  specified number of milliseconds, a CPU stall warning is printed.
	  If the RCU grace period persists, additional CPU stall warnings
	  are printed at more widely spaced intervals.  A value of zero
	  says to use the RCU_CPU_STALL_TIMEOUT value converted from
	  seconds to milliseconds.

config RCU_TRACE
	bool "Enable tracing for RCU"
	depends on DEBUG_KERNEL
	default y if TREE_RCU
	select TRACE_CLOCK
	help
	  This option enables additional tracepoints for ftrace-style
	  event tracing.

	  Say Y here if you want to enable RCU tracing
	  Say N if you are unsure.

config RCU_EQS_DEBUG
	bool "Provide debugging asserts for adding NO_HZ support to an arch"
	depends on DEBUG_KERNEL
	help
	  This option provides consistency checks in RCU's handling of
	  NO_HZ.  These checks have proven quite helpful in detecting
	  bugs in arch-specific NO_HZ code.

	  Say N here if you need ultimate kernel/user switch latencies
	  Say Y if you are unsure

config RCU_STRICT_GRACE_PERIOD
	bool "Provide debug RCU implementation with short grace periods"
	depends on DEBUG_KERNEL && RCU_EXPERT && NR_CPUS <= 4 && !TINY_RCU
	default n
	select PREEMPT_COUNT if PREEMPT=n
	help
	  Select this option to build an RCU variant that is strict about
	  grace periods, making them as short as it can.  This limits
	  scalability, destroys real-time response, degrades battery
	  lifetime and kills performance.  Don't try this on large
	  machines, as in systems with more than about 10 or 20 CPUs.
	  But in conjunction with tools like KASAN, it can be helpful
	  when looking for certain types of RCU usage bugs, for example,
	  too-short RCU read-side critical sections.

endmenu # "RCU Debugging"
