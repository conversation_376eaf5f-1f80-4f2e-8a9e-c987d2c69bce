# SPDX-License-Identifier: GPL-2.0-only
#
# Timer Interrupt Frequency Configuration
#

choice
	prompt "Timer frequency"
	default HZ_250
	help
	 Allows the configuration of the timer frequency. It is customary
	 to have the timer interrupt run at 1000 Hz but 100 Hz may be more
	 beneficial for servers and NUMA systems that do not need to have
	 a fast response for user interaction and that may experience bus
	 contention and cacheline bounces as a result of timer interrupts.
	 Note that the timer interrupt occurs on each processor in an SMP
	 environment leading to NR_CPUS * HZ number of timer interrupts
	 per second.


	config HZ_100
		bool "100 HZ"
	help
	  100 Hz is a typical choice for servers, SMP and NUMA systems
	  with lots of processors that may show reduced performance if
	  too many timer interrupts are occurring.

	config HZ_250
		bool "250 HZ"
	help
	 250 Hz is a good compromise choice allowing server performance
	 while also showing good interactive responsiveness even
	 on SMP and NUMA systems. If you are going to be using NTSC video
	 or multimedia, selected 300Hz instead.

	config HZ_300
		bool "300 HZ"
	help
	 300 Hz is a good compromise choice allowing server performance
	 while also showing good interactive responsiveness even
	 on SMP and NUMA systems and exactly dividing by both PAL and
	 NTSC frame rates for video and multimedia work.

	config HZ_1000
		bool "1000 HZ"
	help
	 1000 Hz is the preferred choice for desktop systems and other
	 systems requiring fast interactive responses to events.

endchoice

config HZ
	int
	default 100 if HZ_100
	default 250 if HZ_250
	default 300 if HZ_300
	default 1000 if HZ_1000

config SCHED_HRTICK
	def_bool HIGH_RES_TIMERS
