# SPDX-License-Identifier: GPL-2.0-only
#
# The ARCH_INLINE foo is necessary because select ignores "depends on"
#
config ARCH_INLINE_SPIN_TRYLOCK
	bool

config ARCH_INLINE_SPIN_TRYLOCK_BH
	bool

config ARCH_INLINE_SPIN_LOCK
	bool

config ARCH_INLINE_SPIN_LOCK_BH
	bool

config ARCH_INLINE_SPIN_LOCK_IRQ
	bool

config ARCH_INLINE_SPIN_LOCK_IRQSAVE
	bool

config ARCH_INLINE_SPIN_UNLOCK
	bool

config ARCH_INLINE_SPIN_UNLOCK_BH
	bool

config ARCH_INLINE_SPIN_UNLOCK_IRQ
	bool

config ARCH_INLINE_SPIN_UNLOCK_IRQRESTORE
	bool


config ARCH_INLINE_READ_TRYLOCK
	bool

config ARCH_INLINE_READ_LOCK
	bool

config ARCH_INLINE_READ_LOCK_BH
	bool

config ARCH_INLINE_READ_LOCK_IRQ
	bool

config ARCH_INLINE_READ_LOCK_IRQSAVE
	bool

config ARCH_INLINE_READ_UNLOCK
	bool

config ARCH_INLINE_READ_UNLOCK_BH
	bool

config ARCH_INLINE_READ_UNLOCK_IRQ
	bool

config ARCH_INLINE_READ_UNLOCK_IRQRESTORE
	bool


config ARCH_INLINE_WRITE_TRYLOCK
	bool

config ARCH_INLINE_WRITE_LOCK
	bool

config ARCH_INLINE_WRITE_LOCK_BH
	bool

config ARCH_INLINE_WRITE_LOCK_IRQ
	bool

config ARCH_INLINE_WRITE_LOCK_IRQSAVE
	bool

config ARCH_INLINE_WRITE_UNLOCK
	bool

config ARCH_INLINE_WRITE_UNLOCK_BH
	bool

config ARCH_INLINE_WRITE_UNLOCK_IRQ
	bool

config ARCH_INLINE_WRITE_UNLOCK_IRQRESTORE
	bool

config UNINLINE_SPIN_UNLOCK
	bool

#
# lock_* functions are inlined when:
#   - DEBUG_SPINLOCK=n and GENERIC_LOCKBREAK=n and ARCH_INLINE_*LOCK=y
#
# trylock_* functions are inlined when:
#   - DEBUG_SPINLOCK=n and ARCH_INLINE_*LOCK=y
#
# unlock and unlock_irq functions are inlined when:
#   - DEBUG_SPINLOCK=n and ARCH_INLINE_*LOCK=y
#  or
#   - DEBUG_SPINLOCK=n and PREEMPTION=n
#
# unlock_bh and unlock_irqrestore functions are inlined when:
#   - DEBUG_SPINLOCK=n and ARCH_INLINE_*LOCK=y
#

if !DEBUG_SPINLOCK

config INLINE_SPIN_TRYLOCK
	def_bool y
	depends on ARCH_INLINE_SPIN_TRYLOCK

config INLINE_SPIN_TRYLOCK_BH
	def_bool y
	depends on ARCH_INLINE_SPIN_TRYLOCK_BH

config INLINE_SPIN_LOCK
	def_bool y
	depends on !GENERIC_LOCKBREAK && ARCH_INLINE_SPIN_LOCK

config INLINE_SPIN_LOCK_BH
	def_bool y
	depends on !GENERIC_LOCKBREAK && ARCH_INLINE_SPIN_LOCK_BH

config INLINE_SPIN_LOCK_IRQ
	def_bool y
	depends on !GENERIC_LOCKBREAK && ARCH_INLINE_SPIN_LOCK_IRQ

config INLINE_SPIN_LOCK_IRQSAVE
	def_bool y
	depends on !GENERIC_LOCKBREAK && ARCH_INLINE_SPIN_LOCK_IRQSAVE

config INLINE_SPIN_UNLOCK_BH
	def_bool y
	depends on ARCH_INLINE_SPIN_UNLOCK_BH

config INLINE_SPIN_UNLOCK_IRQ
	def_bool y
	depends on !PREEMPTION || ARCH_INLINE_SPIN_UNLOCK_IRQ

config INLINE_SPIN_UNLOCK_IRQRESTORE
	def_bool y
	depends on ARCH_INLINE_SPIN_UNLOCK_IRQRESTORE


config INLINE_READ_TRYLOCK
	def_bool y
	depends on ARCH_INLINE_READ_TRYLOCK

config INLINE_READ_LOCK
	def_bool y
	depends on !GENERIC_LOCKBREAK && ARCH_INLINE_READ_LOCK

config INLINE_READ_LOCK_BH
	def_bool y
	depends on !GENERIC_LOCKBREAK && ARCH_INLINE_READ_LOCK_BH

config INLINE_READ_LOCK_IRQ
	def_bool y
	depends on !GENERIC_LOCKBREAK && ARCH_INLINE_READ_LOCK_IRQ

config INLINE_READ_LOCK_IRQSAVE
	def_bool y
	depends on !GENERIC_LOCKBREAK && ARCH_INLINE_READ_LOCK_IRQSAVE

config INLINE_READ_UNLOCK
	def_bool y
	depends on !PREEMPTION || ARCH_INLINE_READ_UNLOCK

config INLINE_READ_UNLOCK_BH
	def_bool y
	depends on ARCH_INLINE_READ_UNLOCK_BH

config INLINE_READ_UNLOCK_IRQ
	def_bool y
	depends on !PREEMPTION || ARCH_INLINE_READ_UNLOCK_IRQ

config INLINE_READ_UNLOCK_IRQRESTORE
	def_bool y
	depends on ARCH_INLINE_READ_UNLOCK_IRQRESTORE


config INLINE_WRITE_TRYLOCK
	def_bool y
	depends on ARCH_INLINE_WRITE_TRYLOCK

config INLINE_WRITE_LOCK
	def_bool y
	depends on !GENERIC_LOCKBREAK && ARCH_INLINE_WRITE_LOCK

config INLINE_WRITE_LOCK_BH
	def_bool y
	depends on !GENERIC_LOCKBREAK && ARCH_INLINE_WRITE_LOCK_BH

config INLINE_WRITE_LOCK_IRQ
	def_bool y
	depends on !GENERIC_LOCKBREAK && ARCH_INLINE_WRITE_LOCK_IRQ

config INLINE_WRITE_LOCK_IRQSAVE
	def_bool y
	depends on !GENERIC_LOCKBREAK && ARCH_INLINE_WRITE_LOCK_IRQSAVE

config INLINE_WRITE_UNLOCK
	def_bool y
	depends on !PREEMPTION || ARCH_INLINE_WRITE_UNLOCK

config INLINE_WRITE_UNLOCK_BH
	def_bool y
	depends on ARCH_INLINE_WRITE_UNLOCK_BH

config INLINE_WRITE_UNLOCK_IRQ
	def_bool y
	depends on !PREEMPTION || ARCH_INLINE_WRITE_UNLOCK_IRQ

config INLINE_WRITE_UNLOCK_IRQRESTORE
	def_bool y
	depends on ARCH_INLINE_WRITE_UNLOCK_IRQRESTORE

endif

config ARCH_SUPPORTS_ATOMIC_RMW
	bool

config MUTEX_SPIN_ON_OWNER
	def_bool y
	depends on SMP && ARCH_SUPPORTS_ATOMIC_RMW

config RWSEM_SPIN_ON_OWNER
       def_bool y
       depends on SMP && ARCH_SUPPORTS_ATOMIC_RMW

config LOCK_SPIN_ON_OWNER
       def_bool y
       depends on MUTEX_SPIN_ON_OWNER || RWSEM_SPIN_ON_OWNER

config ARCH_USE_QUEUED_SPINLOCKS
	bool

config QUEUED_SPINLOCKS
	def_bool y if ARCH_USE_QUEUED_SPINLOCKS
	depends on SMP

config BPF_ARCH_SPINLOCK
	bool

config ARCH_USE_QUEUED_RWLOCKS
	bool

config QUEUED_RWLOCKS
	def_bool y if ARCH_USE_QUEUED_RWLOCKS
	depends on SMP && !PREEMPT_RT

config ARCH_HAS_MMIOWB
	bool

config MMIOWB
	def_bool y if ARCH_HAS_MMIOWB
	depends on SMP
