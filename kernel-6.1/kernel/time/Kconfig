# SPDX-License-Identifier: GPL-2.0-only
#
# Timer subsystem related configuration options
#

# Options selectable by arch Kconfig

# Watchdog function for clocksources to detect instabilities
config CLOCKSOURCE_WATCHDOG
	bool

# Architecture has extra clocksource data
config ARCH_CLOCKSOURCE_DATA
	bool

# Architecture has extra clocksource init called from registration
config ARCH_CLOCKSOURCE_INIT
	bool

# Clocksources require validation of the clocksource against the last
# cycle update - x86/TSC misfeature
config CLOCKSOURCE_VALIDATE_LAST_CYCLE
	bool

# Timekeeping vsyscall support
config GENERIC_TIME_VSYSCALL
	bool

# The generic clock events infrastructure
config GENERIC_CLOCKEVENTS
	def_bool !LEGACY_TIMER_TICK

# Architecture can handle broadcast in a driver-agnostic way
config ARCH_HAS_TICK_BROADCAST
	bool

# Clockevents broadcasting infrastructure
config GENERIC_CLOCKEVENTS_BROADCAST
	bool
	depends on GENERIC_CLOCKEVENTS

# Automatically adjust the min. reprogramming time for
# clock event device
config GENERIC_CLOCKEVENTS_MIN_ADJUST
	bool

# Generic update of CMOS clock
config GENERIC_CMOS_UPDATE
	bool

# Select to handle posix CPU timers from task_work
# and not from the timer interrupt context
config HAVE_POSIX_CPU_TIMERS_TASK_WORK
	bool

config POSIX_CPU_TIMERS_TASK_WORK
	bool
	default y if POSIX_TIMERS && HAVE_POSIX_CPU_TIMERS_TASK_WORK

config LEGACY_TIMER_TICK
	bool
	help
	  The legacy timer tick helper is used by platforms that
	  lack support for the generic clockevent framework.
	  New platforms should use generic clockevents instead.

config TIME_KUNIT_TEST
	tristate "KUnit test for kernel/time functions" if !KUNIT_ALL_TESTS
	depends on KUNIT
	default KUNIT_ALL_TESTS
	help
	  Enable this option to test RTC library functions.

	  If unsure, say N.

config CONTEXT_TRACKING
	bool

config CONTEXT_TRACKING_IDLE
	bool
	select CONTEXT_TRACKING
	help
	  Tracks idle state on behalf of RCU.

if GENERIC_CLOCKEVENTS
menu "Timers subsystem"

# Core internal switch. Selected by NO_HZ_COMMON / HIGH_RES_TIMERS. This is
# only related to the tick functionality. Oneshot clockevent devices
# are supported independent of this.
config TICK_ONESHOT
	bool

config NO_HZ_COMMON
	bool
	select TICK_ONESHOT

choice
	prompt "Timer tick handling"
	default NO_HZ_IDLE if NO_HZ

config HZ_PERIODIC
	bool "Periodic timer ticks (constant rate, no dynticks)"
	help
	  This option keeps the tick running periodically at a constant
	  rate, even when the CPU doesn't need it.

config NO_HZ_IDLE
	bool "Idle dynticks system (tickless idle)"
	select NO_HZ_COMMON
	help
	  This option enables a tickless idle system: timer interrupts
	  will only trigger on an as-needed basis when the system is idle.
	  This is usually interesting for energy saving.

	  Most of the time you want to say Y here.

config NO_HZ_FULL
	bool "Full dynticks system (tickless)"
	# NO_HZ_COMMON dependency
	# We need at least one periodic CPU for timekeeping
	depends on SMP
	depends on HAVE_CONTEXT_TRACKING_USER
	# VIRT_CPU_ACCOUNTING_GEN dependency
	depends on HAVE_VIRT_CPU_ACCOUNTING_GEN
	select NO_HZ_COMMON
	select RCU_NOCB_CPU
	select VIRT_CPU_ACCOUNTING_GEN
	select IRQ_WORK
	select CPU_ISOLATION
	help
	 Adaptively try to shutdown the tick whenever possible, even when
	 the CPU is running tasks. Typically this requires running a single
	 task on the CPU. Chances for running tickless are maximized when
	 the task mostly runs in userspace and has few kernel activity.

	 You need to fill up the nohz_full boot parameter with the
	 desired range of dynticks CPUs to use it. This is implemented at
	 the expense of some overhead in user <-> kernel transitions:
	 syscalls, exceptions and interrupts.

	 By default, without passing the nohz_full parameter, this behaves just
	 like NO_HZ_IDLE.

	 If you're a distro say Y.

endchoice

config CONTEXT_TRACKING_USER
	bool
	depends on HAVE_CONTEXT_TRACKING_USER
	select CONTEXT_TRACKING
	help
	  Track transitions between kernel and user on behalf of RCU and
	  tickless cputime accounting. The former case relies on context
	  tracking to enter/exit RCU extended quiescent states.

config CONTEXT_TRACKING_USER_FORCE
	bool "Force user context tracking"
	depends on CONTEXT_TRACKING_USER
	default y if !NO_HZ_FULL
	help
	  The major pre-requirement for full dynticks to work is to
	  support the user context tracking subsystem. But there are also
	  other dependencies to provide in order to make the full
	  dynticks working.

	  This option stands for testing when an arch implements the
	  user context tracking backend but doesn't yet fulfill all the
	  requirements to make the full dynticks feature working.
	  Without the full dynticks, there is no way to test the support
	  for user context tracking and the subsystems that rely on it: RCU
	  userspace extended quiescent state and tickless cputime
	  accounting. This option copes with the absence of the full
	  dynticks subsystem by forcing the user context tracking on all
	  CPUs in the system.

	  Say Y only if you're working on the development of an
	  architecture backend for the user context tracking.

	  Say N otherwise, this option brings an overhead that you
	  don't want in production.

config NO_HZ
	bool "Old Idle dynticks config"
	help
	  This is the old config entry that enables dynticks idle.
	  We keep it around for a little while to enforce backward
	  compatibility with older config files.

config HIGH_RES_TIMERS
	bool "High Resolution Timer Support"
	select TICK_ONESHOT
	help
	  This option enables high resolution timer support. If your
	  hardware is not capable then this option only increases
	  the size of the kernel image.

config CLOCKSOURCE_WATCHDOG_MAX_SKEW_US
	int "Clocksource watchdog maximum allowable skew (in μs)"
	depends on CLOCKSOURCE_WATCHDOG
	range 50 1000
	default 100
	help
	  Specify the maximum amount of allowable watchdog skew in
	  microseconds before reporting the clocksource to be unstable.

endmenu
endif
