/******************************************************************************
 *
 * Copyright(c) 2007 - 2017 Realtek Corporation.
 *
 * This program is free software; you can redistribute it and/or modify it
 * under the terms of version 2 of the GNU General Public License as
 * published by the Free Software Foundation.
 *
 * This program is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or
 * FITNESS FOR A PARTICULAR PURPOSE. See the GNU General Public License for
 * more details.
 *
 *****************************************************************************/
#ifndef __HAL_PHY_REG_H__
#define __HAL_PHY_REG_H__

/* for PutRFRegsetting & GetRFRegSetting BitMask*/
#define		bRFRegOffsetMask	0xfffff

/* alias for phydm coding style */
#define REG_OFDM_0_XA_TX_IQ_IMBALANCE	rOFDM0_XATxIQImbalance
#define REG_OFDM_0_ECCA_THRESHOLD		rOFDM0_ECCAThreshold
#define REG_FPGA0_XB_LSSI_READ_BACK		rFPGA0_XB_LSSIReadBack
#define REG_FPGA0_TX_GAIN_STAGE			rFPGA0_TxGainStage
#define REG_OFDM_0_XA_AGC_CORE1			rOFDM0_XAAGCCore1
#define REG_OFDM_0_XB_AGC_CORE1			rOFDM0_XBAGCCore1
#define REG_A_TX_SCALE_JAGUAR			rA_TxScale_Jaguar
#define REG_B_TX_SCALE_JAGUAR			rB_TxScale_Jaguar

#define REG_FPGA0_XAB_RF_INTERFACE_SW	rFPGA0_XAB_RFInterfaceSW
#define REG_FPGA0_XAB_RF_PARAMETER	rFPGA0_XAB_RFParameter
#define REG_FPGA0_XA_HSSI_PARAMETER1	rFPGA0_XA_HSSIParameter1
#define REG_FPGA0_XA_LSSI_PARAMETER	rFPGA0_XA_LSSIParameter
#define REG_FPGA0_XA_RF_INTERFACE_OE	rFPGA0_XA_RFInterfaceOE
#define REG_FPGA0_XB_HSSI_PARAMETER1	rFPGA0_XB_HSSIParameter1
#define REG_FPGA0_XB_LSSI_PARAMETER	rFPGA0_XB_LSSIParameter
#define REG_FPGA0_XB_LSSI_READ_BACK	rFPGA0_XB_LSSIReadBack
#define REG_FPGA0_XB_RF_INTERFACE_OE	rFPGA0_XB_RFInterfaceOE
#define REG_FPGA0_XCD_RF_INTERFACE_SW	rFPGA0_XCD_RFInterfaceSW
#define REG_FPGA0_XCD_SWITCH_CONTROL	rFPGA0_XCD_SwitchControl
#define REG_FPGA1_TX_BLOCK	rFPGA1_TxBlock
#define REG_FPGA1_TX_INFO	rFPGA1_TxInfo
#define REG_IQK_AGC_CONT	rIQK_AGC_Cont
#define REG_IQK_AGC_PTS	rIQK_AGC_Pts
#define REG_IQK_AGC_RSP	rIQK_AGC_Rsp
#define REG_OFDM_0_AGC_RSSI_TABLE	rOFDM0_AGCRSSITable
#define REG_OFDM_0_ECCA_THRESHOLD	rOFDM0_ECCAThreshold
#define REG_OFDM_0_RX_IQ_EXT_ANTA	rOFDM0_RxIQExtAnta
#define REG_OFDM_0_TR_MUX_PAR	rOFDM0_TRMuxPar
#define REG_OFDM_0_TRX_PATH_ENABLE	rOFDM0_TRxPathEnable
#define REG_OFDM_0_XA_AGC_CORE1	rOFDM0_XAAGCCore1
#define REG_OFDM_0_XA_RX_IQ_IMBALANCE	rOFDM0_XARxIQImbalance
#define REG_OFDM_0_XA_TX_IQ_IMBALANCE	rOFDM0_XATxIQImbalance
#define REG_OFDM_0_XB_AGC_CORE1	rOFDM0_XBAGCCore1
#define REG_OFDM_0_XB_RX_IQ_IMBALANCE	rOFDM0_XBRxIQImbalance
#define REG_OFDM_0_XB_TX_IQ_IMBALANCE	rOFDM0_XBTxIQImbalance
#define REG_OFDM_0_XC_TX_AFE	rOFDM0_XCTxAFE
#define REG_OFDM_0_XD_TX_AFE	rOFDM0_XDTxAFE

/*#define REG_A_CFO_LONG_DUMP_92E	rA_CfoLongDump_92E*/
#define REG_A_CFO_LONG_DUMP_JAGUAR	rA_CfoLongDump_Jaguar
/*#define REG_A_CFO_SHORT_DUMP_92E	rA_CfoShortDump_92E*/
#define REG_A_CFO_SHORT_DUMP_JAGUAR	rA_CfoShortDump_Jaguar
#define REG_A_RFE_PINMUX_JAGUAR	rA_RFE_Pinmux_Jaguar
/*#define REG_A_RSSI_DUMP_92E	rA_RSSIDump_92E*/
#define REG_A_RSSI_DUMP_JAGUAR	rA_RSSIDump_Jaguar
/*#define REG_A_RX_SNR_DUMP_92E	rA_RXsnrDump_92E*/
#define REG_A_RX_SNR_DUMP_JAGUAR	rA_RXsnrDump_Jaguar
/*#define REG_A_TX_AGC	rA_TXAGC*/
#define REG_A_TX_SCALE_JAGUAR	rA_TxScale_Jaguar
#define REG_BW_INDICATION_JAGUAR	rBWIndication_Jaguar
/*#define REG_B_BBSWING	rB_BBSWING*/
/*#define REG_B_CFO_LONG_DUMP_92E	rB_CfoLongDump_92E*/
#define REG_B_CFO_LONG_DUMP_JAGUAR	rB_CfoLongDump_Jaguar
/*#define REG_B_CFO_SHORT_DUMP_92E	rB_CfoShortDump_92E*/
#define REG_B_CFO_SHORT_DUMP_JAGUAR	rB_CfoShortDump_Jaguar
/*#define REG_B_RSSI_DUMP_92E	rB_RSSIDump_92E*/
#define REG_B_RSSI_DUMP_JAGUAR	rB_RSSIDump_Jaguar
/*#define REG_B_RX_SNR_DUMP_92E	rB_RXsnrDump_92E*/
#define REG_B_RX_SNR_DUMP_JAGUAR	rB_RXsnrDump_Jaguar
/*#define REG_B_TX_AGC	rB_TXAGC*/
#define REG_B_TX_SCALE_JAGUAR	rB_TxScale_Jaguar
#define REG_BLUE_TOOTH	rBlue_Tooth
#define REG_CCK_0_AFE_SETTING	rCCK0_AFESetting
/*#define REG_C_BBSWING	rC_BBSWING*/
/*#define REG_C_TX_AGC	rC_TXAGC*/
#define REG_C_TX_SCALE_JAGUAR2	rC_TxScale_Jaguar2
#define REG_CONFIG_ANT_A	rConfig_AntA
#define REG_CONFIG_ANT_B	rConfig_AntB
#define REG_CONFIG_PMPD_ANT_A	rConfig_Pmpd_AntA
#define REG_CONFIG_PMPD_ANT_B	rConfig_Pmpd_AntB
#define REG_DPDT_CONTROL	rDPDT_control
/*#define REG_D_BBSWING	rD_BBSWING*/
/*#define REG_D_TX_AGC	rD_TXAGC*/
#define REG_D_TX_SCALE_JAGUAR2	rD_TxScale_Jaguar2
#define REG_FPGA0_ANALOG_PARAMETER4	rFPGA0_AnalogParameter4
#define REG_FPGA0_IQK	rFPGA0_IQK
#define REG_FPGA0_PSD_FUNCTION	rFPGA0_PSDFunction
#define REG_FPGA0_PSD_REPORT	rFPGA0_PSDReport
#define REG_FPGA0_RFMOD	rFPGA0_RFMOD
#define REG_FPGA0_TX_GAIN_STAGE	rFPGA0_TxGainStage
#define REG_FPGA0_XAB_RF_INTERFACE_SW	rFPGA0_XAB_RFInterfaceSW
#define REG_FPGA0_XAB_RF_PARAMETER	rFPGA0_XAB_RFParameter
#define REG_FPGA0_XA_HSSI_PARAMETER1	rFPGA0_XA_HSSIParameter1
#define REG_FPGA0_XA_LSSI_PARAMETER	rFPGA0_XA_LSSIParameter
#define REG_FPGA0_XA_RF_INTERFACE_OE	rFPGA0_XA_RFInterfaceOE
#define REG_FPGA0_XB_HSSI_PARAMETER1	rFPGA0_XB_HSSIParameter1
#define REG_FPGA0_XB_LSSI_PARAMETER	rFPGA0_XB_LSSIParameter
#define REG_FPGA0_XB_LSSI_READ_BACK	rFPGA0_XB_LSSIReadBack
#define REG_FPGA0_XB_RF_INTERFACE_OE	rFPGA0_XB_RFInterfaceOE
#define REG_FPGA0_XCD_RF_INTERFACE_SW	rFPGA0_XCD_RFInterfaceSW
#define REG_FPGA0_XCD_SWITCH_CONTROL	rFPGA0_XCD_SwitchControl
#define REG_FPGA1_TX_BLOCK	rFPGA1_TxBlock
#define REG_FPGA1_TX_INFO	rFPGA1_TxInfo
#define REG_IQK_AGC_CONT	rIQK_AGC_Cont
#define REG_IQK_AGC_PTS	rIQK_AGC_Pts
#define REG_IQK_AGC_RSP	rIQK_AGC_Rsp
#define REG_OFDM_0_AGC_RSSI_TABLE	rOFDM0_AGCRSSITable
#define REG_OFDM_0_ECCA_THRESHOLD	rOFDM0_ECCAThreshold
#define REG_OFDM_0_RX_IQ_EXT_ANTA	rOFDM0_RxIQExtAnta
#define REG_OFDM_0_TR_MUX_PAR	rOFDM0_TRMuxPar
#define REG_OFDM_0_TRX_PATH_ENABLE	rOFDM0_TRxPathEnable
#define REG_OFDM_0_XA_AGC_CORE1	rOFDM0_XAAGCCore1
#define REG_OFDM_0_XA_RX_IQ_IMBALANCE	rOFDM0_XARxIQImbalance
#define REG_OFDM_0_XA_TX_IQ_IMBALANCE	rOFDM0_XATxIQImbalance
#define REG_OFDM_0_XB_AGC_CORE1	rOFDM0_XBAGCCore1
#define REG_OFDM_0_XB_RX_IQ_IMBALANCE	rOFDM0_XBRxIQImbalance
#define REG_OFDM_0_XB_TX_IQ_IMBALANCE	rOFDM0_XBTxIQImbalance
#define REG_OFDM_0_XC_TX_AFE	rOFDM0_XCTxAFE
#define REG_OFDM_0_XD_TX_AFE	rOFDM0_XDTxAFE
#define REG_PMPD_ANAEN	rPMPD_ANAEN
#define REG_PDP_ANT_A	rPdp_AntA
#define REG_PDP_ANT_A_4	rPdp_AntA_4
#define REG_PDP_ANT_B	rPdp_AntB
#define REG_PDP_ANT_B_4	rPdp_AntB_4
#define REG_PWED_TH_JAGUAR	rPwed_TH_Jaguar
#define REG_RX_CCK	rRx_CCK
#define REG_RX_IQK	rRx_IQK
#define REG_RX_IQK_PI_A	rRx_IQK_PI_A
#define REG_RX_IQK_PI_B	rRx_IQK_PI_B
#define REG_RX_IQK_TONE_A	rRx_IQK_Tone_A
#define REG_RX_IQK_TONE_B	rRx_IQK_Tone_B
#define REG_RX_OFDM	rRx_OFDM
#define REG_RX_POWER_AFTER_IQK_A_2	rRx_Power_After_IQK_A_2
#define REG_RX_POWER_AFTER_IQK_B_2	rRx_Power_After_IQK_B_2
#define REG_RX_POWER_BEFORE_IQK_A_2	rRx_Power_Before_IQK_A_2
#define REG_RX_POWER_BEFORE_IQK_B_2	rRx_Power_Before_IQK_B_2
#define REG_RX_TO_RX	rRx_TO_Rx
#define REG_RX_WAIT_CCA	rRx_Wait_CCA
#define REG_RX_WAIT_RIFS	rRx_Wait_RIFS
#define REG_S0_S1_PATH_SWITCH	rS0S1_PathSwitch
/*#define REG_S1_RXEVM_DUMP_92E	rS1_RXevmDump_92E*/
#define REG_S1_RXEVM_DUMP_JAGUAR	rS1_RXevmDump_Jaguar
/*#define REG_S2_RXEVM_DUMP_92E	rS2_RXevmDump_92E*/
#define REG_S2_RXEVM_DUMP_JAGUAR	rS2_RXevmDump_Jaguar
#define REG_SYM_WLBT_PAPE_SEL	rSYM_WLBT_PAPE_SEL
#define REG_SINGLE_TONE_CONT_TX_JAGUAR	rSingleTone_ContTx_Jaguar
#define REG_SLEEP	rSleep
#define REG_STANDBY	rStandby
#define REG_TX_AGC_A_CCK_11_CCK_1_JAGUAR	rTxAGC_A_CCK11_CCK1_JAguar
#define REG_TX_AGC_A_CCK_1_MCS32	rTxAGC_A_CCK1_Mcs32
#define REG_TX_AGC_A_MCS11_MCS8_JAGUAR	rTxAGC_A_MCS11_MCS8_JAguar
#define REG_TX_AGC_A_MCS15_MCS12_JAGUAR	rTxAGC_A_MCS15_MCS12_JAguar
#define REG_TX_AGC_A_MCS19_MCS16_JAGUAR	rTxAGC_A_MCS19_MCS16_JAguar
#define REG_TX_AGC_A_MCS23_MCS20_JAGUAR	rTxAGC_A_MCS23_MCS20_JAguar
#define REG_TX_AGC_A_MCS3_MCS0_JAGUAR	rTxAGC_A_MCS3_MCS0_JAguar
#define REG_TX_AGC_A_MCS7_MCS4_JAGUAR	rTxAGC_A_MCS7_MCS4_JAguar
#define REG_TX_AGC_A_MCS03_MCS00	rTxAGC_A_Mcs03_Mcs00
#define REG_TX_AGC_A_MCS07_MCS04	rTxAGC_A_Mcs07_Mcs04
#define REG_TX_AGC_A_MCS11_MCS08	rTxAGC_A_Mcs11_Mcs08
#define REG_TX_AGC_A_MCS15_MCS12	rTxAGC_A_Mcs15_Mcs12
#define REG_TX_AGC_A_NSS1_INDEX3_NSS1_INDEX0_JAGUAR	rTxAGC_A_Nss1Index3_Nss1Index0_JAguar
#define REG_TX_AGC_A_NSS1_INDEX7_NSS1_INDEX4_JAGUAR	rTxAGC_A_Nss1Index7_Nss1Index4_JAguar
#define REG_TX_AGC_A_NSS2_INDEX1_NSS1_INDEX8_JAGUAR	rTxAGC_A_Nss2Index1_Nss1Index8_JAguar
#define REG_TX_AGC_A_NSS2_INDEX5_NSS2_INDEX2_JAGUAR	rTxAGC_A_Nss2Index5_Nss2Index2_JAguar
#define REG_TX_AGC_A_NSS2_INDEX9_NSS2_INDEX6_JAGUAR	rTxAGC_A_Nss2Index9_Nss2Index6_JAguar
#define REG_TX_AGC_A_NSS3_INDEX3_NSS3_INDEX0_JAGUAR	rTxAGC_A_Nss3Index3_Nss3Index0_JAguar
#define REG_TX_AGC_A_NSS3_INDEX7_NSS3_INDEX4_JAGUAR	rTxAGC_A_Nss3Index7_Nss3Index4_JAguar
#define REG_TX_AGC_A_NSS3_INDEX9_NSS3_INDEX8_JAGUAR	rTxAGC_A_Nss3Index9_Nss3Index8_JAguar
#define REG_TX_AGC_A_OFDM18_OFDM6_JAGUAR	rTxAGC_A_Ofdm18_Ofdm6_JAguar
#define REG_TX_AGC_A_OFDM54_OFDM24_JAGUAR	rTxAGC_A_Ofdm54_Ofdm24_JAguar
#define REG_TX_AGC_A_RATE18_06	rTxAGC_A_Rate18_06
#define REG_TX_AGC_A_RATE54_24	rTxAGC_A_Rate54_24
#define REG_TX_AGC_B_CCK_11_A_CCK_2_11	rTxAGC_B_CCK11_A_CCK2_11
#define REG_TX_AGC_B_CCK_11_CCK_1_JAGUAR	rTxAGC_B_CCK11_CCK1_JAguar
#define REG_TX_AGC_B_CCK_1_55_MCS32	rTxAGC_B_CCK1_55_Mcs32
#define REG_TX_AGC_B_MCS11_MCS8_JAGUAR	rTxAGC_B_MCS11_MCS8_JAguar
#define REG_TX_AGC_B_MCS15_MCS12_JAGUAR	rTxAGC_B_MCS15_MCS12_JAguar
#define REG_TX_AGC_B_MCS19_MCS16_JAGUAR	rTxAGC_B_MCS19_MCS16_JAguar
#define REG_TX_AGC_B_MCS23_MCS20_JAGUAR	rTxAGC_B_MCS23_MCS20_JAguar
#define REG_TX_AGC_B_MCS3_MCS0_JAGUAR	rTxAGC_B_MCS3_MCS0_JAguar
#define REG_TX_AGC_B_MCS7_MCS4_JAGUAR	rTxAGC_B_MCS7_MCS4_JAguar
#define REG_TX_AGC_B_MCS03_MCS00	rTxAGC_B_Mcs03_Mcs00
#define REG_TX_AGC_B_MCS07_MCS04	rTxAGC_B_Mcs07_Mcs04
#define REG_TX_AGC_B_MCS11_MCS08	rTxAGC_B_Mcs11_Mcs08
#define REG_TX_AGC_B_MCS15_MCS12	rTxAGC_B_Mcs15_Mcs12
#define REG_TX_AGC_B_NSS1_INDEX3_NSS1_INDEX0_JAGUAR	rTxAGC_B_Nss1Index3_Nss1Index0_JAguar
#define REG_TX_AGC_B_NSS1_INDEX7_NSS1_INDEX4_JAGUAR	rTxAGC_B_Nss1Index7_Nss1Index4_JAguar
#define REG_TX_AGC_B_NSS2_INDEX1_NSS1_INDEX8_JAGUAR	rTxAGC_B_Nss2Index1_Nss1Index8_JAguar
#define REG_TX_AGC_B_NSS2_INDEX5_NSS2_INDEX2_JAGUAR	rTxAGC_B_Nss2Index5_Nss2Index2_JAguar
#define REG_TX_AGC_B_NSS2_INDEX9_NSS2_INDEX6_JAGUAR	rTxAGC_B_Nss2Index9_Nss2Index6_JAguar
#define REG_TX_AGC_B_NSS3_INDEX3_NSS3_INDEX0_JAGUAR	rTxAGC_B_Nss3Index3_Nss3Index0_JAguar
#define REG_TX_AGC_B_NSS3_INDEX7_NSS3_INDEX4_JAGUAR	rTxAGC_B_Nss3Index7_Nss3Index4_JAguar
#define REG_TX_AGC_B_NSS3_INDEX9_NSS3_INDEX8_JAGUAR	rTxAGC_B_Nss3Index9_Nss3Index8_JAguar
#define REG_TX_AGC_B_OFDM18_OFDM6_JAGUAR	rTxAGC_B_Ofdm18_Ofdm6_JAguar
#define REG_TX_AGC_B_OFDM54_OFDM24_JAGUAR	rTxAGC_B_Ofdm54_Ofdm24_JAguar
#define REG_TX_AGC_B_RATE18_06	rTxAGC_B_Rate18_06
#define REG_TX_AGC_B_RATE54_24	rTxAGC_B_Rate54_24
#define REG_TX_AGC_C_CCK_11_CCK_1_JAGUAR	rTxAGC_C_CCK11_CCK1_JAguar
#define REG_TX_AGC_C_MCS11_MCS8_JAGUAR	rTxAGC_C_MCS11_MCS8_JAguar
#define REG_TX_AGC_C_MCS15_MCS12_JAGUAR	rTxAGC_C_MCS15_MCS12_JAguar
#define REG_TX_AGC_C_MCS19_MCS16_JAGUAR	rTxAGC_C_MCS19_MCS16_JAguar
#define REG_TX_AGC_C_MCS23_MCS20_JAGUAR	rTxAGC_C_MCS23_MCS20_JAguar
#define REG_TX_AGC_C_MCS3_MCS0_JAGUAR	rTxAGC_C_MCS3_MCS0_JAguar
#define REG_TX_AGC_C_MCS7_MCS4_JAGUAR	rTxAGC_C_MCS7_MCS4_JAguar
#define REG_TX_AGC_C_NSS1_INDEX3_NSS1_INDEX0_JAGUAR	rTxAGC_C_Nss1Index3_Nss1Index0_JAguar
#define REG_TX_AGC_C_NSS1_INDEX7_NSS1_INDEX4_JAGUAR	rTxAGC_C_Nss1Index7_Nss1Index4_JAguar
#define REG_TX_AGC_C_NSS2_INDEX1_NSS1_INDEX8_JAGUAR	rTxAGC_C_Nss2Index1_Nss1Index8_JAguar
#define REG_TX_AGC_C_NSS2_INDEX5_NSS2_INDEX2_JAGUAR	rTxAGC_C_Nss2Index5_Nss2Index2_JAguar
#define REG_TX_AGC_C_NSS2_INDEX9_NSS2_INDEX6_JAGUAR	rTxAGC_C_Nss2Index9_Nss2Index6_JAguar
#define REG_TX_AGC_C_NSS3_INDEX3_NSS3_INDEX0_JAGUAR	rTxAGC_C_Nss3Index3_Nss3Index0_JAguar
#define REG_TX_AGC_C_NSS3_INDEX7_NSS3_INDEX4_JAGUAR	rTxAGC_C_Nss3Index7_Nss3Index4_JAguar
#define REG_TX_AGC_C_NSS3_INDEX9_NSS3_INDEX8_JAGUAR	rTxAGC_C_Nss3Index9_Nss3Index8_JAguar
#define REG_TX_AGC_C_OFDM18_OFDM6_JAGUAR	rTxAGC_C_Ofdm18_Ofdm6_JAguar
#define REG_TX_AGC_C_OFDM54_OFDM24_JAGUAR	rTxAGC_C_Ofdm54_Ofdm24_JAguar
#define REG_TX_AGC_D_CCK_11_CCK_1_JAGUAR	rTxAGC_D_CCK11_CCK1_JAguar
#define REG_TX_AGC_D_MCS11_MCS8_JAGUAR	rTxAGC_D_MCS11_MCS8_JAguar
#define REG_TX_AGC_D_MCS15_MCS12_JAGUAR	rTxAGC_D_MCS15_MCS12_JAguar
#define REG_TX_AGC_D_MCS19_MCS16_JAGUAR	rTxAGC_D_MCS19_MCS16_JAguar
#define REG_TX_AGC_D_MCS23_MCS20_JAGUAR	rTxAGC_D_MCS23_MCS20_JAguar
#define REG_TX_AGC_D_MCS3_MCS0_JAGUAR	rTxAGC_D_MCS3_MCS0_JAguar
#define REG_TX_AGC_D_MCS7_MCS4_JAGUAR	rTxAGC_D_MCS7_MCS4_JAguar
#define REG_TX_AGC_D_NSS1_INDEX3_NSS1_INDEX0_JAGUAR	rTxAGC_D_Nss1Index3_Nss1Index0_JAguar
#define REG_TX_AGC_D_NSS1_INDEX7_NSS1_INDEX4_JAGUAR	rTxAGC_D_Nss1Index7_Nss1Index4_JAguar
#define REG_TX_AGC_D_NSS2_INDEX1_NSS1_INDEX8_JAGUAR	rTxAGC_D_Nss2Index1_Nss1Index8_JAguar
#define REG_TX_AGC_D_NSS2_INDEX5_NSS2_INDEX2_JAGUAR	rTxAGC_D_Nss2Index5_Nss2Index2_JAguar
#define REG_TX_AGC_D_NSS2_INDEX9_NSS2_INDEX6_JAGUAR	rTxAGC_D_Nss2Index9_Nss2Index6_JAguar
#define REG_TX_AGC_D_NSS3_INDEX3_NSS3_INDEX0_JAGUAR	rTxAGC_D_Nss3Index3_Nss3Index0_JAguar
#define REG_TX_AGC_D_NSS3_INDEX7_NSS3_INDEX4_JAGUAR	rTxAGC_D_Nss3Index7_Nss3Index4_JAguar
#define REG_TX_AGC_D_NSS3_INDEX9_NSS3_INDEX8_JAGUAR	rTxAGC_D_Nss3Index9_Nss3Index8_JAguar
#define REG_TX_AGC_D_OFDM18_OFDM6_JAGUAR	rTxAGC_D_Ofdm18_Ofdm6_JAguar
#define REG_TX_AGC_D_OFDM54_OFDM24_JAGUAR	rTxAGC_D_Ofdm54_Ofdm24_JAguar
#define REG_TX_PATH_JAGUAR	rTxPath_Jaguar
#define REG_TX_CCK_BBON	rTx_CCK_BBON
#define REG_TX_CCK_RFON	rTx_CCK_RFON
#define REG_TX_IQK	rTx_IQK
#define REG_TX_IQK_PI_A	rTx_IQK_PI_A
#define REG_TX_IQK_PI_B	rTx_IQK_PI_B
#define REG_TX_IQK_TONE_A	rTx_IQK_Tone_A
#define REG_TX_IQK_TONE_B	rTx_IQK_Tone_B
#define REG_TX_OFDM_BBON	rTx_OFDM_BBON
#define REG_TX_OFDM_RFON	rTx_OFDM_RFON
#define REG_TX_POWER_AFTER_IQK_A	rTx_Power_After_IQK_A
#define REG_TX_POWER_AFTER_IQK_B	rTx_Power_After_IQK_B
#define REG_TX_POWER_BEFORE_IQK_A	rTx_Power_Before_IQK_A
#define REG_TX_POWER_BEFORE_IQK_B	rTx_Power_Before_IQK_B
#define REG_TX_TO_RX	rTx_To_Rx
#define REG_TX_TO_TX	rTx_To_Tx
#define REG_APK	rAPK
#define REG_ANTSEL_SW_JAGUAR	r_ANTSEL_SW_Jaguar

#define rf_welut_jaguar	RF_WeLut_Jaguar
#define rf_mode_table_addr	RF_ModeTableAddr
#define rf_mode_table_data0	RF_ModeTableData0
#define rf_mode_table_data1	RF_ModeTableData1

#define RX_SMOOTH_FACTOR	Rx_Smooth_Factor

#endif /* __HAL_PHY_REG_H__ */
