/******************************************************************************
 *
 * Copyright(c) Semiconductor - 2017 Realtek Corporation.
 *
 * This program is free software; you can redistribute it and/or modify it
 * under the terms of version 2 of the GNU General Public License as
 * published by the Free Software Foundation.
 *
 * This program is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or
 * FITNESS FOR A PARTICULAR PURPOSE. See the GNU General Public License for
 * more details.
 *
 *****************************************************************************/


#include "Hal8188FPwrSeq.h"


/*
    drivers should parse below arrays and do the corresponding actions
*/
/*3 Power on  Array */
WLAN_PWR_CFG rtl8188F_power_on_flow[RTL8188F_TRANS_CARDEMU_TO_ACT_STEPS + RTL8188F_TRANS_END_STEPS] = {
	RTL8188F_TRANS_CARDEMU_TO_ACT
	RTL8188F_TRANS_END
};

/*3Radio off GPIO Array */
WLAN_PWR_CFG rtl8188F_radio_off_flow[RTL8188F_TRANS_ACT_TO_CARDEMU_STEPS + RTL8188F_TRANS_END_STEPS] = {
	RTL8188F_TRANS_ACT_TO_CARDEMU
	RTL8188F_TRANS_END
};

/*3Card Disable Array */
WLAN_PWR_CFG rtl8188F_card_disable_flow[RTL8188F_TRANS_ACT_TO_CARDEMU_STEPS + RTL8188F_TRANS_CARDEMU_TO_PDN_STEPS + RTL8188F_TRANS_END_STEPS] = {
	RTL8188F_TRANS_ACT_TO_CARDEMU
	RTL8188F_TRANS_CARDEMU_TO_CARDDIS
	RTL8188F_TRANS_END
};

/*3 Card Enable Array */
WLAN_PWR_CFG rtl8188F_card_enable_flow[RTL8188F_TRANS_ACT_TO_CARDEMU_STEPS + RTL8188F_TRANS_CARDEMU_TO_PDN_STEPS + RTL8188F_TRANS_END_STEPS] = {
	RTL8188F_TRANS_CARDDIS_TO_CARDEMU
	RTL8188F_TRANS_CARDEMU_TO_ACT
	RTL8188F_TRANS_END
};

/*3Suspend Array */
WLAN_PWR_CFG rtl8188F_suspend_flow[RTL8188F_TRANS_ACT_TO_CARDEMU_STEPS + RTL8188F_TRANS_CARDEMU_TO_SUS_STEPS + RTL8188F_TRANS_END_STEPS] = {
	RTL8188F_TRANS_ACT_TO_CARDEMU
	RTL8188F_TRANS_CARDEMU_TO_SUS
	RTL8188F_TRANS_END
};

/*3 Resume Array */
WLAN_PWR_CFG rtl8188F_resume_flow[RTL8188F_TRANS_ACT_TO_CARDEMU_STEPS + RTL8188F_TRANS_CARDEMU_TO_SUS_STEPS + RTL8188F_TRANS_END_STEPS] = {
	RTL8188F_TRANS_SUS_TO_CARDEMU
	RTL8188F_TRANS_CARDEMU_TO_ACT
	RTL8188F_TRANS_END
};



/*3HWPDN Array */
WLAN_PWR_CFG rtl8188F_hwpdn_flow[RTL8188F_TRANS_ACT_TO_CARDEMU_STEPS + RTL8188F_TRANS_CARDEMU_TO_PDN_STEPS + RTL8188F_TRANS_END_STEPS] = {
	RTL8188F_TRANS_ACT_TO_CARDEMU
	RTL8188F_TRANS_CARDEMU_TO_PDN
	RTL8188F_TRANS_END
};

/*3 Enter LPS */
WLAN_PWR_CFG rtl8188F_enter_lps_flow[RTL8188F_TRANS_ACT_TO_LPS_STEPS + RTL8188F_TRANS_END_STEPS] = {
	/*FW behavior */
	RTL8188F_TRANS_ACT_TO_LPS
	RTL8188F_TRANS_END
};

/*3 Leave LPS */
WLAN_PWR_CFG rtl8188F_leave_lps_flow[RTL8188F_TRANS_LPS_TO_ACT_STEPS + RTL8188F_TRANS_END_STEPS] = {
	/*FW behavior */
	RTL8188F_TRANS_LPS_TO_ACT
	RTL8188F_TRANS_END
};

/*3 Enter SW LPS */
WLAN_PWR_CFG rtl8188F_enter_swlps_flow[RTL8188F_TRANS_ACT_TO_SWLPS_STEPS + RTL8188F_TRANS_END_STEPS] = {
	/*SW behavior */
	RTL8188F_TRANS_ACT_TO_SWLPS
	RTL8188F_TRANS_END
};

/*3 Leave SW LPS */
WLAN_PWR_CFG rtl8188F_leave_swlps_flow[RTL8188F_TRANS_SWLPS_TO_ACT_STEPS + RTL8188F_TRANS_END_STEPS] = {
	/*SW behavior */
	RTL8188F_TRANS_SWLPS_TO_ACT
	RTL8188F_TRANS_END
};
