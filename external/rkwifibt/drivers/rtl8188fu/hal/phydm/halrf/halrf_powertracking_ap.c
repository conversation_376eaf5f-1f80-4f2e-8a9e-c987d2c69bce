/******************************************************************************
 *
 * Copyright(c) 2007 - 2017 Realtek Corporation.
 *
 * This program is free software; you can redistribute it and/or modify it
 * under the terms of version 2 of the GNU General Public License as
 * published by the Free Software Foundation.
 *
 * This program is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or
 * FITNESS FOR A PARTICULAR PURPOSE. See the GNU General Public License for
 * more details.
 *
 *****************************************************************************/

/* ************************************************************
 * include files
 * ************************************************************ */
#include "mp_precomp.h"
#include "phydm_precomp.h"

#if !defined(_OUTSRC_COEXIST)
/* ************************************************************
 * Global var
 * ************************************************************ */


u32 ofdm_swing_table_new[OFDM_TABLE_SIZE_92D] = {
	0x0b40002d, /* 0,  -15.0dB */
	0x0c000030, /* 1,  -14.5dB */
	0x0cc00033, /* 2,  -14.0dB */
	0x0d800036, /* 3,  -13.5dB */
	0x0e400039, /* 4,  -13.0dB */
	0x0f00003c, /* 5,  -12.5dB */
	0x10000040, /* 6,  -12.0dB */
	0x11000044, /* 7,  -11.5dB */
	0x12000048, /* 8,  -11.0dB */
	0x1300004c, /* 9,  -10.5dB */
	0x14400051, /* 10, -10.0dB */
	0x15800056, /* 11, -9.5dB */
	0x16c0005b, /* 12, -9.0dB */
	0x18000060, /* 13, -8.5dB */
	0x19800066, /* 14, -8.0dB */
	0x1b00006c, /* 15, -7.5dB */
	0x1c800072, /* 16, -7.0dB */
	0x1e400079, /* 17, -6.5dB */
	0x20000080, /* 18, -6.0dB */
	0x22000088, /* 19, -5.5dB */
	0x24000090, /* 20, -5.0dB */
	0x26000098, /* 21, -4.5dB */
	0x288000a2, /* 22, -4.0dB */
	0x2ac000ab, /* 23, -3.5dB */
	0x2d4000b5, /* 24, -3.0dB */
	0x300000c0, /* 25, -2.5dB */
	0x32c000cb, /* 26, -2.0dB */
	0x35c000d7, /* 27, -1.5dB */
	0x390000e4, /* 28, -1.0dB */
	0x3c8000f2, /* 29, -0.5dB */
	0x40000100, /* 30, +0dB */
	0x43c0010f, /* 31, +0.5dB */
	0x47c0011f, /* 32, +1.0dB */
	0x4c000130, /* 33, +1.5dB */
	0x50800142, /* 34, +2.0dB */
	0x55400155, /* 35, +2.5dB */
	0x5a400169, /* 36, +3.0dB */
	0x5fc0017f, /* 37, +3.5dB */
	0x65400195, /* 38, +4.0dB */
	0x6b8001ae, /* 39, +4.5dB */
	0x71c001c7, /* 40, +5.0dB */
	0x788001e2, /* 41, +5.5dB */
	0x7f8001fe  /* 42, +6.0dB */
};

u8 cck_swing_table_ch1_ch13_new[CCK_TABLE_SIZE][8] = {
	{0x09, 0x08, 0x07, 0x06, 0x04, 0x03, 0x01, 0x01},	/* 0, -16.0dB */
	{0x09, 0x09, 0x08, 0x06, 0x05, 0x03, 0x01, 0x01},	/* 1, -15.5dB */
	{0x0a, 0x09, 0x08, 0x07, 0x05, 0x03, 0x02, 0x01},	/* 2, -15.0dB */
	{0x0a, 0x0a, 0x09, 0x07, 0x05, 0x03, 0x02, 0x01},	/* 3, -14.5dB */
	{0x0b, 0x0a, 0x09, 0x08, 0x06, 0x04, 0x02, 0x01},	/* 4, -14.0dB */
	{0x0b, 0x0b, 0x0a, 0x08, 0x06, 0x04, 0x02, 0x01},	/* 5, -13.5dB */
	{0x0c, 0x0c, 0x0a, 0x09, 0x06, 0x04, 0x02, 0x01},	/* 6, -13.0dB */
	{0x0d, 0x0c, 0x0b, 0x09, 0x07, 0x04, 0x02, 0x01},	/* 7, -12.5dB */
	{0x0d, 0x0d, 0x0c, 0x0a, 0x07, 0x05, 0x02, 0x01},	/* 8, -12.0dB */
	{0x0e, 0x0e, 0x0c, 0x0a, 0x08, 0x05, 0x02, 0x01},	/* 9, -11.5dB */
	{0x0f, 0x0f, 0x0d, 0x0b, 0x08, 0x05, 0x03, 0x01},	/* 10, -11.0dB */
	{0x10, 0x10, 0x0e, 0x0b, 0x08, 0x05, 0x03, 0x01},	/* 11, -10.5dB */
	{0x11, 0x11, 0x0f, 0x0c, 0x09, 0x06, 0x03, 0x01},	/* 12, -10.0dB */
	{0x12, 0x12, 0x0f, 0x0c, 0x09, 0x06, 0x03, 0x01},	/* 13, -9.5dB */
	{0x13, 0x13, 0x10, 0x0d, 0x0a, 0x06, 0x03, 0x01},	/* 14, -9.0dB */
	{0x14, 0x14, 0x11, 0x0e, 0x0b, 0x07, 0x03, 0x02},	/* 15, -8.5dB */
	{0x16, 0x15, 0x12, 0x0f, 0x0b, 0x07, 0x04, 0x01},	/* 16, -8.0dB */
	{0x17, 0x16, 0x13, 0x10, 0x0c, 0x08, 0x04, 0x02},	/* 17, -7.5dB */
	{0x18, 0x17, 0x15, 0x11, 0x0c, 0x08, 0x04, 0x02},	/* 18, -7.0dB */
	{0x1a, 0x19, 0x16, 0x12, 0x0d, 0x09, 0x04, 0x02},	/* 19, -6.5dB */
	{0x1c, 0x1a, 0x18, 0x12, 0x0e, 0x08, 0x04, 0x02},	/* 20, -6.0dB */
	{0x1d, 0x1c, 0x18, 0x14, 0x0f, 0x0a, 0x05, 0x02},	/* 21, -5.5dB */
	{0x1f, 0x1e, 0x1a, 0x15, 0x10, 0x0a, 0x05, 0x02},	/* 22, -5.0dB */
	{0x20, 0x20, 0x1b, 0x16, 0x11, 0x08, 0x05, 0x02},	/* 23, -4.5dB */
	{0x22, 0x21, 0x1d, 0x18, 0x11, 0x0b, 0x06, 0x02},	/* 24, -4.0dB */
	{0x24, 0x23, 0x1f, 0x19, 0x13, 0x0c, 0x06, 0x03},	/* 25, -3.5dB */
	{0x26, 0x25, 0x21, 0x1b, 0x14, 0x0d, 0x06, 0x03},	/* 26, -3.0dB */
	{0x28, 0x28, 0x22, 0x1c, 0x15, 0x0d, 0x07, 0x03},	/* 27, -2.5dB */
	{0x2b, 0x2a, 0x25, 0x1e, 0x16, 0x0e, 0x07, 0x03},	/* 28, -2.0dB */
	{0x2d, 0x2d, 0x27, 0x1f, 0x18, 0x0f, 0x08, 0x03},	/* 29, -1.5dB */
	{0x30, 0x2f, 0x29, 0x21, 0x19, 0x10, 0x08, 0x03},	/* 30, -1.0dB */
	{0x33, 0x32, 0x2b, 0x23, 0x1a, 0x11, 0x08, 0x04},	/* 31, -0.5dB */
	{0x36, 0x35, 0x2e, 0x25, 0x1c, 0x12, 0x09, 0x04}	/* 32, +0dB */
};


u8 cck_swing_table_ch14_new[CCK_TABLE_SIZE][8] = {
	{0x09, 0x08, 0x07, 0x04, 0x00, 0x00, 0x00, 0x00},	/* 0, -16.0dB */
	{0x09, 0x09, 0x08, 0x05, 0x00, 0x00, 0x00, 0x00},	/* 1, -15.5dB */
	{0x0a, 0x09, 0x08, 0x05, 0x00, 0x00, 0x00, 0x00},	/* 2, -15.0dB */
	{0x0a, 0x0a, 0x09, 0x05, 0x00, 0x00, 0x00, 0x00},	/* 3, -14.5dB */
	{0x0b, 0x0a, 0x09, 0x05, 0x00, 0x00, 0x00, 0x00},	/* 4, -14.0dB */
	{0x0b, 0x0b, 0x0a, 0x06, 0x00, 0x00, 0x00, 0x00},	/* 5, -13.5dB */
	{0x0c, 0x0c, 0x0a, 0x06, 0x00, 0x00, 0x00, 0x00},	/* 6, -13.0dB */
	{0x0d, 0x0c, 0x0b, 0x06, 0x00, 0x00, 0x00, 0x00},	/* 7, -12.5dB */
	{0x0d, 0x0d, 0x0c, 0x07, 0x00, 0x00, 0x00, 0x00},	/* 8, -12.0dB */
	{0x0e, 0x0e, 0x0c, 0x07, 0x00, 0x00, 0x00, 0x00},	/* 9, -11.5dB */
	{0x0f, 0x0f, 0x0d, 0x08, 0x00, 0x00, 0x00, 0x00},	/* 10, -11.0dB */
	{0x10, 0x10, 0x0e, 0x08, 0x00, 0x00, 0x00, 0x00},	/* 11, -10.5dB */
	{0x11, 0x11, 0x0f, 0x09, 0x00, 0x00, 0x00, 0x00},	/* 12, -10.0dB */
	{0x12, 0x12, 0x0f, 0x09, 0x00, 0x00, 0x00, 0x00},	/* 13, -9.5dB */
	{0x13, 0x13, 0x10, 0x0a, 0x00, 0x00, 0x00, 0x00},	/* 14, -9.0dB */
	{0x14, 0x14, 0x11, 0x0a, 0x00, 0x00, 0x00, 0x00},	/* 15, -8.5dB */
	{0x16, 0x15, 0x12, 0x0b, 0x00, 0x00, 0x00, 0x00},	/* 16, -8.0dB */
	{0x17, 0x16, 0x13, 0x0b, 0x00, 0x00, 0x00, 0x00},	/* 17, -7.5dB */
	{0x18, 0x17, 0x15, 0x0c, 0x00, 0x00, 0x00, 0x00},	/* 18, -7.0dB */
	{0x1a, 0x19, 0x16, 0x0d, 0x00, 0x00, 0x00, 0x00},	/* 19, -6.5dB */
	{0x1c, 0x1a, 0x18, 0x0e, 0x00, 0x00, 0x00, 0x00},	/* 20, -6.0dB */
	{0x1d, 0x1c, 0x18, 0x0e, 0x00, 0x00, 0x00, 0x00},	/* 21, -5.5dB */
	{0x1f, 0x1e, 0x1a, 0x0f, 0x00, 0x00, 0x00, 0x00},	/* 22, -5.0dB */
	{0x20, 0x20, 0x1b, 0x10, 0x00, 0x00, 0x00, 0x00},	/* 23, -4.5dB */
	{0x22, 0x21, 0x1d, 0x11, 0x00, 0x00, 0x00, 0x00},	/* 24, -4.0dB */
	{0x24, 0x23, 0x1f, 0x12, 0x00, 0x00, 0x00, 0x00},	/* 25, -3.5dB */
	{0x26, 0x25, 0x21, 0x13, 0x00, 0x00, 0x00, 0x00},	/* 26, -3.0dB */
	{0x28, 0x28, 0x24, 0x14, 0x00, 0x00, 0x00, 0x00},	/* 27, -2.5dB */
	{0x2b, 0x2a, 0x25, 0x15, 0x00, 0x00, 0x00, 0x00},	/* 28, -2.0dB */
	{0x2d, 0x2d, 0x17, 0x17, 0x00, 0x00, 0x00, 0x00},	/* 29, -1.5dB */
	{0x30, 0x2f, 0x29, 0x18, 0x00, 0x00, 0x00, 0x00},	/* 30, -1.0dB */
	{0x33, 0x32, 0x2b, 0x19, 0x00, 0x00, 0x00, 0x00},	/* 31, -0.5dB */
	{0x36, 0x35, 0x2e, 0x1b, 0x00, 0x00, 0x00, 0x00}	/* 32, +0dB */
};

u32 ofdm_swing_table[OFDM_TABLE_SIZE_92D] = {
	0x0b40002d, /* 0,  -15.0dB */
	0x0c000030, /* 1,  -14.5dB */
	0x0cc00033, /* 2,  -14.0dB */
	0x0d800036, /* 3,  -13.5dB */
	0x0e400039, /* 4,  -13.0dB */
	0x0f00003c, /* 5,  -12.5dB */
	0x10000040, /* 6,  -12.0dB */
	0x11000044, /* 7,  -11.5dB */
	0x12000048, /* 8,  -11.0dB */
	0x1300004c, /* 9,  -10.5dB */
	0x14400051, /* 10, -10.0dB */
	0x15800056, /* 11, -9.5dB */
	0x16c0005b, /* 12, -9.0dB */
	0x18000060, /* 13, -8.5dB */
	0x19800066, /* 14, -8.0dB */
	0x1b00006c, /* 15, -7.5dB */
	0x1c800072, /* 16, -7.0dB */
	0x1e400079, /* 17, -6.5dB */
	0x20000080, /* 18, -6.0dB */
	0x22000088, /* 19, -5.5dB */
	0x24000090, /* 20, -5.0dB */
	0x26000098, /* 21, -4.5dB */
	0x288000a2, /* 22, -4.0dB */
	0x2ac000ab, /* 23, -3.5dB */
	0x2d4000b5, /* 24, -3.0dB */
	0x300000c0, /* 25, -2.5dB */
	0x32c000cb, /* 26, -2.0dB */
	0x35c000d7, /* 27, -1.5dB */
	0x390000e4, /* 28, -1.0dB */
	0x3c8000f2, /* 29, -0.5dB */
	0x40000100, /* 30, +0dB */
	0x43c0010f, /* 31, +0.5dB */
	0x47c0011f, /* 32, +1.0dB */
	0x4c000130, /* 33, +1.5dB */
	0x50800142, /* 34, +2.0dB */
	0x55400155, /* 35, +2.5dB */
	0x5a400169, /* 36, +3.0dB */
	0x5fc0017f, /* 37, +3.5dB */
	0x65400195, /* 38, +4.0dB */
	0x6b8001ae, /* 39, +4.5dB */
	0x71c001c7, /* 40, +5.0dB */
	0x788001e2, /* 41, +5.5dB */
	0x7f8001fe  /* 42, +6.0dB */
};


u8 cck_swing_table_ch1_ch13[CCK_TABLE_SIZE][8] = {
	{0x09, 0x08, 0x07, 0x06, 0x04, 0x03, 0x01, 0x01},	/* 0, -16.0dB */
	{0x09, 0x09, 0x08, 0x06, 0x05, 0x03, 0x01, 0x01},	/* 1, -15.5dB */
	{0x0a, 0x09, 0x08, 0x07, 0x05, 0x03, 0x02, 0x01},	/* 2, -15.0dB */
	{0x0a, 0x0a, 0x09, 0x07, 0x05, 0x03, 0x02, 0x01},	/* 3, -14.5dB */
	{0x0b, 0x0a, 0x09, 0x08, 0x06, 0x04, 0x02, 0x01},	/* 4, -14.0dB */
	{0x0b, 0x0b, 0x0a, 0x08, 0x06, 0x04, 0x02, 0x01},	/* 5, -13.5dB */
	{0x0c, 0x0c, 0x0a, 0x09, 0x06, 0x04, 0x02, 0x01},	/* 6, -13.0dB */
	{0x0d, 0x0c, 0x0b, 0x09, 0x07, 0x04, 0x02, 0x01},	/* 7, -12.5dB */
	{0x0d, 0x0d, 0x0c, 0x0a, 0x07, 0x05, 0x02, 0x01},	/* 8, -12.0dB */
	{0x0e, 0x0e, 0x0c, 0x0a, 0x08, 0x05, 0x02, 0x01},	/* 9, -11.5dB */
	{0x0f, 0x0f, 0x0d, 0x0b, 0x08, 0x05, 0x03, 0x01},	/* 10, -11.0dB */
	{0x10, 0x10, 0x0e, 0x0b, 0x08, 0x05, 0x03, 0x01},	/* 11, -10.5dB */
	{0x11, 0x11, 0x0f, 0x0c, 0x09, 0x06, 0x03, 0x01},	/* 12, -10.0dB */
	{0x12, 0x12, 0x0f, 0x0c, 0x09, 0x06, 0x03, 0x01},	/* 13, -9.5dB */
	{0x13, 0x13, 0x10, 0x0d, 0x0a, 0x06, 0x03, 0x01},	/* 14, -9.0dB */
	{0x14, 0x14, 0x11, 0x0e, 0x0b, 0x07, 0x03, 0x02},	/* 15, -8.5dB */
	{0x16, 0x15, 0x12, 0x0f, 0x0b, 0x07, 0x04, 0x01},	/* 16, -8.0dB */
	{0x17, 0x16, 0x13, 0x10, 0x0c, 0x08, 0x04, 0x02},	/* 17, -7.5dB */
	{0x18, 0x17, 0x15, 0x11, 0x0c, 0x08, 0x04, 0x02},	/* 18, -7.0dB */
	{0x1a, 0x19, 0x16, 0x12, 0x0d, 0x09, 0x04, 0x02},	/* 19, -6.5dB */
	{0x1c, 0x1a, 0x18, 0x12, 0x0e, 0x08, 0x04, 0x02},	/* 20, -6.0dB */
	{0x1d, 0x1c, 0x18, 0x14, 0x0f, 0x0a, 0x05, 0x02},	/* 21, -5.5dB */
	{0x1f, 0x1e, 0x1a, 0x15, 0x10, 0x0a, 0x05, 0x02},	/* 22, -5.0dB */
	{0x20, 0x20, 0x1b, 0x16, 0x11, 0x08, 0x05, 0x02},	/* 23, -4.5dB */
	{0x22, 0x21, 0x1d, 0x18, 0x11, 0x0b, 0x06, 0x02},	/* 24, -4.0dB */
	{0x24, 0x23, 0x1f, 0x19, 0x13, 0x0c, 0x06, 0x03},	/* 25, -3.5dB */
	{0x26, 0x25, 0x21, 0x1b, 0x14, 0x0d, 0x06, 0x03},	/* 26, -3.0dB */
	{0x28, 0x28, 0x22, 0x1c, 0x15, 0x0d, 0x07, 0x03},	/* 27, -2.5dB */
	{0x2b, 0x2a, 0x25, 0x1e, 0x16, 0x0e, 0x07, 0x03},	/* 28, -2.0dB */
	{0x2d, 0x2d, 0x27, 0x1f, 0x18, 0x0f, 0x08, 0x03},	/* 29, -1.5dB */
	{0x30, 0x2f, 0x29, 0x21, 0x19, 0x10, 0x08, 0x03},	/* 30, -1.0dB */
	{0x33, 0x32, 0x2b, 0x23, 0x1a, 0x11, 0x08, 0x04},	/* 31, -0.5dB */
	{0x36, 0x35, 0x2e, 0x25, 0x1c, 0x12, 0x09, 0x04}	/* 32, +0dB */
};


u8 cck_swing_table_ch14[CCK_TABLE_SIZE][8] = {
	{0x09, 0x08, 0x07, 0x04, 0x00, 0x00, 0x00, 0x00},	/* 0, -16.0dB */
	{0x09, 0x09, 0x08, 0x05, 0x00, 0x00, 0x00, 0x00},	/* 1, -15.5dB */
	{0x0a, 0x09, 0x08, 0x05, 0x00, 0x00, 0x00, 0x00},	/* 2, -15.0dB */
	{0x0a, 0x0a, 0x09, 0x05, 0x00, 0x00, 0x00, 0x00},	/* 3, -14.5dB */
	{0x0b, 0x0a, 0x09, 0x05, 0x00, 0x00, 0x00, 0x00},	/* 4, -14.0dB */
	{0x0b, 0x0b, 0x0a, 0x06, 0x00, 0x00, 0x00, 0x00},	/* 5, -13.5dB */
	{0x0c, 0x0c, 0x0a, 0x06, 0x00, 0x00, 0x00, 0x00},	/* 6, -13.0dB */
	{0x0d, 0x0c, 0x0b, 0x06, 0x00, 0x00, 0x00, 0x00},	/* 7, -12.5dB */
	{0x0d, 0x0d, 0x0c, 0x07, 0x00, 0x00, 0x00, 0x00},	/* 8, -12.0dB */
	{0x0e, 0x0e, 0x0c, 0x07, 0x00, 0x00, 0x00, 0x00},	/* 9, -11.5dB */
	{0x0f, 0x0f, 0x0d, 0x08, 0x00, 0x00, 0x00, 0x00},	/* 10, -11.0dB */
	{0x10, 0x10, 0x0e, 0x08, 0x00, 0x00, 0x00, 0x00},	/* 11, -10.5dB */
	{0x11, 0x11, 0x0f, 0x09, 0x00, 0x00, 0x00, 0x00},	/* 12, -10.0dB */
	{0x12, 0x12, 0x0f, 0x09, 0x00, 0x00, 0x00, 0x00},	/* 13, -9.5dB */
	{0x13, 0x13, 0x10, 0x0a, 0x00, 0x00, 0x00, 0x00},	/* 14, -9.0dB */
	{0x14, 0x14, 0x11, 0x0a, 0x00, 0x00, 0x00, 0x00},	/* 15, -8.5dB */
	{0x16, 0x15, 0x12, 0x0b, 0x00, 0x00, 0x00, 0x00},	/* 16, -8.0dB */
	{0x17, 0x16, 0x13, 0x0b, 0x00, 0x00, 0x00, 0x00},	/* 17, -7.5dB */
	{0x18, 0x17, 0x15, 0x0c, 0x00, 0x00, 0x00, 0x00},	/* 18, -7.0dB */
	{0x1a, 0x19, 0x16, 0x0d, 0x00, 0x00, 0x00, 0x00},	/* 19, -6.5dB */
	{0x1c, 0x1a, 0x18, 0x0e, 0x00, 0x00, 0x00, 0x00},	/* 20, -6.0dB */
	{0x1d, 0x1c, 0x18, 0x0e, 0x00, 0x00, 0x00, 0x00},	/* 21, -5.5dB */
	{0x1f, 0x1e, 0x1a, 0x0f, 0x00, 0x00, 0x00, 0x00},	/* 22, -5.0dB */
	{0x20, 0x20, 0x1b, 0x10, 0x00, 0x00, 0x00, 0x00},	/* 23, -4.5dB */
	{0x22, 0x21, 0x1d, 0x11, 0x00, 0x00, 0x00, 0x00},	/* 24, -4.0dB */
	{0x24, 0x23, 0x1f, 0x12, 0x00, 0x00, 0x00, 0x00},	/* 25, -3.5dB */
	{0x26, 0x25, 0x21, 0x13, 0x00, 0x00, 0x00, 0x00},	/* 26, -3.0dB */
	{0x28, 0x28, 0x24, 0x14, 0x00, 0x00, 0x00, 0x00},	/* 27, -2.5dB */
	{0x2b, 0x2a, 0x25, 0x15, 0x00, 0x00, 0x00, 0x00},	/* 28, -2.0dB */
	{0x2d, 0x2d, 0x17, 0x17, 0x00, 0x00, 0x00, 0x00},	/* 29, -1.5dB */
	{0x30, 0x2f, 0x29, 0x18, 0x00, 0x00, 0x00, 0x00},	/* 30, -1.0dB */
	{0x33, 0x32, 0x2b, 0x19, 0x00, 0x00, 0x00, 0x00},	/* 31, -0.5dB */
	{0x36, 0x35, 0x2e, 0x1b, 0x00, 0x00, 0x00, 0x00}	/* 32, +0dB */
};

u8 cck_swing_table_ch1_ch14_88f[CCK_TABLE_SIZE_88F][16] = {
	{0x16, 0x15, 0x13, 0x10, 0xD, 0x9, 0x6, 0x3, 0x2, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0},        /* 0  -16dB */
	{0x18, 0x17, 0x15, 0x12, 0xE, 0xA, 0x7, 0x4, 0x2, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0},        /* 1  -15.5dB */
	{0x1B, 0x1A, 0x18, 0x14, 0x10, 0xB, 0x7, 0x4, 0x2, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0},        /* 2  -15dB */
	{0x1F, 0x1E, 0x1B, 0x17, 0x12, 0xD, 0x8, 0x5, 0x2, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0},        /* 3  -14.5dB */
	{0x22, 0x21, 0x1E, 0x19, 0x14, 0xE, 0x9, 0x5, 0x3, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0},        /* 4  -14dB */
	{0x26, 0x25, 0x22, 0x1C, 0x16, 0x10, 0xA, 0x6, 0x3, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0},        /* 5  -13.5dB */
	{0x2B, 0x2A, 0x26, 0x20, 0x19, 0x12, 0xC, 0x7, 0x3, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0},        /* 6  -13dB */
	{0x30, 0x2F, 0x2A, 0x24, 0x1C, 0x14, 0xD, 0x8, 0x4, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0},        /* 7  -12.5dB */
	{0x36, 0x34, 0x2F, 0x28, 0x1F, 0x17, 0xF, 0x9, 0x4, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0},        /* 8  -12dB */
	{0x3D, 0x3B, 0x35, 0x2D, 0x23, 0x19, 0x11, 0xA, 0x5, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0},        /* 9  -11.5dB */
	{0x44, 0x42, 0x3C, 0x33, 0x28, 0x1C, 0x13, 0xB, 0x5, 0x2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0},        /* 10  -11dB */
	{0x4D, 0x4A, 0x43, 0x39, 0x2C, 0x20, 0x15, 0xC, 0x6, 0x2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0},        /* 11  -10.5dB */
	{0x56, 0x53, 0x4B, 0x40, 0x32, 0x24, 0x17, 0xE, 0x6, 0x2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0},        /* 12  -10dB */
	{0x60, 0x5D, 0x54, 0x47, 0x38, 0x28, 0x1A, 0xF, 0x7, 0x2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0},        /* 13  -9.5dB */
	{0x6C, 0x69, 0x5F, 0x50, 0x3F, 0x2D, 0x1E, 0x11, 0x8, 0x3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0},        /* 14  -9dB */
	{0x79, 0x76, 0x6A, 0x5A, 0x46, 0x33, 0x21, 0x13, 0x9, 0x3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0},        /* 15  -8.5dB */
	{0x88, 0x84, 0x77, 0x65, 0x4F, 0x39, 0x25, 0x15, 0xA, 0x3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0},        /* 16  -8dB */
	{0x99, 0x94, 0x86, 0x71, 0x58, 0x40, 0x2A, 0x18, 0xB, 0x4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0},        /* 17  -7.5dB */
	{0xAC, 0xA6, 0x96, 0x7F, 0x63, 0x47, 0x2F, 0x1B, 0xD, 0x4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0},        /* 18  -7dB */
	{0xC1, 0xBA, 0xA8, 0x8F, 0x6F, 0x50, 0x35, 0x1E, 0xE, 0x4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0},        /* 19  -6.5dB */
	{0xD8, 0xD1, 0xBD, 0xA0, 0x7D, 0x5A, 0x3B, 0x22, 0x10, 0x5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0}      /* 20  -6dB */
};


u8 cck_swing_table_ch1_ch13_88f[CCK_TABLE_SIZE_88F][16] = {
	{0x16, 0x15, 0x13, 0x10, 0xD, 0x9, 0x6, 0x3, 0x2, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0},        /* 0  -16dB */
	{0x18, 0x17, 0x15, 0x12, 0xE, 0xA, 0x7, 0x4, 0x2, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0},        /* 1  -15.5dB */
	{0x1B, 0x1A, 0x18, 0x14, 0x10, 0xB, 0x7, 0x4, 0x2, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0},        /* 2  -15dB */
	{0x1F, 0x1E, 0x1B, 0x17, 0x12, 0xD, 0x8, 0x5, 0x2, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0},        /* 3  -14.5dB */
	{0x22, 0x21, 0x1E, 0x19, 0x14, 0xE, 0x9, 0x5, 0x3, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0},        /* 4  -14dB */
	{0x26, 0x25, 0x22, 0x1C, 0x16, 0x10, 0xA, 0x6, 0x3, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0},        /* 5  -13.5dB */
	{0x2B, 0x2A, 0x26, 0x20, 0x19, 0x12, 0xC, 0x7, 0x3, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0},        /* 6  -13dB */
	{0x30, 0x2F, 0x2A, 0x24, 0x1C, 0x14, 0xD, 0x8, 0x4, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0},        /* 7  -12.5dB */
	{0x36, 0x34, 0x2F, 0x28, 0x1F, 0x17, 0xF, 0x9, 0x4, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0},        /* 8  -12dB */
	{0x3D, 0x3B, 0x35, 0x2D, 0x23, 0x19, 0x11, 0xA, 0x5, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0},        /* 9  -11.5dB */
	{0x44, 0x42, 0x3C, 0x33, 0x28, 0x1C, 0x13, 0xB, 0x5, 0x2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0},        /* 10  -11dB */
	{0x4D, 0x4A, 0x43, 0x39, 0x2C, 0x20, 0x15, 0xC, 0x6, 0x2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0},        /* 11  -10.5dB */
	{0x56, 0x53, 0x4B, 0x40, 0x32, 0x24, 0x17, 0xE, 0x6, 0x2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0},        /* 12  -10dB */
	{0x60, 0x5D, 0x54, 0x47, 0x38, 0x28, 0x1A, 0xF, 0x7, 0x2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0},        /* 13  -9.5dB */
	{0x6C, 0x69, 0x5F, 0x50, 0x3F, 0x2D, 0x1E, 0x11, 0x8, 0x3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0},        /* 14  -9dB */
	{0x79, 0x76, 0x6A, 0x5A, 0x46, 0x33, 0x21, 0x13, 0x9, 0x3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0},        /* 15  -8.5dB */
	{0x88, 0x84, 0x77, 0x65, 0x4F, 0x39, 0x25, 0x15, 0xA, 0x3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0},        /* 16  -8dB */
	{0x99, 0x94, 0x86, 0x71, 0x58, 0x40, 0x2A, 0x18, 0xB, 0x4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0},        /* 17  -7.5dB */
	{0xAC, 0xA6, 0x96, 0x7F, 0x63, 0x47, 0x2F, 0x1B, 0xD, 0x4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0},        /* 18  -7dB */
	{0xC1, 0xBA, 0xA8, 0x8F, 0x6F, 0x50, 0x35, 0x1E, 0xE, 0x4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0},        /* 19  -6.5dB */
	{0xD8, 0xD1, 0xBD, 0xA0, 0x7D, 0x5A, 0x3B, 0x22, 0x10, 0x5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0}      /* 20  -6dB */
};


u8 cck_swing_table_ch14_88f[CCK_TABLE_SIZE_88F][16] = {
	{0x44,	 0x42, 0x3C, 0x28, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},    /*-16dB*/
	{0x48, 0x46, 0x3F, 0x2A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},    /*-15.5dB*/
	{0x4D, 0x4A, 0x43, 0x2C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},    /*-15dB*/
	{0x51, 0x4F, 0x47, 0x2F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},	    /*-14.5dB*/
	{0x56, 0x53, 0x4B, 0x32, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},    /*-14dB*/
	{0x5B, 0x58, 0x50, 0x35, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},    /*-13.5dB*/
	{0x60, 0x5D, 0x54, 0x38, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},    /*-13dB*/
	{0x66, 0x63, 0x59, 0x3B, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},    /*-12.5dB*/
	{0x6C, 0x69, 0x5F, 0x3F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},    /*-12dB*/
	{0x73, 0x6F, 0x64, 0x42, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},    /*-11.5dB*/
	{0x79, 0x76, 0x6A, 0x46, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},    /*-11dB*/
	{0x81, 0x7C, 0x71, 0x4A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},    /*-10.5dB*/
	{0x88, 0x84, 0x77, 0x4F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},    /*-10dB*/
	{0x90, 0x8C, 0x7E, 0x54, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},    /*-9.5dB*/
	{0x99, 0x94, 0x86, 0x58, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},    /*-9dB*/
	{0xA2, 0x9D, 0x8E, 0x5E, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},    /*-8.5dB*/
	{0xAC, 0xA6, 0x96, 0x63, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},    /*-8dB*/
	{0xB6, 0xB0, 0x9F, 0x69, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},    /*-7.5dB*/
	{0xC1, 0xBA, 0xA8, 0x6F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},    /*-7dB*/
	{0xCC, 0xC5, 0xB2, 0x76, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},    /*-6.5dB*/
	{0xD8, 0xD1, 0xBD, 0x7D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}     /*-6dB*/
};

/* Winnita ADD 20171113 PathA 0xAB4[10:0],PathB 0xAB4[21:11]*/
u32 cck_swing_table_ch1_ch14_8192f[CCK_TABLE_SIZE_8192F] = {
	0x0CD,			 /*0 ,    -20dB*/
	0x0D9,
	0x0E6,
	0x0F3,
	0x102,
	0x111,
	0x121,
	0x132,
	0x144,
	0x158,
	0x16C,
	0x182,
	0x198,
	0x1B1,
	0x1CA,
	0x1E5,
	0x202,
	0x221,
	0x241,
	0x263,		/*19*/
	0x287,		/*20*/
	0x2AE,		/*21*/
	0x2D6,		/*22*/
	0x301,		/*23*/
	0x32F,		/*24*/
	0x35F,		/*25*/
	0x392,		/*26*/
	0x3C9,		/*27*/
	0x402,		/*28*/
	0x43F,		/*29*/
	0x47F,		/*30*/
	0x4C3,		/*31*/
	0x50C,		/*32*/
	0x558,		/*33*/
	0x5A9,		/*34*/
	0x5FF,		/*35*/
	0x65A,		/*36*/
	0x6BA,
	0x720,
	0x78C,
	0x7FF,
};


#if 0
u32 ofdm_swing_table_92e[OFDM_TABLE_SIZE_92E] = {
	/* Index0   6  dB */ 0x7fc001ff,
	/* Index1   5.7dB */ 0x7b4001ed,
	/* Index2   5.4dB */ 0x774001dd,
	/* Index3   5.1dB */ 0x734001cd,
	/* Index4   4.8dB */ 0x6f4001bd,
	/* Index5   4.5dB */ 0x6b8001ae,
	/* Index6   4.2dB */ 0x67c0019f,
	/* Index7   3.9dB */ 0x64400191,
	/* Index8   3.6dB */ 0x60c00183,
	/* Index9   3.3dB */ 0x5d800176,
	/* Index10  3  dB */ 0x5a80016a,
	/* Index11  2.7dB */ 0x5740015d,
	/* Index12  2.4dB */ 0x54400151,
	/* Index13  2.1dB */ 0x51800146,
	/* Index14  1.8dB */ 0x4ec0013b,
	/* Index15  1.5dB */ 0x4c000130,
	/* Index16  1.2dB */ 0x49800126,
	/* Index17  0.9dB */ 0x4700011c,
	/* Index18  0.6dB */ 0x44800112,
	/* Index19  0.3dB */ 0x42000108,
	/* Index20  0  dB */ 0x40000100, /* 20 This is OFDM base index */
	/* Index21 -0.3dB */ 0x3dc000f7,
	/* Index22 -0.6dB */ 0x3bc000ef,
	/* Index23 -0.9dB */ 0x39c000e7,
	/* Index24 -1.2dB */ 0x37c000df,
	/* Index25 -1.5dB */ 0x35c000d7,
	/* Index26 -1.8dB */ 0x340000d0,
	/* Index27 -2.1dB */ 0x324000c9,
	/* Index28 -2.4dB */ 0x308000c2,
	/* Index29 -2.7dB */ 0x2f0000bc,
	/* Index30 -3  dB */ 0x2d4000b5,
	/* Index31 -3.3dB */ 0x2bc000af,
	/* Index32 -3.6dB */ 0x2a4000a9,
	/* Index33 -3.9dB */ 0x28c000a3,
	/* Index34 -4.2dB */ 0x2780009e,
	/* Index35 -4.5dB */ 0x26000098,
	/* Index36 -4.8dB */ 0x24c00093,
	/* Index37 -5.1dB */ 0x2380008e,
	/* Index38 -5.4dB */ 0x22400089,
	/* Index39 -5.7dB */ 0x21400085,
	/* Index40 -6  dB */ 0x20000080,
	/* Index41 -6.3dB */ 0x1f00007c,
	/* Index42 -6.6dB */ 0x1e000078,
	/* Index43 -6.9dB */ 0x1d000074,
	/* Index44 -7.2dB */ 0x1c000070,
	/* Index45 -7.5dB */ 0x1b00006c,
	/* Index46 -7.8dB */ 0x1a000068,
	/* Index47 -8.1dB */ 0x19400065,
	/* Index48 -8.4dB */ 0x18400061,
	/* Index49 -8.7dB */ 0x1780005e,
	/* Index50 -9  dB */ 0x16c0005b,
	/* Index51 -9.3dB */ 0x16000058,
	/* Index52 -9.6dB */ 0x15400055,
	/* Index53 -9.9dB */ 0x14800052
};
u8 cck_swing_table_ch1_ch13_92e[CCK_TABLE_SIZE_92E][8] = {
	/* Index0    0  dB */    {0x36, 0x34, 0x2E, 0x26, 0x1C, 0x12, 0x08, 0x04},
	/* Index1   -0.3dB */    {0x34, 0x32, 0x2C, 0x25, 0x1B, 0x11, 0x08, 0x04},
	/* Index2   -0.6dB */    {0x32, 0x30, 0x2B, 0x23, 0x1A, 0x11, 0x07, 0x04},
	/* Index3   -0.9dB */    {0x31, 0x2F, 0x29, 0x22, 0x19, 0x10, 0x07, 0x04},
	/* Index4   -1.2dB */    {0x2F, 0x2D, 0x28, 0x21, 0x18, 0x10, 0x07, 0x03},
	/* Index5   -1.5dB */    {0x2D, 0x2C, 0x27, 0x20, 0x18, 0x0F, 0x07, 0x03},
	/* Index6   -1.8dB */    {0x2C, 0x2A, 0x25, 0x1F, 0x17, 0x0F, 0x06, 0x03},
	/* Index7   -2.1dB */    {0x2A, 0x29, 0x24, 0x1E, 0x16, 0x0E, 0x06, 0x03},
	/* Index8   -2.4dB */    {0x29, 0x27, 0x23, 0x1D, 0x15, 0x0E, 0x06, 0x03},
	/* Index9   -2.7dB */    {0x27, 0x26, 0x22, 0x1C, 0x14, 0x0D, 0x06, 0x03},
	/* Index10  -3  dB */    {0x26, 0x25, 0x20, 0x1B, 0x14, 0x0D, 0x06, 0x03},
	/* Index11  -3.3dB */    {0x25, 0x23, 0x1F, 0x1A, 0x13, 0x0C, 0x05, 0x03},
	/* Index12  -3.6dB */    {0x24, 0x22, 0x1E, 0x19, 0x12, 0x0C, 0x05, 0x03},
	/* Index13  -3.9dB */    {0x22, 0x21, 0x1D, 0x18, 0x12, 0x0B, 0x05, 0x03},
	/* Index14  -4.2dB */    {0x21, 0x20, 0x1C, 0x17, 0x11, 0x0B, 0x05, 0x02},
	/* Index15  -4.5dB */    {0x20, 0x1F, 0x1B, 0x17, 0x11, 0x0B, 0x05, 0x02},
	/* Index16  -4.8dB */    {0x1F, 0x1E, 0x1A, 0x16, 0x10, 0x0A, 0x05, 0x02},
	/* Index17  -5.1dB */    {0x1E, 0x1D, 0x1A, 0x15, 0x10, 0x0A, 0x04, 0x02},
	/* Index18  -5.4dB */    {0x1D, 0x1C, 0x19, 0x14, 0x0F, 0x0A, 0x04, 0x02},
	/* Index19  -5.7dB */    {0x1C, 0x1B, 0x18, 0x14, 0x0E, 0x09, 0x04, 0x02},
	/* Index20  -6.0dB */    {0x1B, 0x1A, 0x17, 0x13, 0x0E, 0x09, 0x04, 0x02}, /* 20 This is CCK base index */
	/* Index21  -6.3dB */    {0x1A, 0x19, 0x16, 0x12, 0x0E, 0x09, 0x04, 0x02},
	/* Index22  -6.6dB */    {0x19, 0x18, 0x15, 0x12, 0x0D, 0x08, 0x04, 0x02},
	/* Index23  -6.9dB */    {0x18, 0x17, 0x15, 0x11, 0x0D, 0x08, 0x04, 0x02},
	/* Index24  -7.2dB */    {0x18, 0x17, 0x14, 0x11, 0x0C, 0x08, 0x03, 0x02},
	/* Index25  -7.5dB */    {0x17, 0x16, 0x13, 0x10, 0x0C, 0x08, 0x03, 0x02},
	/* Index26  -7.8dB */    {0x16, 0x15, 0x13, 0x0F, 0x0B, 0x07, 0x03, 0x02},
	/* Index27  -8.1dB */    {0x15, 0x14, 0x12, 0x0F, 0x0B, 0x07, 0x03, 0x02},
	/* Index28  -8.4dB */    {0x14, 0x14, 0x11, 0x0E, 0x0B, 0x07, 0x03, 0x02},
	/* Index29  -8.7dB */    {0x14, 0x13, 0x11, 0x0E, 0x0A, 0x07, 0x03, 0x01},
	/* Index30  -9.0dB */    {0x13, 0x12, 0x10, 0x0D, 0x0A, 0x06, 0x03, 0x01}, /* 30 This is hp CCK base index */
	/* Index31  -9.3dB */    {0x12, 0x12, 0x0F, 0x0D, 0x0A, 0x06, 0x03, 0x01},
	/* Index32  -9.6dB */    {0x12, 0x11, 0x0F, 0x0D, 0x09, 0x06, 0x03, 0x01},
	/* Index33  -9.9dB */    {0x11, 0x11, 0x0F, 0x0C, 0x09, 0x06, 0x03, 0x01},
	/* Index34 -10.2dB */    {0x11, 0x11, 0x0E, 0x0C, 0x09, 0x06, 0x02, 0x01},
	/* Index35 -10.5dB */    {0x10, 0x0F, 0x0E, 0x0B, 0x08, 0x05, 0x02, 0x01},
	/* Index36 -10.8dB */    {0x10, 0x0F, 0x0D, 0x0B, 0x08, 0x05, 0x02, 0x01},
	/* Index37 -11.1dB */    {0x0F, 0x0E, 0x0D, 0x0A, 0x08, 0x05, 0x02, 0x01},
	/* Index38 -11.4dB */    {0x0E, 0x0E, 0x0C, 0x0A, 0x07, 0x05, 0x02, 0x01},
	/* Index39 -11.7dB */    {0x0E, 0x0D, 0x0C, 0x0A, 0x07, 0x05, 0x02, 0x01},
	/* Index40 -12  dB */    {0x0E, 0x0D, 0x0C, 0x0A, 0x07, 0x05, 0x02, 0x01},
	/* Index41 -12.3dB */    {0x0D, 0x0D, 0x0B, 0x09, 0x07, 0x04, 0x02, 0x01},
	/* Index42 -12.6dB */    {0x0D, 0x0C, 0x0B, 0x09, 0x07, 0x04, 0x02, 0x01},
	/* Index43 -12.9dB */    {0x0C, 0x0C, 0x0A, 0x09, 0x06, 0x04, 0x02, 0x01},
	/* Index44 -13.2dB */    {0x0C, 0x0B, 0x0A, 0x08, 0x06, 0x04, 0x02, 0x01},
	/* Index45 -13.5dB */    {0x0B, 0x0B, 0x0A, 0x08, 0x06, 0x04, 0x02, 0x01},
	/* Index46 -13.8dB */    {0x0B, 0x0B, 0x09, 0x08, 0x06, 0x04, 0x02, 0x01},
	/* Index47 -14.1dB */    {0x0B, 0x0A, 0x09, 0x07, 0x06, 0x04, 0x02, 0x01},
	/* Index48 -14.4dB */    {0x0A, 0x0A, 0x09, 0x07, 0x05, 0x03, 0x02, 0x01},
	/* Index49 -14.7dB */    {0x0A, 0x0A, 0x08, 0x07, 0x05, 0x03, 0x01, 0x01},
	/* Index50 -15  dB */    {0x0A, 0x09, 0x08, 0x07, 0x05, 0x03, 0x01, 0x01},
	/* Index51 -15.3dB */    {0x09, 0x09, 0x08, 0x06, 0x05, 0x03, 0x01, 0x01},
	/* Index52 -15.6dB */    {0x09, 0x09, 0x08, 0x06, 0x05, 0x03, 0x01, 0x01},
	/* Index53 -15.9dB */    {0x09, 0x08, 0x07, 0x06, 0x04, 0x03, 0x01, 0x01}
};
u8 cck_swing_table_ch14_92e[CCK_TABLE_SIZE_92E][8] = {
	/* Index0    0  dB */    {0x36, 0x34, 0x2E, 0x26, 0x00, 0x00, 0x00, 0x00},
	/* Index1   -0.3dB */    {0x34, 0x32, 0x2C, 0x25, 0x00, 0x00, 0x00, 0x00},
	/* Index2   -0.6dB */    {0x32, 0x30, 0x2B, 0x23, 0x00, 0x00, 0x00, 0x00},
	/* Index3   -0.9dB */    {0x31, 0x2F, 0x29, 0x22, 0x00, 0x00, 0x00, 0x00},
	/* Index4   -1.2dB */    {0x2F, 0x2D, 0x28, 0x21, 0x00, 0x00, 0x00, 0x00},
	/* Index5   -1.5dB */    {0x2D, 0x2C, 0x27, 0x20, 0x00, 0x00, 0x00, 0x00},
	/* Index6   -1.8dB */    {0x2C, 0x2A, 0x25, 0x1F, 0x00, 0x00, 0x00, 0x00},
	/* Index7   -2.1dB */    {0x2A, 0x29, 0x24, 0x1E, 0x00, 0x00, 0x00, 0x00},
	/* Index8   -2.4dB */    {0x29, 0x27, 0x23, 0x1D, 0x00, 0x00, 0x00, 0x00},
	/* Index9   -2.7dB */    {0x27, 0x26, 0x22, 0x1C, 0x00, 0x00, 0x00, 0x00},
	/* Index10  -3  dB */    {0x26, 0x25, 0x20, 0x1B, 0x00, 0x00, 0x00, 0x00},
	/* Index11  -3.3dB */    {0x25, 0x23, 0x1F, 0x1A, 0x00, 0x00, 0x00, 0x00},
	/* Index12  -3.6dB */    {0x24, 0x22, 0x1E, 0x19, 0x00, 0x00, 0x00, 0x00},
	/* Index13  -3.9dB */    {0x22, 0x21, 0x1D, 0x18, 0x00, 0x00, 0x00, 0x00},
	/* Index14  -4.2dB */    {0x21, 0x20, 0x1C, 0x17, 0x00, 0x00, 0x00, 0x00},
	/* Index15  -4.5dB */    {0x20, 0x1F, 0x1B, 0x17, 0x00, 0x00, 0x00, 0x00},
	/* Index16  -4.8dB */    {0x1F, 0x1E, 0x1A, 0x16, 0x00, 0x00, 0x00, 0x00},
	/* Index17  -5.1dB */    {0x1E, 0x1D, 0x1A, 0x15, 0x00, 0x00, 0x00, 0x00},
	/* Index18  -5.4dB */    {0x1D, 0x1C, 0x19, 0x14, 0x00, 0x00, 0x00, 0x00},
	/* Index19  -5.7dB */    {0x1C, 0x1B, 0x18, 0x14, 0x00, 0x00, 0x00, 0x00},
	/* Index20  -6  dB */     {0x1B, 0x1A, 0x17, 0x13, 0x00, 0x00, 0x00, 0x00},
	/* Index21  -6.3dB */    {0x1A, 0x19, 0x16, 0x12, 0x00, 0x00, 0x00, 0x00},
	/* Index22  -6.6dB */    {0x19, 0x18, 0x15, 0x12, 0x00, 0x00, 0x00, 0x00},
	/* Index23  -6.9dB */    {0x18, 0x17, 0x15, 0x11, 0x00, 0x00, 0x00, 0x00},
	/* Index24  -7.2dB */    {0x18, 0x17, 0x14, 0x11, 0x00, 0x00, 0x00, 0x00},
	/* Index25  -7.5dB */    {0x17, 0x16, 0x13, 0x10, 0x00, 0x00, 0x00, 0x00},
	/* Index26  -7.8dB */    {0x16, 0x15, 0x13, 0x0F, 0x00, 0x00, 0x00, 0x00},
	/* Index27  -8.1dB */    {0x15, 0x14, 0x12, 0x0F, 0x00, 0x00, 0x00, 0x00},
	/* Index28  -8.4dB */    {0x14, 0x14, 0x11, 0x0E, 0x00, 0x00, 0x00, 0x00},
	/* Index29  -8.7dB */    {0x14, 0x13, 0x11, 0x0E, 0x00, 0x00, 0x00, 0x00},
	/* Index30  -9  dB */    {0x13, 0x12, 0x10, 0x0D, 0x00, 0x00, 0x00, 0x00},
	/* Index31  -9.3dB */    {0x12, 0x12, 0x0F, 0x0D, 0x00, 0x00, 0x00, 0x00},
	/* Index32  -9.6dB */    {0x12, 0x11, 0x0F, 0x0D, 0x00, 0x00, 0x00, 0x00},
	/* Index33  -9.9dB */    {0x11, 0x11, 0x0F, 0x0C, 0x00, 0x00, 0x00, 0x00},
	/* Index34 -10.2dB */    {0x11, 0x11, 0x0E, 0x0C, 0x00, 0x00, 0x00, 0x00},
	/* Index35 -10.5dB */    {0x10, 0x0F, 0x0E, 0x0B, 0x00, 0x00, 0x00, 0x00},
	/* Index36 -10.8dB */    {0x10, 0x0F, 0x0D, 0x0B, 0x00, 0x00, 0x00, 0x00},
	/* Index37 -11.1dB */    {0x0F, 0x0E, 0x0D, 0x0A, 0x00, 0x00, 0x00, 0x00},
	/* Index38 -11.4dB */    {0x0E, 0x0E, 0x0C, 0x0A, 0x00, 0x00, 0x00, 0x00},
	/* Index39 -11.7dB */    {0x0E, 0x0D, 0x0C, 0x0A, 0x00, 0x00, 0x00, 0x00},
	/* Index40 -12  dB */    {0x0E, 0x0D, 0x0C, 0x0A, 0x00, 0x00, 0x00, 0x00},
	/* Index41 -12.3dB */    {0x0D, 0x0D, 0x0B, 0x09, 0x00, 0x00, 0x00, 0x00},
	/* Index42 -12.6dB */    {0x0D, 0x0C, 0x0B, 0x09, 0x00, 0x00, 0x00, 0x00},
	/* Index43 -12.9dB */    {0x0C, 0x0C, 0x0A, 0x09, 0x00, 0x00, 0x00, 0x00},
	/* Index44 -13.2dB */    {0x0C, 0x0B, 0x0A, 0x08, 0x00, 0x00, 0x00, 0x00},
	/* Index45 -13.5dB */    {0x0B, 0x0B, 0x0A, 0x08, 0x00, 0x00, 0x00, 0x00},
	/* Index46 -13.8dB */    {0x0B, 0x0B, 0x09, 0x08, 0x00, 0x00, 0x00, 0x00},
	/* Index47 -14.1dB */    {0x0B, 0x0A, 0x09, 0x07, 0x00, 0x00, 0x00, 0x00},
	/* Index48 -14.4dB */    {0x0A, 0x0A, 0x09, 0x07, 0x00, 0x00, 0x00, 0x00},
	/* Index49 -14.7dB */    {0x0A, 0x0A, 0x08, 0x07, 0x00, 0x00, 0x00, 0x00},
	/* Index50 -15  dB */    {0x0A, 0x09, 0x08, 0x07, 0x00, 0x00, 0x00, 0x00},
	/* Index51 -15.3dB */    {0x09, 0x09, 0x08, 0x06, 0x00, 0x00, 0x00, 0x00},
	/* Index52 -15.6dB */    {0x09, 0x09, 0x08, 0x06, 0x00, 0x00, 0x00, 0x00},
	/* Index53 -15.9dB */    {0x09, 0x08, 0x07, 0x06, 0x00, 0x00, 0x00, 0x00}
};
#endif
#endif


u8 delta_swing_table_idx_2ga_p_default[DELTA_SWINGIDX_SIZE] = {0, 0, 0, 0, 1, 1, 2, 2, 3, 3
	, 4, 4, 4,  4,  4,  4,  4,  4,  5,  5,  7,  7,  8,  8,  8,  9,  9,  9,  9,  9
							      };
u8 delta_swing_table_idx_2ga_n_default[DELTA_SWINGIDX_SIZE] = {0, 0, 0, 2, 2, 3, 3, 4, 4, 4
	, 4, 5, 5,  6,  6,  7,  7,  7,  7,  8,  8,  9,  9, 10, 10, 10, 11, 11, 11, 11
							      };


#ifdef CONFIG_WLAN_HAL_8192EE
u32 ofdm_swing_table_92e[OFDM_TABLE_SIZE_92E] = {
	/* Index0   6  dB */ 0x7fc001ff,
	/* Index1   5.7dB */ 0x7b4001ed,
	/* Index2   5.4dB */ 0x774001dd,
	/* Index3   5.1dB */ 0x734001cd,
	/* Index4   4.8dB */ 0x6f4001bd,
	/* Index5   4.5dB */ 0x6b8001ae,
	/* Index6   4.2dB */ 0x67c0019f,
	/* Index7   3.9dB */ 0x64400191,
	/* Index8   3.6dB */ 0x60c00183,
	/* Index9   3.3dB */ 0x5d800176,
	/* Index10  3  dB */ 0x5a80016a,
	/* Index11  2.7dB */ 0x5740015d,
	/* Index12  2.4dB */ 0x54400151,
	/* Index13  2.1dB */ 0x51800146,
	/* Index14  1.8dB */ 0x4ec0013b,
	/* Index15  1.5dB */ 0x4c000130,
	/* Index16  1.2dB */ 0x49800126,
	/* Index17  0.9dB */ 0x4700011c,
	/* Index18  0.6dB */ 0x44800112,
	/* Index19  0.3dB */ 0x42000108,
	/* Index20  0  dB */ 0x40000100, /* 20 This is OFDM base index */
	/* Index21 -0.3dB */ 0x3dc000f7,
	/* Index22 -0.6dB */ 0x3bc000ef,
	/* Index23 -0.9dB */ 0x39c000e7,
	/* Index24 -1.2dB */ 0x37c000df,
	/* Index25 -1.5dB */ 0x35c000d7,
	/* Index26 -1.8dB */ 0x340000d0,
	/* Index27 -2.1dB */ 0x324000c9,
	/* Index28 -2.4dB */ 0x308000c2,
	/* Index29 -2.7dB */ 0x2f0000bc,
	/* Index30 -3  dB */ 0x2d4000b5,
	/* Index31 -3.3dB */ 0x2bc000af,
	/* Index32 -3.6dB */ 0x2a4000a9,
	/* Index33 -3.9dB */ 0x28c000a3,
	/* Index34 -4.2dB */ 0x2780009e,
	/* Index35 -4.5dB */ 0x26000098,
	/* Index36 -4.8dB */ 0x24c00093,
	/* Index37 -5.1dB */ 0x2380008e,
	/* Index38 -5.4dB */ 0x22400089,
	/* Index39 -5.7dB */ 0x21400085,
	/* Index40 -6  dB */ 0x20000080,
	/* Index41 -6.3dB */ 0x1f00007c,
	/* Index42 -6.6dB */ 0x1e000078,
	/* Index43 -6.9dB */ 0x1d000074,
	/* Index44 -7.2dB */ 0x1c000070,
	/* Index45 -7.5dB */ 0x1b00006c,
	/* Index46 -7.8dB */ 0x1a000068,
	/* Index47 -8.1dB */ 0x19400065,
	/* Index48 -8.4dB */ 0x18400061,
	/* Index49 -8.7dB */ 0x1780005e,
	/* Index50 -9  dB */ 0x16c0005b,
	/* Index51 -9.3dB */ 0x16000058,
	/* Index52 -9.6dB */ 0x15400055,
	/* Index53 -9.9dB */ 0x14800052
};
u8 cck_swing_table_ch1_ch13_92e[CCK_TABLE_SIZE_92E][8] = {
	/* Index0    0  dB */    {0x36, 0x34, 0x2E, 0x26, 0x1C, 0x12, 0x08, 0x04},
	/* Index1   -0.3dB */    {0x34, 0x32, 0x2C, 0x25, 0x1B, 0x11, 0x08, 0x04},
	/* Index2   -0.6dB */    {0x32, 0x30, 0x2B, 0x23, 0x1A, 0x11, 0x07, 0x04},
	/* Index3   -0.9dB */    {0x31, 0x2F, 0x29, 0x22, 0x19, 0x10, 0x07, 0x04},
	/* Index4   -1.2dB */    {0x2F, 0x2D, 0x28, 0x21, 0x18, 0x10, 0x07, 0x03},
	/* Index5   -1.5dB */    {0x2D, 0x2C, 0x27, 0x20, 0x18, 0x0F, 0x07, 0x03},
	/* Index6   -1.8dB */    {0x2C, 0x2A, 0x25, 0x1F, 0x17, 0x0F, 0x06, 0x03},
	/* Index7   -2.1dB */    {0x2A, 0x29, 0x24, 0x1E, 0x16, 0x0E, 0x06, 0x03},
	/* Index8   -2.4dB */    {0x29, 0x27, 0x23, 0x1D, 0x15, 0x0E, 0x06, 0x03},
	/* Index9   -2.7dB */    {0x27, 0x26, 0x22, 0x1C, 0x14, 0x0D, 0x06, 0x03},
	/* Index10  -3  dB */    {0x26, 0x25, 0x20, 0x1B, 0x14, 0x0D, 0x06, 0x03},
	/* Index11  -3.3dB */    {0x25, 0x23, 0x1F, 0x1A, 0x13, 0x0C, 0x05, 0x03},
	/* Index12  -3.6dB */    {0x24, 0x22, 0x1E, 0x19, 0x12, 0x0C, 0x05, 0x03},
	/* Index13  -3.9dB */    {0x22, 0x21, 0x1D, 0x18, 0x12, 0x0B, 0x05, 0x03},
	/* Index14  -4.2dB */    {0x21, 0x20, 0x1C, 0x17, 0x11, 0x0B, 0x05, 0x02},
	/* Index15  -4.5dB */    {0x20, 0x1F, 0x1B, 0x17, 0x11, 0x0B, 0x05, 0x02},
	/* Index16  -4.8dB */    {0x1F, 0x1E, 0x1A, 0x16, 0x10, 0x0A, 0x05, 0x02},
	/* Index17  -5.1dB */    {0x1E, 0x1D, 0x1A, 0x15, 0x10, 0x0A, 0x04, 0x02},
	/* Index18  -5.4dB */    {0x1D, 0x1C, 0x19, 0x14, 0x0F, 0x0A, 0x04, 0x02},
	/* Index19  -5.7dB */    {0x1C, 0x1B, 0x18, 0x14, 0x0E, 0x09, 0x04, 0x02},
	/* Index20  -6.0dB */    {0x1B, 0x1A, 0x17, 0x13, 0x0E, 0x09, 0x04, 0x02}, /* 20 This is CCK base index */
	/* Index21  -6.3dB */    {0x1A, 0x19, 0x16, 0x12, 0x0E, 0x09, 0x04, 0x02},
	/* Index22  -6.6dB */    {0x19, 0x18, 0x15, 0x12, 0x0D, 0x08, 0x04, 0x02},
	/* Index23  -6.9dB */    {0x18, 0x17, 0x15, 0x11, 0x0D, 0x08, 0x04, 0x02},
	/* Index24  -7.2dB */    {0x18, 0x17, 0x14, 0x11, 0x0C, 0x08, 0x03, 0x02},
	/* Index25  -7.5dB */    {0x17, 0x16, 0x13, 0x10, 0x0C, 0x08, 0x03, 0x02},
	/* Index26  -7.8dB */    {0x16, 0x15, 0x13, 0x0F, 0x0B, 0x07, 0x03, 0x02},
	/* Index27  -8.1dB */    {0x15, 0x14, 0x12, 0x0F, 0x0B, 0x07, 0x03, 0x02},
	/* Index28  -8.4dB */    {0x14, 0x14, 0x11, 0x0E, 0x0B, 0x07, 0x03, 0x02},
	/* Index29  -8.7dB */    {0x14, 0x13, 0x11, 0x0E, 0x0A, 0x07, 0x03, 0x01},
	/* Index30  -9.0dB */    {0x13, 0x12, 0x10, 0x0D, 0x0A, 0x06, 0x03, 0x01}, /* 30 This is hp CCK base index */
	/* Index31  -9.3dB */    {0x12, 0x12, 0x0F, 0x0D, 0x0A, 0x06, 0x03, 0x01},
	/* Index32  -9.6dB */    {0x12, 0x11, 0x0F, 0x0D, 0x09, 0x06, 0x03, 0x01},
	/* Index33  -9.9dB */    {0x11, 0x11, 0x0F, 0x0C, 0x09, 0x06, 0x03, 0x01},
	/* Index34 -10.2dB */    {0x11, 0x11, 0x0E, 0x0C, 0x09, 0x06, 0x02, 0x01},
	/* Index35 -10.5dB */    {0x10, 0x0F, 0x0E, 0x0B, 0x08, 0x05, 0x02, 0x01},
	/* Index36 -10.8dB */    {0x10, 0x0F, 0x0D, 0x0B, 0x08, 0x05, 0x02, 0x01},
	/* Index37 -11.1dB */    {0x0F, 0x0E, 0x0D, 0x0A, 0x08, 0x05, 0x02, 0x01},
	/* Index38 -11.4dB */    {0x0E, 0x0E, 0x0C, 0x0A, 0x07, 0x05, 0x02, 0x01},
	/* Index39 -11.7dB */    {0x0E, 0x0D, 0x0C, 0x0A, 0x07, 0x05, 0x02, 0x01},
	/* Index40 -12  dB */    {0x0E, 0x0D, 0x0C, 0x0A, 0x07, 0x05, 0x02, 0x01},
	/* Index41 -12.3dB */    {0x0D, 0x0D, 0x0B, 0x09, 0x07, 0x04, 0x02, 0x01},
	/* Index42 -12.6dB */    {0x0D, 0x0C, 0x0B, 0x09, 0x07, 0x04, 0x02, 0x01},
	/* Index43 -12.9dB */    {0x0C, 0x0C, 0x0A, 0x09, 0x06, 0x04, 0x02, 0x01},
	/* Index44 -13.2dB */    {0x0C, 0x0B, 0x0A, 0x08, 0x06, 0x04, 0x02, 0x01},
	/* Index45 -13.5dB */    {0x0B, 0x0B, 0x0A, 0x08, 0x06, 0x04, 0x02, 0x01},
	/* Index46 -13.8dB */    {0x0B, 0x0B, 0x09, 0x08, 0x06, 0x04, 0x02, 0x01},
	/* Index47 -14.1dB */    {0x0B, 0x0A, 0x09, 0x07, 0x06, 0x04, 0x02, 0x01},
	/* Index48 -14.4dB */    {0x0A, 0x0A, 0x09, 0x07, 0x05, 0x03, 0x02, 0x01},
	/* Index49 -14.7dB */    {0x0A, 0x0A, 0x08, 0x07, 0x05, 0x03, 0x01, 0x01},
	/* Index50 -15  dB */    {0x0A, 0x09, 0x08, 0x07, 0x05, 0x03, 0x01, 0x01},
	/* Index51 -15.3dB */    {0x09, 0x09, 0x08, 0x06, 0x05, 0x03, 0x01, 0x01},
	/* Index52 -15.6dB */    {0x09, 0x09, 0x08, 0x06, 0x05, 0x03, 0x01, 0x01},
	/* Index53 -15.9dB */    {0x09, 0x08, 0x07, 0x06, 0x04, 0x03, 0x01, 0x01}
};
u8 cck_swing_table_ch14_92e[CCK_TABLE_SIZE_92E][8] = {
	/* Index0    0  dB */    {0x36, 0x34, 0x2E, 0x26, 0x00, 0x00, 0x00, 0x00},
	/* Index1   -0.3dB */    {0x34, 0x32, 0x2C, 0x25, 0x00, 0x00, 0x00, 0x00},
	/* Index2   -0.6dB */    {0x32, 0x30, 0x2B, 0x23, 0x00, 0x00, 0x00, 0x00},
	/* Index3   -0.9dB */    {0x31, 0x2F, 0x29, 0x22, 0x00, 0x00, 0x00, 0x00},
	/* Index4   -1.2dB */    {0x2F, 0x2D, 0x28, 0x21, 0x00, 0x00, 0x00, 0x00},
	/* Index5   -1.5dB */    {0x2D, 0x2C, 0x27, 0x20, 0x00, 0x00, 0x00, 0x00},
	/* Index6   -1.8dB */    {0x2C, 0x2A, 0x25, 0x1F, 0x00, 0x00, 0x00, 0x00},
	/* Index7   -2.1dB */    {0x2A, 0x29, 0x24, 0x1E, 0x00, 0x00, 0x00, 0x00},
	/* Index8   -2.4dB */    {0x29, 0x27, 0x23, 0x1D, 0x00, 0x00, 0x00, 0x00},
	/* Index9   -2.7dB */    {0x27, 0x26, 0x22, 0x1C, 0x00, 0x00, 0x00, 0x00},
	/* Index10  -3  dB */    {0x26, 0x25, 0x20, 0x1B, 0x00, 0x00, 0x00, 0x00},
	/* Index11  -3.3dB */    {0x25, 0x23, 0x1F, 0x1A, 0x00, 0x00, 0x00, 0x00},
	/* Index12  -3.6dB */    {0x24, 0x22, 0x1E, 0x19, 0x00, 0x00, 0x00, 0x00},
	/* Index13  -3.9dB */    {0x22, 0x21, 0x1D, 0x18, 0x00, 0x00, 0x00, 0x00},
	/* Index14  -4.2dB */    {0x21, 0x20, 0x1C, 0x17, 0x00, 0x00, 0x00, 0x00},
	/* Index15  -4.5dB */    {0x20, 0x1F, 0x1B, 0x17, 0x00, 0x00, 0x00, 0x00},
	/* Index16  -4.8dB */    {0x1F, 0x1E, 0x1A, 0x16, 0x00, 0x00, 0x00, 0x00},
	/* Index17  -5.1dB */    {0x1E, 0x1D, 0x1A, 0x15, 0x00, 0x00, 0x00, 0x00},
	/* Index18  -5.4dB */    {0x1D, 0x1C, 0x19, 0x14, 0x00, 0x00, 0x00, 0x00},
	/* Index19  -5.7dB */    {0x1C, 0x1B, 0x18, 0x14, 0x00, 0x00, 0x00, 0x00},
	/* Index20  -6  dB */     {0x1B, 0x1A, 0x17, 0x13, 0x00, 0x00, 0x00, 0x00},
	/* Index21  -6.3dB */    {0x1A, 0x19, 0x16, 0x12, 0x00, 0x00, 0x00, 0x00},
	/* Index22  -6.6dB */    {0x19, 0x18, 0x15, 0x12, 0x00, 0x00, 0x00, 0x00},
	/* Index23  -6.9dB */    {0x18, 0x17, 0x15, 0x11, 0x00, 0x00, 0x00, 0x00},
	/* Index24  -7.2dB */    {0x18, 0x17, 0x14, 0x11, 0x00, 0x00, 0x00, 0x00},
	/* Index25  -7.5dB */    {0x17, 0x16, 0x13, 0x10, 0x00, 0x00, 0x00, 0x00},
	/* Index26  -7.8dB */    {0x16, 0x15, 0x13, 0x0F, 0x00, 0x00, 0x00, 0x00},
	/* Index27  -8.1dB */    {0x15, 0x14, 0x12, 0x0F, 0x00, 0x00, 0x00, 0x00},
	/* Index28  -8.4dB */    {0x14, 0x14, 0x11, 0x0E, 0x00, 0x00, 0x00, 0x00},
	/* Index29  -8.7dB */    {0x14, 0x13, 0x11, 0x0E, 0x00, 0x00, 0x00, 0x00},
	/* Index30  -9  dB */    {0x13, 0x12, 0x10, 0x0D, 0x00, 0x00, 0x00, 0x00},
	/* Index31  -9.3dB */    {0x12, 0x12, 0x0F, 0x0D, 0x00, 0x00, 0x00, 0x00},
	/* Index32  -9.6dB */    {0x12, 0x11, 0x0F, 0x0D, 0x00, 0x00, 0x00, 0x00},
	/* Index33  -9.9dB */    {0x11, 0x11, 0x0F, 0x0C, 0x00, 0x00, 0x00, 0x00},
	/* Index34 -10.2dB */    {0x11, 0x11, 0x0E, 0x0C, 0x00, 0x00, 0x00, 0x00},
	/* Index35 -10.5dB */    {0x10, 0x0F, 0x0E, 0x0B, 0x00, 0x00, 0x00, 0x00},
	/* Index36 -10.8dB */    {0x10, 0x0F, 0x0D, 0x0B, 0x00, 0x00, 0x00, 0x00},
	/* Index37 -11.1dB */    {0x0F, 0x0E, 0x0D, 0x0A, 0x00, 0x00, 0x00, 0x00},
	/* Index38 -11.4dB */    {0x0E, 0x0E, 0x0C, 0x0A, 0x00, 0x00, 0x00, 0x00},
	/* Index39 -11.7dB */    {0x0E, 0x0D, 0x0C, 0x0A, 0x00, 0x00, 0x00, 0x00},
	/* Index40 -12  dB */    {0x0E, 0x0D, 0x0C, 0x0A, 0x00, 0x00, 0x00, 0x00},
	/* Index41 -12.3dB */    {0x0D, 0x0D, 0x0B, 0x09, 0x00, 0x00, 0x00, 0x00},
	/* Index42 -12.6dB */    {0x0D, 0x0C, 0x0B, 0x09, 0x00, 0x00, 0x00, 0x00},
	/* Index43 -12.9dB */    {0x0C, 0x0C, 0x0A, 0x09, 0x00, 0x00, 0x00, 0x00},
	/* Index44 -13.2dB */    {0x0C, 0x0B, 0x0A, 0x08, 0x00, 0x00, 0x00, 0x00},
	/* Index45 -13.5dB */    {0x0B, 0x0B, 0x0A, 0x08, 0x00, 0x00, 0x00, 0x00},
	/* Index46 -13.8dB */    {0x0B, 0x0B, 0x09, 0x08, 0x00, 0x00, 0x00, 0x00},
	/* Index47 -14.1dB */    {0x0B, 0x0A, 0x09, 0x07, 0x00, 0x00, 0x00, 0x00},
	/* Index48 -14.4dB */    {0x0A, 0x0A, 0x09, 0x07, 0x00, 0x00, 0x00, 0x00},
	/* Index49 -14.7dB */    {0x0A, 0x0A, 0x08, 0x07, 0x00, 0x00, 0x00, 0x00},
	/* Index50 -15  dB */    {0x0A, 0x09, 0x08, 0x07, 0x00, 0x00, 0x00, 0x00},
	/* Index51 -15.3dB */    {0x09, 0x09, 0x08, 0x06, 0x00, 0x00, 0x00, 0x00},
	/* Index52 -15.6dB */    {0x09, 0x09, 0x08, 0x06, 0x00, 0x00, 0x00, 0x00},
	/* Index53 -15.9dB */    {0x09, 0x08, 0x07, 0x06, 0x00, 0x00, 0x00, 0x00}
};
#endif

#if (RTL8814A_SUPPORT == 1 || RTL8822B_SUPPORT == 1 ||\
	RTL8821C_SUPPORT == 1 || RTL8198F_SUPPORT == 1 ||\
	RTL8814B_SUPPORT == 1)
u32 tx_scaling_table_jaguar[TXSCALE_TABLE_SIZE] = {
	0x081, /* 0,  -12.0dB */
	0x088, /* 1,  -11.5dB */
	0x090, /* 2,  -11.0dB */
	0x099, /* 3,  -10.5dB */
	0x0A2, /* 4,  -10.0dB */
	0x0AC, /* 5,  -9.5dB */
	0x0B6, /* 6,  -9.0dB */
	0x0C0, /* 7,  -8.5dB */
	0x0CC, /* 8,  -8.0dB */
	0x0D8, /* 9,  -7.5dB */
	0x0E5, /* 10, -7.0dB */
	0x0F2, /* 11, -6.5dB */
	0x101, /* 12, -6.0dB */
	0x110, /* 13, -5.5dB */
	0x120, /* 14, -5.0dB */
	0x131, /* 15, -4.5dB */
	0x143, /* 16, -4.0dB */
	0x156, /* 17, -3.5dB */
	0x16A, /* 18, -3.0dB */
	0x180, /* 19, -2.5dB */
	0x197, /* 20, -2.0dB */
	0x1AF, /* 21, -1.5dB */
	0x1C8, /* 22, -1.0dB */
	0x1E3, /* 23, -0.5dB */
	0x200, /* 24, +0  dB */
	0x21E, /* 25, +0.5dB */
	0x23E, /* 26, +1.0dB */
	0x261, /* 27, +1.5dB */
	0x285, /* 28, +2.0dB */
	0x2AB, /* 29, +2.5dB */
	0x2D3, /* 30, +3.0dB */
	0x2FE, /* 31, +3.5dB */
	0x32B, /* 32, +4.0dB */
	0x35C, /* 33, +4.5dB */
	0x38E, /* 34, +5.0dB */
	0x3C4, /* 35, +5.5dB */
	0x3FE  /* 36, +6.0dB */
};
#elif(ODM_IC_11AC_SERIES_SUPPORT)
u32 ofdm_swing_table_8812[OFDM_TABLE_SIZE_8812] = {
	0x3FE, /* 0,  (6dB) */
	0x3C4, /* 1,  (5.5dB) */
	0x38E, /* 2,  (5dB) */
	0x35C, /* 3,  (4.5dB) */
	0x32B, /* 4,  (4dB) */
	0x2FE, /* 5,  (3.5dB) */
	0x2D3, /* 6,  (3dB) */
	0x2AB, /* 7,  (2.5dB) */
	0x285, /* 8,  (2dB) */
	0x261, /* 9,  (1.5dB */
	0x23E, /* 10, (1dB) */
	0x21E, /* 11, (0.5dB) */
	0x200, /* 12, (0dB)		8814 int PA 2G default */
	0x1E3, /* 13, (-0.5dB) */
	0x1C8, /* 14, (-1dB) */
	0x1AF, /* 15, (-1.5dB) */
	0x197, /* 16, (-2dB) */
	0x180, /* 17, (-2.5dB) */
	0x16A, /* 18, (-3dB)		8812 / 8814 int PA 5G / 8814 ext PA 2G5G default */
	0x156, /* 19, (-3.5dB) */
	0x143, /* 20, (-4dB)		8812 HP default */
	0x131, /* 21, (-4.5dB) */
	0x120, /* 22, (-5dB) */
	0x110, /* 23, (-5.5dB) */
	0x101, /* 24, (-6dB) */
	0x0F2, /* 25, (-6.5dB) */
	0x0E5, /* 26, (-7dB) */
	0x0D8, /* 27, (-7.5dB) */
	0x0CC, /* 28, (-8dB) */
	0x0C0, /* 29, (-8.5dB) */
	0x0B6, /* 30, (-9dB) */
	0x0AC, /* 31, (-9.5dB) */
	0x0A2, /* 32, (-10dB) */
	0x099, /* 33, (-10.5dB) */
	0x090, /* 34, (-11dB) */
	0x088, /* 35, (-11.5dB) */
	0x081, /* 36, (-12dB) */
	0x079, /* 37, (-12.5dB) */
	0x072, /* 38, (-13dB) */
	0x06c, /* 39, (-13.5dB) */
	0x066, /* 40, (-14dB) */
	0x060, /* 41, (-14.5dB) */
	0x05B  /* 42, (-15dB) */
};
#endif

u32 cck_swing_table_ch1_ch14_8723d[CCK_TABLE_SIZE_8723D] = {
	0x0CD,
	0x0D9,
	0x0E6,
	0x0F3,
	0x102,
	0x111,
	0x121,
	0x132,
	0x144,
	0x158,
	0x16C,
	0x182,
	0x198,
	0x1B1,
	0x1CA,
	0x1E5,
	0x202,
	0x221,
	0x241,
	0x263,
	0x287,
	0x2AE,
	0x2D6,
	0x301,
	0x32F,
	0x35F,
	0x392,
	0x3C9,
	0x402,
	0x43F,
	0x47F,
	0x4C3,
	0x50C,
	0x558,
	0x5A9,
	0x5FF,
	0x65A,
	0x6BA,
	0x720,
	0x78C,
	0x7FF,
};
/* JJ ADD 20161014 */
u32 cck_swing_table_ch1_ch14_8710b[CCK_TABLE_SIZE_8710B] = {
	0x0CD,
	0x0D9,
	0x0E6,
	0x0F3,
	0x102,
	0x111,
	0x121,
	0x132,
	0x144,
	0x158,
	0x16C,
	0x182,
	0x198,
	0x1B1,
	0x1CA,
	0x1E5,
	0x202,
	0x221,
	0x241,
	0x263,
	0x287,
	0x2AE,
	0x2D6,
	0x301,
	0x32F,
	0x35F,
	0x392,
	0x3C9,
	0x402,
	0x43F,
	0x47F,
	0x4C3,
	0x50C,
	0x558,
	0x5A9,
	0x5FF,
	0x65A,
	0x6BA,
	0x720,
	0x78C,
	0x7FF,
};


/* #endif */
/* 3============================================================
 * 3 Tx Power Tracking
 * 3============================================================ */

void
odm_txpowertracking_init(
	void		*dm_void
)
{
	struct dm_struct		*dm = (struct dm_struct *)dm_void;
#if (DM_ODM_SUPPORT_TYPE & (ODM_AP))
	if (!(dm->support_ic_type & (ODM_RTL8814A | ODM_RTL8822B | ODM_RTL8814B | ODM_IC_11N_SERIES)))
		return;
#endif

	odm_txpowertracking_thermal_meter_init(dm);
}


u8
get_swing_index(
	void		*dm_void
)
{
	struct dm_struct		*dm = (struct dm_struct *)dm_void;
	u8			i = 0, bb_swing_mask = 0;
	u32			bb_swing = 0;
	u32			swing_table_size = 0;
	u32			*swing_table = 0;
	struct rtl8192cd_priv	*priv = dm->priv;

#if (RTL8197F_SUPPORT == 1)
	if (GET_CHIP_VER(priv) == VERSION_8197F) {
		bb_swing = phy_query_bb_reg(priv, REG_OFDM_0_XA_TX_IQ_IMBALANCE, MASKOFDM_D);
		swing_table = ofdm_swing_table_new;
		swing_table_size = OFDM_TABLE_SIZE_92D;
		bb_swing_mask = 22;
	}
#endif

#if (RTL8192F_SUPPORT == 1)
	if (GET_CHIP_VER(priv) == VERSION_8192F) {
		bb_swing = phy_query_bb_reg(priv, REG_OFDM_0_XA_TX_IQ_IMBALANCE, MASKOFDM_D);
		swing_table = ofdm_swing_table_new;
		swing_table_size = OFDM_TABLE_SIZE_92D;
		bb_swing_mask = 22;
	}
#endif

#if (RTL8822B_SUPPORT == 1)
	if (GET_CHIP_VER(priv) == VERSION_8822B) {
		bb_swing = phy_query_bb_reg(priv, REG_A_TX_SCALE_JAGUAR, 0xFFE00000);
		swing_table = tx_scaling_table_jaguar;
		swing_table_size = TXSCALE_TABLE_SIZE;
		bb_swing_mask = 0;
	}
#endif

	for (i = 0; i < swing_table_size - 1; i++) {
		u32 table_value = swing_table[i] >> bb_swing_mask;

		if (bb_swing == table_value)
			break;
	}

	RF_DBG(dm, DBG_RF_TX_PWR_TRACK, "bb_swing=0x%x bbswing_index=%d\n", bb_swing, i);


	return i;
}

s8
get_txagc_default_index(
	void *dm_void
)
{
	struct dm_struct *dm = (struct dm_struct *)dm_void;
	s8 tmp;

#if RTL8814B_SUPPORT
	if (dm->support_ic_type == ODM_RTL8814B) {
		tmp = (s8)(odm_get_bb_reg(dm, R_0x18a0, 0x7f) & 0xff);
		if (tmp & BIT(6))
			tmp = tmp | 0x80;
		return tmp;
	} else
		return 0;
#endif
}

void
odm_txpowertracking_thermal_meter_init(
	void		*dm_void
)
{
	struct dm_struct		*dm = (struct dm_struct *)dm_void;
	struct dm_rf_calibration_struct	*cali_info = &(dm->rf_calibrate_info);
	struct rtl8192cd_priv		*priv = dm->priv;
	u8 p;
	u8 default_swing_index;
#if (RTL8197F_SUPPORT == 1 || RTL8822B_SUPPORT == 1 || RTL8192F_SUPPORT == 1)
	if ((GET_CHIP_VER(priv) == VERSION_8197F) || (GET_CHIP_VER(priv) == VERSION_8822B) ||(GET_CHIP_VER(priv) == VERSION_8192F))
		default_swing_index = get_swing_index(dm);
#endif

#if (DM_ODM_SUPPORT_TYPE == ODM_WIN)
	void		*adapter = dm->adapter;
	PMGNT_INFO	mgnt_info = &adapter->MgntInfo;
	HAL_DATA_TYPE		*hal_data = GET_HAL_DATA(((PADAPTER)adapter));

	mgnt_info->is_txpowertracking = true;
	hal_data->tx_powercount       = 0;
	hal_data->is_txpowertracking_init = false;

	if (*(dm->mp_mode) == false)
		hal_data->txpowertrack_control = true;
	RF_DBG(dm, COMP_POWER_TRACKING, "mgnt_info->is_txpowertracking = %d\n", mgnt_info->is_txpowertracking);
#elif (DM_ODM_SUPPORT_TYPE == ODM_CE)
#ifdef CONFIG_RTL8188E
	{
		dm->rf_calibrate_info.is_txpowertracking = true;
		dm->rf_calibrate_info.tx_powercount = 0;
		dm->rf_calibrate_info.is_txpowertracking_init = false;

		if (*(dm->mp_mode) == false)
			dm->rf_calibrate_info.txpowertrack_control = true;

		MSG_8192C("dm txpowertrack_control = %d\n", dm->rf_calibrate_info.txpowertrack_control);
	}
#else
	{
		void		*adapter = dm->adapter;
		HAL_DATA_TYPE	*hal_data = GET_HAL_DATA(((PADAPTER)adapter));
		struct dm_priv	*pdmpriv = &hal_data->dmpriv;
		
		pdmpriv->is_txpowertracking = true;
		pdmpriv->tx_powercount = 0;
		pdmpriv->is_txpowertracking_init = false;

		if (*(dm->mp_mode) == false)		/* for mp driver, turn off txpwrtracking as default */
			pdmpriv->txpowertrack_control = true;

		MSG_8192C("pdmpriv->txpowertrack_control = %d\n", pdmpriv->txpowertrack_control);

	}
#endif/* endif (CONFIG_RTL8188E==1) */
#elif (DM_ODM_SUPPORT_TYPE & (ODM_AP))

#ifdef RTL8188E_SUPPORT
	{
		dm->rf_calibrate_info.is_txpowertracking = true;
		dm->rf_calibrate_info.tx_powercount = 0;
		dm->rf_calibrate_info.is_txpowertracking_init = false;
		dm->rf_calibrate_info.txpowertrack_control = true;
		dm->rf_calibrate_info.tm_trigger = 0;
	}
#endif
#endif

	dm->rf_calibrate_info.txpowertrack_control = true;
	dm->rf_calibrate_info.delta_power_index = 0;
	dm->rf_calibrate_info.delta_power_index_last = 0;
	dm->rf_calibrate_info.power_index_offset = 0;
	dm->rf_calibrate_info.thermal_value = 0;
	cali_info->default_ofdm_index = 28;

#if (RTL8197F_SUPPORT == 1)
	if (GET_CHIP_VER(priv) == VERSION_8197F) {
		cali_info->default_ofdm_index = (default_swing_index >= (OFDM_TABLE_SIZE_92D - 1)) ? 30 : default_swing_index;
		cali_info->default_cck_index = 28;
	}
#endif

#if (RTL8192F_SUPPORT == 1)
	if (GET_CHIP_VER(priv) == VERSION_8192F) {
		cali_info->default_ofdm_index = (default_swing_index >= (OFDM_TABLE_SIZE_92D - 1)) ? 30 : default_swing_index;
		cali_info->default_cck_index = 28;
	}
#endif

#if (RTL8822B_SUPPORT == 1)
	if (GET_CHIP_VER(priv) == VERSION_8822B) {
		cali_info->default_ofdm_index = (default_swing_index >= (TXSCALE_TABLE_SIZE - 1)) ? 24 : default_swing_index;
		cali_info->default_cck_index = 20;
	}
#endif


#if RTL8188E_SUPPORT
	if (GET_CHIP_VER(priv) == VERSION_8188E) {
		cali_info->default_cck_index = 20;	/* -6 dB */
	}
#endif

#if RTL8192E_SUPPORT
	if (GET_CHIP_VER(priv) == VERSION_8192E) {
		cali_info->default_cck_index = 8;	/* -12 dB */
	}
#endif

#if RTL8814B_SUPPORT
	if (GET_CHIP_VER(priv) == VERSION_8814B) {
		cali_info->default_txagc_index = get_txagc_default_index(dm);
	}
#endif

	cali_info->bb_swing_idx_ofdm_base = cali_info->default_ofdm_index;
	cali_info->bb_swing_idx_cck_base = cali_info->default_cck_index;
	dm->rf_calibrate_info.CCK_index = cali_info->default_cck_index;

	for (p = 0; p < MAX_RF_PATH; p++) {
		dm->rf_calibrate_info.OFDM_index[p] = cali_info->default_ofdm_index;
		cali_info->bb_swing_idx_ofdm[p] = cali_info->default_ofdm_index;
		cali_info->kfree_offset[p] = 0;	/* for 8814 kfree*/
	}
	cali_info->bb_swing_idx_cck = cali_info->default_cck_index;

	RF_DBG(dm, DBG_RF_TX_PWR_TRACK, "cali_info->default_ofdm_index=%d cali_info->default_cck_index=%d\n", cali_info->default_ofdm_index, cali_info->default_cck_index);

	cali_info->tm_trigger = 0;
}


void
odm_txpowertracking_check(
	void		*dm_void
)
{
	/*  */
	/* For AP/ADSL use struct rtl8192cd_priv* */
	/* For CE/NIC use struct void* */
	/*  */
	struct dm_struct		*dm = (struct dm_struct *)dm_void;
	struct _hal_rf_				*rf = &(dm->rf_table);

	if (!(rf->rf_supportability & HAL_RF_TX_PWR_TRACK))
		return;

	/*  */
	/* 2011/09/29 MH In HW integration first stage, we provide 4 different handle to operate */
	/* at the same time. In the stage2/3, we need to prive universal interface and merge all */
	/* HW dynamic mechanism. */
	/*  */
	switch	(dm->support_platform) {
	case	ODM_WIN:
		odm_txpowertracking_check_mp(dm);
		break;

	case	ODM_CE:
		odm_txpowertracking_check_ce(dm);
		break;

	case	ODM_AP:
		odm_txpowertracking_check_ap(dm);
		break;
	}

}

void
odm_txpowertracking_check_ce(
	void		*dm_void
)
{
#if (DM_ODM_SUPPORT_TYPE == ODM_CE)
	struct dm_struct		*dm = (struct dm_struct *)dm_void;
	void	*adapter = dm->adapter;
	struct _hal_rf_				*rf = &(dm->rf_table);

#if (RTL8188E_SUPPORT == 1)

	/* if(!mgnt_info->is_txpowertracking || (!pdmpriv->txpowertrack_control && pdmpriv->is_ap_kdone)) */

	if (!(rf->rf_supportability & HAL_RF_TX_PWR_TRACK))
		return;

	if (!dm->rf_calibrate_info.tm_trigger) {	/* at least delay 1 sec */
		/* hal_data->TxPowerCheckCnt++;	 */ /* cosa add for debug */
		odm_set_rf_reg(dm, RF_PATH_A, RF_T_METER, RFREGOFFSETMASK, 0x60);
		/* DBG_8192C("Trigger 92C Thermal Meter!!\n"); */

		dm->rf_calibrate_info.tm_trigger = 1;
		return;

	} else {
		/* DBG_8192C("Schedule TxPowerTracking direct call!!\n"); */
		odm_txpowertracking_callback_thermal_meter_8188e(adapter);
		dm->rf_calibrate_info.tm_trigger = 0;
	}
#endif

#endif
}

void
odm_txpowertracking_check_mp(
	void		*dm_void
)
{
#if (DM_ODM_SUPPORT_TYPE == ODM_WIN)
	struct dm_struct		*dm = (struct dm_struct *)dm_void;
	void	*adapter = dm->adapter;

	if (odm_check_power_status(adapter) == false)
		return;

	if (!adapter->is_slave_of_dmsp || adapter->dual_mac_smart_concurrent == false)
		odm_txpowertracking_thermal_meter_check(adapter);
#endif

}


void
odm_txpowertracking_check_ap(
	void		*dm_void
)
{
	struct dm_struct *dm = (struct dm_struct *)dm_void;
	struct _hal_rf_ *rf = &dm->rf_table;
	struct _halrf_tssi_data *tssi = &rf->halrf_tssi_data;

#if ((RTL8188E_SUPPORT == 1) || (RTL8192E_SUPPORT == 1) || (RTL8812A_SUPPORT == 1) || (RTL8881A_SUPPORT == 1) || (RTL8814A_SUPPORT == 1) || (RTL8197F_SUPPORT == 1) || (RTL8192F_SUPPORT == 1) || (RTL8198F_SUPPORT == 1) || (RTL8814B_SUPPORT == 1) || (RTL8812F_SUPPORT == 1) || (RTL8197G_SUPPORT == 1))
	if (!dm->rf_calibrate_info.tm_trigger) {
		if (dm->support_ic_type & (ODM_RTL8188E | ODM_RTL8192E | ODM_RTL8812 | ODM_RTL8881A | ODM_RTL8814A | ODM_RTL8197F | ODM_RTL8822B | ODM_RTL8821C | ODM_RTL8192F | ODM_RTL8198F)) {
			odm_set_rf_reg(dm, RF_PATH_A, 0x42, (BIT(17) | BIT(16)), 0x3);
		} else if (dm->support_ic_type & ODM_RTL8812F) {
			odm_set_rf_reg(dm, RF_PATH_A, R_0x42, BIT(19), 0x01);
			odm_set_rf_reg(dm, RF_PATH_A, R_0x42, BIT(19), 0x00);
			odm_set_rf_reg(dm, RF_PATH_A, R_0x42, BIT(19), 0x01);
			
			odm_set_rf_reg(dm, RF_PATH_B, R_0x42, BIT(19), 0x01);
			odm_set_rf_reg(dm, RF_PATH_B, R_0x42, BIT(19), 0x00);
			odm_set_rf_reg(dm, RF_PATH_B, R_0x42, BIT(19), 0x01);
		} else if (dm->support_ic_type & ODM_RTL8814B) {
			odm_set_rf_reg(dm, RF_PATH_A, 0x42, BIT(17), 0x1);
			odm_set_rf_reg(dm, RF_PATH_B, 0x42, BIT(17), 0x1);
			odm_set_rf_reg(dm, RF_PATH_C, 0x42, BIT(17), 0x1);
			odm_set_rf_reg(dm, RF_PATH_D, 0x42, BIT(17), 0x1);
		} else if (dm->support_ic_type & ODM_RTL8197G) {
			odm_set_rf_reg(dm, RF_PATH_A, RF_0x42, BIT(17), 0x1);
			odm_set_rf_reg(dm, RF_PATH_A, RF_0x42, BIT(17), 0x0);
			odm_set_rf_reg(dm, RF_PATH_A, RF_0x42, BIT(17), 0x1);

			odm_set_rf_reg(dm, RF_PATH_B, RF_0x42, BIT(17), 0x1);
			odm_set_rf_reg(dm, RF_PATH_B, RF_0x42, BIT(17), 0x0);
			odm_set_rf_reg(dm, RF_PATH_B, RF_0x42, BIT(17), 0x1);
		}

		if (dm->support_ic_type & ODM_RTL8814B) {
			ODM_delay_us(300);
			odm_txpowertracking_callback_thermal_meter(dm);
			tssi->thermal_trigger = 1;
		}

		dm->rf_calibrate_info.tm_trigger = 1;
	} else {
		odm_txpowertracking_callback_thermal_meter(dm);
		if (dm->support_ic_type & ODM_RTL8814B)
			tssi->thermal_trigger = 0;
		dm->rf_calibrate_info.tm_trigger = 0;
	}
#endif

}
