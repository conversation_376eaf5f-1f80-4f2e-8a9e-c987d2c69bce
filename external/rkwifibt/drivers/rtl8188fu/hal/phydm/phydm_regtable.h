/******************************************************************************
 *
 * Copyright(c) 2007 - 2019  Realtek Corporation.
 *
 * This program is free software; you can redistribute it and/or modify it
 * under the terms of version 2 of the GNU General Public License as
 * published by the Free Software Foundation.
 *
 * This program is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or
 * FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License for
 * more details.
 *
 * The full GNU General Public License is included in this distribution in the
 * file called LICENSE.
 *
 * Contact Information:
 * wlanfae <<EMAIL>>
 * Realtek Corporation, No. 2, Innovation Road II, Hsinchu Science Park,
 * Hsinchu 300, Taiwan.
 *
 *
 *****************************************************************************/

#define R_0x0 0x0
#define R_0x00 0x00
#define R_0x0106 0x0106
#define R_0x0140 0x0140
#define R_0x0144 0x0144
#define R_0x0148 0x0148
#define R_0x040 0x040
#define R_0x10 0x10
#define R_0x100 0x100
#define R_0x1038 0x1038
#define R_0x103c 0x103c
#define R_0x1040 0x1040
#define R_0x1048 0x1048
#define R_0x1080 0x1080
#define R_0x14 0x14
#define R_0x14c0 0x14c0
#define R_0x14c4 0x14c4
#define R_0x14c8 0x14c8
#define R_0x14cc 0x14cc
#define R_0x1518 0x1518
#define R_0x1684 0x1684
#define R_0x1688 0x1688
#define R_0x168c 0x168c
#define R_0x1700 0x1700
#define R_0x1704 0x1704
#define R_0x1800 0x1800
#define R_0x1804 0x1804
#define R_0x1808 0x1808
#define R_0x180c 0x180c
#define R_0x1810 0x1810
#define R_0x1814 0x1814
#define R_0x1818 0x1818
#define R_0x181c 0x181c
#define R_0x1830 0x1830
#define R_0x1834 0x1834
#define R_0x1838 0x1838
#define R_0x183c 0x183c
#define R_0x1840 0x1840
#define R_0x1844 0x1844
#define R_0x1848 0x1848
#define R_0x1860 0x1860
#define R_0x1864 0x1864
#define R_0x1868 0x1868
#define R_0x186c 0x186c
#define R_0x1870 0x1870
#define R_0x1880 0x1880
#define R_0x1884 0x1884
#define R_0x188c 0x188c
#define R_0x1894 0x1894
#define R_0x189c 0x189c
#define R_0x18a0 0x18a0
#define R_0x18a4 0x18a4
#define R_0x18a8 0x18a8
#define R_0x18ac 0x18ac
#define R_0x18e0 0x18e0
#define R_0x18e8 0x18e8
#define R_0x18ec 0x18ec
#define R_0x18f0 0x18f0
#define R_0x18f8 0x18f8
#define R_0x18fc 0x18fc
#define R_0x1900 0x1900
#define R_0x1904 0x1904
#define R_0x1908 0x1908
#define R_0x1910 0x1910
#define R_0x1918 0x1918
#define R_0x191c 0x191c
#define R_0x1928 0x1928
#define R_0x1938 0x1938
#define R_0x1940 0x1940
#define R_0x1944 0x1944
#define R_0x1950 0x1950
#define R_0x1954 0x1954
#define R_0x195c 0x195c
#define R_0x1970 0x1970
#define R_0x1984 0x1984
#define R_0x1988 0x1988
#define R_0x198c 0x198c
#define R_0x1990 0x1990
#define R_0x1991 0x1991
#define R_0x1998 0x1998
#define R_0x19a8 0x19a8
#define R_0x19b8 0x19b8
#define R_0x19d4 0x19d4
#define R_0x19d8 0x19d8
#define R_0x19e0 0x19e0
#define R_0x19f0 0x19f0
#define R_0x19f8 0x19f8
#define R_0x1a00 0x1a00
#define R_0x1a04 0x1a04
#define R_0x1a08 0x1a08
#define R_0x1a10 0x1a10
#define R_0x1a14 0x1a14
#define R_0x1a20 0x1a20
#define R_0x1a24 0x1a24
#define R_0x1a28 0x1a28
#define R_0x1a2c 0x1a2c
#define R_0x1a5c 0x1a5c
#define R_0x1a70 0x1a70
#define R_0x1a74 0x1a74
#define R_0x1a80 0x1a80
#define R_0x1a84 0x1a84
#define R_0x1a8c 0x1a8c
#define R_0x1a94 0x1a94
#define R_0x1a98 0x1a98
#define R_0x1a9c 0x1a9c
#define R_0x1aa0 0x1aa0
#define R_0x1aa8 0x1aa8
#define R_0x1aac 0x1aac
#define R_0x1ab0 0x1ab0
#define R_0x1abc 0x1abc
#define R_0x1ac0 0x1ac0
#define R_0x1ac8 0x1ac8
#define R_0x1acc 0x1acc
#define R_0x1ad0 0x1ad0
#define R_0x1ad4 0x1ad4
#define R_0x1ae0 0x1ae0
#define R_0x1ae8 0x1ae8
#define R_0x1aec 0x1aec
#define R_0x1b00 0x1b00
#define R_0x1b04 0x1b04
#define R_0x1b08 0x1b08
#define R_0x1b0c 0x1b0c
#define R_0x1b10 0x1b10
#define R_0x1b14 0x1b14
#define R_0x1b18 0x1b18
#define R_0x1b1c 0x1b1c
#define R_0x1b20 0x1b20
#define R_0x1b23 0x1b23
#define R_0x1b24 0x1b24
#define R_0x1b28 0x1b28
#define R_0x1b2c 0x1b2c
#define R_0x1b30 0x1b30
#define R_0x1b34 0x1b34
#define R_0x1b38 0x1b38
#define R_0x1b3c 0x1b3c
#define R_0x1b40 0x1b40
#define R_0x1b44 0x1b44
#define R_0x1b48 0x1b48
#define R_0x1b4c 0x1b4c
#define R_0x1b50 0x1b50
#define R_0x1b54 0x1b54
#define R_0x1b58 0x1b58
#define R_0x1b5c 0x1b5c
#define R_0x1b60 0x1b60
#define R_0x1b64 0x1b64
#define R_0x1b67 0x1b67
#define R_0x1b68 0x1b68
#define R_0x1b6c 0x1b6c
#define R_0x1b70 0x1b70
#define R_0x1b74 0x1b74
#define R_0x1b78 0x1b78
#define R_0x1b7c 0x1b7c
#define R_0x1b80 0x1b80
#define R_0x1b83 0x1b83
#define R_0x1b84 0x1b84
#define R_0x1b88 0x1b88
#define R_0x1b8c 0x1b8c
#define R_0x1b90 0x1b90
#define R_0x1b92 0x1b92
#define R_0x1b94 0x1b94
#define R_0x1b97 0x1b97
#define R_0x1b98 0x1b98
#define R_0x1b9c 0x1b9c
#define R_0x1ba0 0x1ba0
#define R_0x1ba4 0x1ba4
#define R_0x1ba8 0x1ba8
#define R_0x1bac 0x1bac
#define R_0x1bb0 0x1bb0
#define R_0x1bb4 0x1bb4
#define R_0x1bb8 0x1bb8
#define R_0x1bbc 0x1bbc
#define R_0x1bc0 0x1bc0
#define R_0x1bc8 0x1bc8
#define R_0x1bca 0x1bca
#define R_0x1bcb 0x1bcb
#define R_0x1bcc 0x1bcc
#define R_0x1bce 0x1bce
#define R_0x1bd0 0x1bd0
#define R_0x1bd4 0x1bd4
#define R_0x1bd6 0x1bd6
#define R_0x1bd8 0x1bd8
#define R_0x1bdc 0x1bdc
#define R_0x1be4 0x1be4
#define R_0x1be8 0x1be8
#define R_0x1beb 0x1beb
#define R_0x1bec 0x1bec
#define R_0x1bef 0x1bef
#define R_0x1bf0 0x1bf0
#define R_0x1bf4 0x1bf4
#define R_0x1bf8 0x1bf8
#define R_0x1bfc 0x1bfc
#define R_0x1c 0x1c
#define R_0x1c20 0x1c20
#define R_0x1c24 0x1c24
#define R_0x1c28 0x1c28
#define R_0x1c2c 0x1c2c
#define R_0x1c30 0x1c30
#define R_0x1c34 0x1c34
#define R_0x1c38 0x1c38
#define R_0x1c3c 0x1c3c
#define R_0x1c64 0x1c64
#define R_0x1c68 0x1c68
#define R_0x1c6c 0x1c6c
#define R_0x1c74 0x1c74
#define R_0x1c78 0x1c78
#define R_0x1c7c 0x1c7c
#define R_0x1c80 0x1c80
#define R_0x1c90 0x1c90
#define R_0x1c94 0x1c94
#define R_0x1c98 0x1c98
#define R_0x1c9c 0x1c9c
#define R_0x1ca0 0x1ca0
#define R_0x1ca4 0x1ca4
#define R_0x1cb0 0x1cb0
#define R_0x1cb8 0x1cb8
#define R_0x1cc0 0x1cc0
#define R_0x1cd0 0x1cd0
#define R_0x1cd8 0x1cd8
#define R_0x1ce4 0x1ce4
#define R_0x1ce8 0x1ce8
#define R_0x1cec 0x1cec
#define R_0x1cf0 0x1cf0
#define R_0x1cf4 0x1cf4
#define R_0x1cf8 0x1cf8
#define R_0x1d04 0x1d04
#define R_0x1d08 0x1d08
#define R_0x1d0c 0x1d0c
#define R_0x1d10 0x1d10
#define R_0x1d2c 0x1d2c
#define R_0x1d30 0x1d30
#define R_0x1d3c 0x1d3c
#define R_0x1d44 0x1d44
#define R_0x1d48 0x1d48
#define R_0x1d58 0x1d58
#define R_0x1d60 0x1d60
#define R_0x1d6c 0x1d6c
#define R_0x1d70 0x1d70
#define R_0x1d90 0x1d90
#define R_0x1d94 0x1d94
#define R_0x1d9c 0x1d9c
#define R_0x1da4 0x1da4
#define R_0x1da8 0x1da8
#define R_0x1de8 0x1de8
#define R_0x1e14 0x1e14
#define R_0x1e18 0x1e18
#define R_0x1e1c 0x1e1c
#define R_0x1e24 0x1e24
#define R_0x1e28 0x1e28
#define R_0x1e2c 0x1e2c
#define R_0x1e28 0x1e28
#define R_0x1e30 0x1e30
#define R_0x1e40 0x1e40
#define R_0x1e44 0x1e44
#define R_0x1e48 0x1e48
#define R_0x1e5c 0x1e5c
#define R_0x1e60 0x1e60
#define R_0x1e64 0x1e64
#define R_0x1e68 0x1e68
#define R_0x1e6c 0x1e6c
#define R_0x1e70 0x1e70
#define R_0x1e7c 0x1e7c
#define R_0x1e80 0x1e80
#define R_0x1e84 0x1e84
#define R_0x1e88 0x1e88
#define R_0x1e8c 0x1e8c
#define R_0x1ea4 0x1ea4
#define R_0x1eb4 0x1eb4
#define R_0x1eb8 0x1eb8
#define R_0x1ed4 0x1ed4
#define R_0x1ed8 0x1ed8
#define R_0x1edc 0x1edc
#define R_0x1ee0 0x1ee0
#define R_0x1ee4 0x1ee4
#define R_0x1ee8 0x1ee8
#define R_0x1eec 0x1eec
#define R_0x1ef0 0x1ef0
#define R_0x1ef4 0x1ef4
#define R_0x1ef8 0x1ef8
#define R_0x1efc 0x1efc
#define R_0x24 0x24
#define R_0x28 0x28
#define R_0x2c 0x2c
#define R_0x28a4 0x28a4
#define R_0x2c04 0x2c04
#define R_0x2c08 0x2c08
#define R_0x2c0c 0x2c0c
#define R_0x2c10 0x2c10
#define R_0x2c14 0x2c14
#define R_0x2c18 0x2c18
#define R_0x2c1c 0x2c1c
#define R_0x2c20 0x2c20
#define R_0x2c2c 0x2c2c
#define R_0x2c30 0x2c30
#define R_0x2c34 0x2c34
#define R_0x2c54 0x2c54
#define R_0x2d00 0x2d00
#define R_0x2d04 0x2d04
#define R_0x2d08 0x2d08
#define R_0x2d0c 0x2d0c
#define R_0x2d10 0x2d10
#define R_0x2d20 0x2d20
#define R_0x2d38 0x2d38
#define R_0x2d40 0x2d40
#define R_0x2d44 0x2d44
#define R_0x2d48 0x2d48
#define R_0x2d4c 0x2d4c
#define R_0x2d88 0x2d88
#define R_0x2d90 0x2d90
#define R_0x2d9c 0x2d9c
#define R_0x2db4 0x2db4
#define R_0x2db8 0x2db8
#define R_0x2dbc 0x2dbc
#define R_0x2de0 0x2de0
#define R_0x2de4 0x2de4
#define R_0x2de8 0x2de8
#define R_0x2e00 0x2e00
#define R_0x2e20 0x2e20
#define R_0x2e60 0x2e60
#define R_0x2e64 0x2e64
#define R_0x2e68 0x2e68
#define R_0x2e6c 0x2e6c
#define R_0x2e70 0x2e70
#define R_0x2e74 0x2e74
#define R_0x2e78 0x2e78
#define R_0x2e7c 0x2e7c
#define R_0x2e80 0x2e80
#define R_0x300 0x300
#define R_0x38 0x38
#define R_0x3a00 0x3a00
#define R_0x3a04 0x3a04
#define R_0x3a08 0x3a08
#define R_0x3a0c 0x3a0c
#define R_0x3a10 0x3a10
#define R_0x3a14 0x3a14
#define R_0x3a18 0x3a18
#define R_0x3a1c 0x3a1c
#define R_0x3a20 0x3a20
#define R_0x3a24 0x3a24
#define R_0x3a28 0x3a28
#define R_0x3a2c 0x3a2c
#define R_0x3a30 0x3a30
#define R_0x3a34 0x3a34
#define R_0x3a38 0x3a38
#define R_0x3a3c 0x3a3c
#define R_0x3a40 0x3a40
#define R_0x3a44 0x3a44
#define R_0x3a48 0x3a48
#define R_0x3a4c 0x3a4c
#define R_0x3a50 0x3a50               
#define R_0x3a54 0x3a54
#define R_0x3a58 0x3a58
#define R_0x3a5c 0x3a5c
#define R_0x3a60 0x3a60
#define R_0x3a64 0x3a64
#define R_0x3a68 0x3a68
#define R_0x3a6c 0x3a6c
#define R_0x3a70 0x3a70
#define R_0x3a74 0x3a74
#define R_0x3a78 0x3a78
#define R_0x3a7c 0x3a7c
#define R_0x3a80 0x3a80
#define R_0x3a84 0x3a84
#define R_0x3a88 0x3a88
#define R_0x3a8c 0x3a8c
#define R_0x3a90 0x3a90
#define R_0x3a94 0x3a94
#define R_0x3a98 0x3a98
#define R_0x3a9c 0x3a9c
#define R_0x3aa0 0x3aa0
#define R_0x3aa4 0x3aa4
#define R_0x3c00 0x3c00
#define R_0x40 0x40
#define R_0x4000 0x4000
#define R_0x4008 0x4008
#define R_0x4018 0x4018
#define R_0x401c 0x401c
#define R_0x4028 0x4028
#define R_0x4040 0x4040
#define R_0x4044 0x4044
#define R_0x4100 0x4100
#define R_0x4104 0x4104
#define R_0x4108 0x4108
#define R_0x410c 0x410c
#define R_0x4110 0x4110
#define R_0x4114 0x4114
#define R_0x4118 0x4118
#define R_0x411c 0x411c
#define R_0x4130 0x4130
#define R_0x4134 0x4134
#define R_0x4138 0x4138
#define R_0x413c 0x413c
#define R_0x4140 0x4140
#define R_0x4144 0x4144
#define R_0x4148 0x4148
#define R_0x4160 0x4160
#define R_0x4164 0x4164
#define R_0x4168 0x4168
#define R_0x416c 0x416c
#define R_0x4180 0x4180
#define R_0x419c 0x419c
#define R_0x41a0 0x41a0
#define R_0x41a4 0x41a4
#define R_0x41a8 0x41a8
#define R_0x41ac 0x41ac
#define R_0x41e0 0x41e0
#define R_0x41e8 0x41e8
#define R_0x41ec 0x41ec
#define R_0x41f0 0x41f0
#define R_0x41f8 0x41f8
#define R_0x41fc 0x41fc
#define R_0x42 0x42
#define R_0x430 0x430
#define R_0x434 0x434
#define R_0x44 0x44
#define R_0x440 0x440
#define R_0x444 0x444
#define R_0x448 0x448
#define R_0x450 0x450
#define R_0x454 0x454
#define R_0x49c 0x49c
#define R_0x4a0 0x4a0
#define R_0x4a4 0x4a4
#define R_0x4a8 0x4a8
#define R_0x4c 0x4c
#define R_0x4c8 0x4c8
#define R_0x4cc 0x4cc
#define R_0x45a4 0x45a4
#define R_0x4c00 0x4c00
#define R_0x5000 0x5000
#define R_0x5008 0x5008
#define R_0x5018 0x5018
#define R_0x501c 0x501c
#define R_0x5028 0x5028
#define R_0x5040 0x5040
#define R_0x5044 0x5044
#define R_0x5100 0x5100
#define R_0x5108 0x5108
#define R_0x5118 0x5118
#define R_0x511c 0x511c
#define R_0x5128 0x5128
#define R_0x5140 0x5140
#define R_0x5144 0x5144
#define R_0x520 0x520
#define R_0x5200 0x5200
#define R_0x520c 0x520c
#define R_0x522 0x522
#define R_0x524 0x524
#define R_0x5230 0x5230
#define R_0x5234 0x5234
#define R_0x5238 0x5238
#define R_0x523c 0x523c
#define R_0x5240 0x5240
#define R_0x5244 0x5244
#define R_0x5248 0x5248
#define R_0x526c 0x526c
#define R_0x5280 0x5280
#define R_0x52a0 0x52a0
#define R_0x52a4 0x52a4
#define R_0x52ac 0x52ac
#define R_0x52e8 0x52e8
#define R_0x5300 0x5300
#define R_0x530c 0x530c
#define R_0x5330 0x5330
#define R_0x5334 0x5334
#define R_0x5338 0x5338
#define R_0x533c 0x533c
#define R_0x5340 0x5340
#define R_0x5344 0x5344
#define R_0x5348 0x5348
#define R_0x536c 0x536c
#define R_0x5380 0x5380
#define R_0x53a0 0x53a0
#define R_0x53a4 0x53a4
#define R_0x53ac 0x53ac
#define R_0x53e8 0x53e8
#define R_0x550 0x550
#define R_0x551 0x551
#define R_0x568 0x568
#define R_0x588 0x588
#define R_0x60 0x60
#define R_0x604 0x604
#define R_0x608 0x608
#define R_0x60c 0x60c
#define R_0x60f 0x60f
#define R_0x64 0x64
#define R_0x66 0x66
#define R_0x660 0x660
#define R_0x668 0x668
#define R_0x688 0x688
#define R_0x6a0 0x6a0
#define R_0x6d8 0x6d8
#define R_0x6dc 0x6dc
#define R_0x6f8 0x6f8
#define R_0x70 0x70
#define R_0x74 0x74
#define R_0x700 0x700
#define R_0x71c 0x71c
#define R_0x72c 0x72c
#define R_0x764 0x764
#define R_0x7b0 0x7b0
#define R_0x7b4 0x7b4
#define R_0x7c0 0x7c0
#define R_0x7c4 0x7c4
#define R_0x7c8 0x7c8
#define R_0x7cc 0x7cc
#define R_0x7f0 0x7f0
#define R_0x7f4 0x7f4
#define R_0x7f8 0x7f8
#define R_0x7fc 0x7fc
#define R_0x800 0x800
#define R_0x8000 0x8000
#define R_0x804 0x804
#define R_0x808 0x808
#define R_0x80c 0x80c
#define R_0x810 0x810
#define R_0x814 0x814
#define R_0x818 0x818
#define R_0x81c 0x81c
#define R_0x820 0x820
#define R_0x824 0x824
#define R_0x828 0x828
#define R_0x82c 0x82c
#define R_0x830 0x830
#define R_0x834 0x834
#define R_0x838 0x838
#define R_0x83c 0x83c
#define R_0x840 0x840
#define R_0x844 0x840
#define R_0x848 0x848
#define R_0x84c 0x84c
#define R_0x850 0x850
#define R_0x854 0x854
#define R_0x858 0x858
#define R_0x85c 0x85c
#define R_0x860 0x860
#define R_0x864 0x864
#define R_0x868 0x868
#define R_0x86c 0x86c
#define R_0x870 0x870
#define R_0x874 0x874
#define R_0x878 0x878
#define R_0x87c 0x87c
#define R_0x880 0x880
#define R_0x884 0x884
#define R_0x888 0x888
#define R_0x88c 0x88c
#define R_0x890 0x890
#define R_0x894 0x894
#define R_0x898 0x898
#define R_0x89c 0x89c
#define R_0x8a0 0x8a0
#define R_0x8a4 0x8a4
#define R_0x8ac 0x8ac
#define R_0x8b4 0x8b4
#define R_0x8b8 0x8b8
#define R_0x8c0 0x8c0
#define R_0x8c4 0x8c4
#define R_0x8c8 0x8c8
#define R_0x8cc 0x8cc
#define R_0x8d0 0x8d0
#define R_0x8d4 0x8d4
#define R_0x8d8 0x8d8
#define R_0x8dc 0x8dc
#define R_0x8f0 0x8f0
#define R_0x8f8 0x8f8
#define R_0x8fc 0x8fc
#define R_0x900 0x900
#define R_0x908 0x908
#define R_0x90c 0x90c
#define R_0x910 0x910
#define R_0x914 0x914
#define R_0x918 0x918
#define R_0x91c 0x91c
#define R_0x920 0x920
#define R_0x924 0x924
#define R_0x92c 0x92c
#define R_0x930 0x930
#define R_0x934 0x934
#define R_0x938 0x938
#define R_0x93c 0x93c
#define R_0x940 0x940
#define R_0x944 0x944
#define R_0x948 0x948
#define R_0x94c 0x94c
#define R_0x950 0x950
#define R_0x954 0x954
#define R_0x958 0x958
#define R_0x95c 0x95c
#define R_0x960 0x960
#define R_0x964 0x964
#define R_0x968 0x968
#define R_0x970 0x970
#define R_0x974 0x974
#define R_0x978 0x978
#define R_0x97c 0x97c
#define R_0x988 0x988
#define R_0x98c 0x98c
#define R_0x990 0x990
#define R_0x994 0x994
#define R_0x998 0x998
#define R_0x99c 0x99c
#define R_0x9a0 0x9a0
#define R_0x9a4 0x9a4
#define R_0x9ac 0x9ac
#define R_0x9b0 0x9b0
#define R_0x9b4 0x9b4
#define R_0x9b8 0x9b8
#define R_0x9cc 0x9cc
#define R_0x9d0 0x9d0
#define R_0x9e4 0x9e4
#define R_0x9e8 0x9e8
#define R_0x9f0 0x9f0
#define R_0xa0 0xa0
#define R_0xa00 0xa00
#define R_0xa04 0xa04
#define R_0xa08 0xa08
#define R_0xa0a 0xa0a
#define R_0xa0c 0xa0c
#define R_0xa10 0xa10
#define R_0xa14 0xa14
#define R_0xa20 0xa20
#define R_0xa24 0xa24
#define R_0xa28 0xa28
#define R_0xa2c 0xa2c
#define R_0xa40 0xa40
#define R_0xa44 0xa44
#define R_0xa48 0xa48
#define R_0xa4c 0xa4c
#define R_0xa50 0xa50
#define R_0xa54 0xa54
#define R_0xa58 0xa58
#define R_0xa68 0xa68
#define R_0xa6c 0xa6c
#define R_0xa70 0xa70
#define R_0xa74 0xa74
#define R_0xa78 0xa78
#define R_0xa8 0xa8
#define R_0xa80 0xa80
#define R_0xa84 0xa84
#define R_0xa98 0xa98
#define R_0xa9c 0xa9c
#define R_0xaa8 0xaa8
#define R_0xaac 0xaac
#define R_0xab4 0xab4
#define R_0xabc 0xabc
#define R_0xac8 0xac8
#define R_0xacc 0xacc
#define R_0xad0 0xad0
#define R_0xb0 0xb0
#define R_0xb00 0xb00
#define R_0xb04 0xb04
#define R_0xb07 0xb07
#define R_0xb08 0xb08
#define R_0xb0c 0xb0c
#define R_0xb10 0xb10
#define R_0xb14 0xb14
#define R_0xb18 0xb18
#define R_0xb1c 0xb1c
#define R_0xb20 0xb20
#define R_0xb24 0xb24
#define R_0xb28 0xb28
#define R_0xb2a 0xb2a
#define R_0xb2b 0xb2b
#define R_0xb2c 0xb2c
#define R_0xb30 0xb30
#define R_0xb34 0xb34
#define R_0xb38 0xb38
#define R_0xb3b 0xb3b
#define R_0xb3c 0xb3c
#define R_0xb40 0xb40
#define R_0xb44 0xb44
#define R_0xb48 0xb48
#define R_0xb54 0xb54
#define R_0xb58 0xb58
#define R_0xb60 0xb60
#define R_0xb64 0xb64
#define R_0xb68 0xb68
#define R_0xb6a 0xb6a
#define R_0xb6b 0xb6b
#define R_0xb6c 0xb6c
#define R_0xb6e 0xb6e
#define R_0xb70 0xb70
#define R_0xb74 0xb74
#define R_0xb77 0xb77
#define R_0xb78 0xb78
#define R_0xb7c 0xb7c
#define R_0xb80 0xb80
#define R_0xb84 0xb84
#define R_0xb88 0xb88
#define R_0xb8c 0xb8c
#define R_0xb90 0xb90
#define R_0xb94 0xb94
#define R_0xb98 0xb98
#define R_0xb9b 0xb9b
#define R_0xb9c 0xb9c
#define R_0xba0 0xba0
#define R_0xba4 0xba4
#define R_0xba8 0xba8
#define R_0xbac 0xbac
#define R_0xbad 0xbad
#define R_0xbc0 0xbc0
#define R_0xbc4 0xbc4
#define R_0xbc8 0xbc8
#define R_0xbcc 0xbcc
#define R_0xbd8 0xbd8
#define R_0xbdc 0xbdc
#define R_0xbe0 0xbe0
#define R_0xbe4 0xbe4
#define R_0xbe8 0xbe8
#define R_0xbec 0xbec
#define R_0xbf0 0xbf0
#define R_0xbf4 0xbf4
#define R_0xbf8 0xbf8
#define R_0xc00 0xc00
#define R_0xc04 0xc04
#define R_0xc08 0xc08
#define R_0xc0c 0xc0c
#define R_0xc10 0xc10
#define R_0xc14 0xc14
#define R_0xc18 0xc18
#define R_0xc1c 0xc1c
#define R_0xc20 0xc20
#define R_0xc24 0xc24
#define R_0xc2c 0xc2c
#define R_0xc30 0xc30
#define R_0xc38 0xc38
#define R_0xc3c 0xc3c
#define R_0xc40 0xc40
#define R_0xc44 0xc44
#define R_0xc4c 0xc4c
#define R_0xc50 0xc50
#define R_0xc54 0xc54
#define R_0xc58 0xc58
#define R_0xc5c 0xc5c
#define R_0xc6c 0xc6c
#define R_0xc70 0xc70
#define R_0xc74 0xc74
#define R_0xc78 0xc78
#define R_0xc7c 0xc7c
#define R_0xc80 0xc80
#define R_0xc84 0xc84
#define R_0xc88 0xc88
#define R_0xc8c 0xc8c
#define R_0xc90 0xc90
#define R_0xc94 0xc94
#define R_0xc9c 0xc9c
#define R_0xca0 0xca0
#define R_0xca4 0xca4
#define R_0xca8 0xca8
#define R_0xcac 0xcac
#define R_0xcb0 0xcb0
#define R_0xcb4 0xcb4
#define R_0xcb8 0xcb8
#define R_0xcbc 0xcbc
#define R_0xcbd 0xcbd
#define R_0xcbe 0xcbe
#define R_0xcc0 0xcc0
#define R_0xcc4 0xcc4
#define R_0xcc8 0xcc8
#define R_0xccc 0xccc
#define R_0xcd0 0xcd0
#define R_0xcd4 0xcd4
#define R_0xcd8 0xcd8
#define R_0xce0 0xce0
#define R_0xce4 0xce4
#define R_0xce8 0xce8
#define R_0xd00 0xd00
#define R_0xd04 0xd04
#define R_0xd08 0xd08
#define R_0xd0c 0xd0c
#define R_0xd10 0xd10
#define R_0xd14 0xd14
#define R_0xd2c 0xd2c
#define R_0xd30 0xd30
#define R_0xd40 0xd40
#define R_0xd44 0xd44
#define R_0xd48 0xd48
#define R_0xd4c 0xd4c
#define R_0xd50 0xd50
#define R_0xd54 0xd54
#define R_0xd5c 0xd5c
#define R_0xd6c 0xd6c
#define R_0xd7c 0xd7c
#define R_0xd80 0xd80
#define R_0xd84 0xd84
#define R_0xd8c 0xd8c
#define R_0xd90 0xd90
#define R_0xd94 0xd94
#define R_0xdac 0xdac
#define R_0xdb0 0xdb0
#define R_0xdb4 0xdb4
#define R_0xdb8 0xdb8
#define R_0xdbc 0xdbc
#define R_0xdcc 0xdcc
#define R_0xdd0 0xdd0
#define R_0xdd4 0xdd4
#define R_0xdd8 0xdd8
#define R_0xde0 0xde0
#define R_0xdec 0xdec
#define R_0xdf4 0xdf4
#define R_0xe00 0xe00
#define R_0xe04 0xe04
#define R_0xe08 0xe08
#define R_0xe10 0xe10
#define R_0xe14 0xe14
#define R_0xe18 0xe18
#define R_0xe1c 0xe1c
#define R_0xe20 0xe20
#define R_0xe24 0xe24
#define R_0xe28 0xe28
#define R_0xe30 0xe30
#define R_0xe34 0xe34
#define R_0xe38 0xe38
#define R_0xe3c 0xe3c
#define R_0xe40 0xe40
#define R_0xe44 0xe44
#define R_0xe48 0xe48
#define R_0xe4c 0xe4c
#define R_0xe50 0xe50
#define R_0xe54 0xe54
#define R_0xe5c 0xe5c
#define R_0xe64 0xe64
#define R_0xe6c 0xe6c
#define R_0xe70 0xe70
#define R_0xe74 0xe74
#define R_0xe78 0xe78
#define R_0xe7c 0xe7c
#define R_0xe80 0xe80
#define R_0xe84 0xe84
#define R_0xe88 0xe88
#define R_0xe8c 0xe8c
#define R_0xe90 0xe90
#define R_0xe94 0xe94
#define R_0xe98 0xe98
#define R_0xe9c 0xe9c
#define R_0xea0 0xea0
#define R_0xea4 0xea4
#define R_0xea8 0xea8
#define R_0xeac 0xeac
#define R_0xeb0 0xeb0
#define R_0xeb4 0xeb4
#define R_0xeb8 0xeb8
#define R_0xebc 0xebc
#define R_0xec 0xec
#define R_0xec0 0xec0
#define R_0xec4 0xec4
#define R_0xec8 0xec8
#define R_0xecc 0xecc
#define R_0xed0 0xed0
#define R_0xed4 0xed4
#define R_0xed8 0xed8
#define R_0xedc 0xedc
#define R_0xee0 0xee0
#define R_0xee8 0xee8
#define R_0xeec 0xeec
#define R_0xf0 0xf0
#define R_0xf00 0xf00
#define R_0xf04 0xf04
#define R_0xf08 0xf08
#define R_0xf0c 0xf0c
#define R_0xf10 0xf10
#define R_0xf14 0xf14
#define R_0xf18 0xf18
#define R_0xf1c 0xf1c
#define R_0xf20 0xf20
#define R_0xf24 0xf24
#define R_0xf2c 0xf2c
#define R_0xf30 0xf30
#define R_0xf34 0xf34
#define R_0xf4 0xf4
#define R_0xf44 0xf44
#define R_0xf48 0xf48
#define R_0xf4c 0xf4c
#define R_0xf50 0xf50
#define R_0xf54 0xf54
#define R_0xf58 0xf58
#define R_0xf5c 0xf5c
#define R_0xf70 0xf70
#define R_0xf74 0xf74
#define R_0xf80 0xf80
#define R_0xf84 0xf84
#define R_0xf87 0xf87
#define R_0xf88 0xf88
#define R_0xf8c 0xf8c
#define R_0xf90 0xf90
#define R_0xf94 0xf94
#define R_0xf98 0xf98
#define R_0xf9c 0xf9c
#define R_0xfa0 0xfa0
#define R_0xfa4 0xfa4
#define R_0xfa8 0xfa8
#define R_0xfac 0xfac
#define R_0xfb0 0xfb0
#define R_0xfb4 0xfb4
#define R_0xfb8 0xfb8
#define R_0xfbc 0xfbc
#define R_0xfc0 0xfc0
#define R_0xfc4 0xfc4
#define R_0xfc8 0xfc8
#define R_0xfcc 0xfcc
#define R_0xfd0 0xfd0
#define R_0xff0 0xff0
#define RF_0x0 0x0
#define RF_0x00 0x00
#define RF_0x08 0x08
#define RF_0x0c 0x0c
#define RF_0x0d 0x0d
#define RF_0x1 0x1
#define RF_0x18 0x18
#define RF_0x19 0x19
#define RF_0x1a 0x1a
#define RF_0x1bf0 0x1bf0
#define RF_0x2 0x2
#define RF_0x3 0x3
#define RF_0x30 0x30
#define RF_0x31 0x31
#define RF_0x32 0x32
#define RF_0x33 0x33
#define RF_0x35 0x35
#define RF_0x3e 0x3e
#define RF_0x3f 0x3f
#define RF_0x4 0x4
#define RF_0x42 0x42
#define RF_0x43 0x43
#define RF_0x51 0x51
#define RF_0x52 0x52
#define RF_0x53 0x53
#define RF_0x54 0x54
#define RF_0x55 0x55
#define RF_0x56 0x56
#define RF_0x57 0x57
#define RF_0x58 0x58
#define RF_0x5c 0x5c
#define RF_0x5d 0x5d
#define RF_0x60 0x60
#define RF_0x61 0x61
#define RF_0x63 0x63
#define RF_0x64 0x64
#define RF_0x65 0x65
#define RF_0x66 0x66
#define RF_0x67 0x67
#define RF_0x6e 0x6e
#define RF_0x6f 0x6f
#define RF_0x75 0x75
#define RF_0x76 0x76
#define RF_0x78 0x78
#define RF_0x7c 0x7c
#define RF_0x7f 0x7f
#define RF_0x8 0x8
#define RF_0x80 0x80
#define RF_0x81 0x81
#define RF_0x82 0x82
#define RF_0x83 0x83
#define RF_0x85 0x85
#define RF_0x86 0x86
#define RF_0x87 0x87
#define RF_0x8a 0x8a
#define RF_0x8b 0x8b
#define RF_0x8c 0x8c
#define RF_0x8d 0x8d
#define RF_0x8f 0x8f
#define RF_0x93 0x93
#define RF_0x9e 0x9e
#define RF_0xa3 0xa3
#define RF_0xa9 0xa9
#define RF_0xae 0xae
#define RF_0xb0 0xb0
#define RF_0xb3 0xb3
#define RF_0xb4 0xb4
#define RF_0xb8 0xb8
#define RF_0xbc 0xbc
#define RF_0xbe 0xbe
#define RF_0xc4 0xc4
#define RF_0xc8 0xc8
#define RF_0xc9 0xc9
#define RF_0xca 0xca
#define RF_0xcc 0xcc
#define RF_0xd 0xd
#define RF_0xdd 0xdd
#define RF_0xde 0xde
#define RF_0xdf 0xdf
#define RF_0xed 0xed
#define RF_0xee 0xee
#define RF_0xef 0xef
#define RF_0xf5 0xf5
#define RF_0xf6 0xf6
