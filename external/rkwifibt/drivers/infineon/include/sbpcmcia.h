/*
 * BCM43XX Sonics SiliconBackplane PCMCIA core hardware definitions.
 *
 * Portions of this code are copyright (c) 2023 Cypress Semiconductor Corporation
 *
 * Copyright (C) 1999-2018, Broadcom Corporation
 *
 *      Unless you and Broadcom execute a separate written software license
 * agreement governing use of this software, this software is licensed to you
 * under the terms of the GNU General Public License version 2 (the "GPL"),
 * available at http://www.broadcom.com/licenses/GPLv2.php, with the
 * following added to such license:
 *
 *      As a special exception, the copyright holders of this software give you
 * permission to link this software with independent modules, and to copy and
 * distribute the resulting executable under terms of your choice, provided that
 * you also meet, for each linked independent module, the terms and conditions of
 * the license of that module.  An independent module is a module which is not
 * derived from this software.  The special exception does not apply to any
 * modifications of the software.
 *
 *      Notwithstanding the above, under no circumstances may you combine this
 * software in any way with any other Broadcom software provided under a license
 * other than the GPL, without Broadcom's express prior written consent.
 *
 *
 * <<Broadcom-WL-IPTag/Open:>>
 *
 * $Id: sbpcmcia.h 647676 2016-07-07 02:59:05Z $
 */

#ifndef	_SBPCMCIA_H
#define	_SBPCMCIA_H

/* All the addresses that are offsets in attribute space are divided
 * by two to account for the fact that odd bytes are invalid in
 * attribute space and our read/write routines make the space appear
 * as if they didn't exist. Still we want to show the original numbers
 * as documented in the hnd_pcmcia core manual.
 */

/* PCMCIA Function Configuration Registers */
#define	PCMCIA_FCR		(0x700 / 2)

#define	FCR0_OFF		0
#define	FCR1_OFF		(0x40 / 2)
#define	FCR2_OFF		(0x80 / 2)
#define	FCR3_OFF		(0xc0 / 2)

#define	PCMCIA_FCR0		(0x700 / 2)
#define	PCMCIA_FCR1		(0x740 / 2)
#define	PCMCIA_FCR2		(0x780 / 2)
#define	PCMCIA_FCR3		(0x7c0 / 2)

/* Standard PCMCIA FCR registers */

#define	PCMCIA_COR		0

#define	COR_RST			0x80
#define	COR_LEV			0x40
#define	COR_IRQEN		0x04
#define	COR_BLREN		0x01
#define	COR_FUNEN		0x01

#define	PCICIA_FCSR		(2 / 2)
#define	PCICIA_PRR		(4 / 2)
#define	PCICIA_SCR		(6 / 2)
#define	PCICIA_ESR		(8 / 2)

#define PCM_MEMOFF		0x0000
#define F0_MEMOFF		0x1000
#define F1_MEMOFF		0x2000
#define F2_MEMOFF		0x3000
#define F3_MEMOFF		0x4000

/* Memory base in the function fcr's */
#define MEM_ADDR0		(0x728 / 2)
#define MEM_ADDR1		(0x72a / 2)
#define MEM_ADDR2		(0x72c / 2)

/* PCMCIA base plus Srom access in fcr0: */
#define PCMCIA_ADDR0		(0x072e / 2)
#define PCMCIA_ADDR1		(0x0730 / 2)
#define PCMCIA_ADDR2		(0x0732 / 2)

#define MEM_SEG			(0x0734 / 2)
#define SROM_CS			(0x0736 / 2)
#define SROM_DATAL		(0x0738 / 2)
#define SROM_DATAH		(0x073a / 2)
#define SROM_ADDRL		(0x073c / 2)
#define SROM_ADDRH		(0x073e / 2)
#define	SROM_INFO2		(0x0772 / 2)	/* Corerev >= 2 && <= 5 */
#define	SROM_INFO		(0x07be / 2)	/* Corerev >= 6 */

/*  Values for srom_cs: */
#define SROM_IDLE		0
#define SROM_WRITE		1
#define SROM_READ		2
#define SROM_WEN		4
#define SROM_WDS		7
#define SROM_DONE		8

/* Fields in srom_info: */
#define	SRI_SZ_MASK		0x03
#define	SRI_BLANK		0x04
#define	SRI_OTP			0x80

#define SROM16K_BANK_SEL_MASK		(3 << 11)
#define SROM16K_BANK_SHFT_MASK		11
#define SROM16K_ADDR_SEL_MASK	((1 << SROM16K_BANK_SHFT_MASK) - 1)
#define SROM_PRSNT_MASK		0x1
#define SROM_SUPPORT_SHIFT_MASK 30
#define SROM_SUPPORTED	(0x1 << SROM_SUPPORT_SHIFT_MASK)
#define SROM_SIZE_MASK    0x00000006
#define SROM_SIZE_2K	2
#define SROM_SIZE_512	1
#define SROM_SIZE_128	0
#define SROM_SIZE_SHFT_MASK  1

/* Standard tuples we know about */

#define CISTPL_NULL		0x00
#define	CISTPL_END		0xff		/* End of the CIS tuple chain */
#ifdef WL_DHD_XR
/* WL Non secure region starts = 12160 bit pos, 12160/8 = 0X5F0 */
#define CISTPL_OFFSET          0x5F0
#else
#define CISTPL_OFFSET           0xC0
#endif // endif

#define	CISTPL_BRCM_HNBU	0x80

#define HNBU_BOARDREV		0x02	/* One byte board revision */

#define HNBU_BOARDTYPE		0x1b	/* 2 bytes; boardtype */

#define HNBU_HNBUCIS		0x1d	/* what follows is proprietary HNBU CIS format */

#define HNBU_CUSTOM1		0x82	/* 4 byte; For non-BRCM post-mfg additions */
#define HNBU_CUSTOM2		0x83	/* Reserved; For non-BRCM post-mfg additions */

/* sbtmstatelow */
#define SBTML_INT_ACK		0x40000		/* ack the sb interrupt */
#define SBTML_INT_EN		0x20000		/* enable sb interrupt */

/* sbtmstatehigh */
#define SBTMH_INT_STATUS	0x40000		/* sb interrupt status */
#endif	/* _SBPCMCIA_H */
