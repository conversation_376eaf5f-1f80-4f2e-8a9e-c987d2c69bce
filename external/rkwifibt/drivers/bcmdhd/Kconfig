#
# Copyright (C) 2023, Broadcom.
#
#      Unless you and Broadcom execute a separate written software license
# agreement governing use of this software, this software is licensed to you
# under the terms of the GNU General Public License version 2 (the "GPL"),
# available at http://www.broadcom.com/licenses/GPLv2.php, with the
# following added to such license:
#
#      As a special exception, the copyright holders of this software give you
# permission to link this software with independent modules, and to copy and
# distribute the resulting executable under terms of your choice, provided that
# you also meet, for each linked independent module, the terms and conditions of
# the license of that module.  An independent module is a module which is not
# derived from this software.  The special exception does not apply to any
# modifications of the software.
#
#
# <<Broadcom-WL-IPTag/Open:>>
#

config BCMDHD
	tristate "Broadcom FullMAC wireless cards support"
	default m
	help
	  This module adds support for wireless adapters based on
	  Broadcom FullMAC chipset.

config BCMDHD_FW_PATH
	depends on BCMDHD
	string "Firmware path"
	default "/system/etc/firmware/fw_bcmdhd.bin"
	help
	  Path to the firmware file.

config BCMDHD_NVRAM_PATH
	depends on BCMDHD
	string "NVRAM path"
	default "/system/etc/firmware/nvram.txt"
	help
	  Path to the calibration file.

config BCMDHD_WEXT
	bool "Enable WEXT support"
	depends on BCMDHD && CFG80211 = n
	select WIRELESS_EXT
	select WEXT_PRIV
	help
	  Enables WEXT support

choice
	prompt "Enable Chip Interface"
	depends on BCMDHD
	help
		Enable Chip Interface.
config BCMDHD_SDIO
		bool "SDIO bus interface support"
		depends on BCMDHD && MMC
config BCMDHD_PCIE
		bool "PCIe bus interface support"
		depends on BCMDHD && PCI
config BCMDHD_USB
		bool "USB bus interface support"
		depends on BCMDHD && USB
endchoice

choice
	depends on BCMDHD && BCMDHD_SDIO
	prompt "Interrupt type"
	help
		Interrupt type
config BCMDHD_OOB
	depends on BCMDHD && BCMDHD_SDIO
	bool "Out-of-Band Interrupt"
	help
		Interrupt from WL_HOST_WAKE.
config BCMDHD_SDIO_IRQ
	depends on BCMDHD && BCMDHD_SDIO
	bool "In-Band Interrupt"
	help
	  Interrupt from SDIO DAT[1]
endchoice
